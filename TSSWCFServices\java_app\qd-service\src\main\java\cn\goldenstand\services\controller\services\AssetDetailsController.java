/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.controller.services;


import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.constant.Status;
import cn.goldenstand.services.mapper.AssetDetailsMapper;
import cn.goldenstand.services.common.GroupIdUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Services/AssetDetails")
public class AssetDetailsController {

    @Resource
    AssetDetailsMapper assetDetailsMapper;

    /**
     *: 获取资产明细
     * @param trustId 产品ID
     * @param pageCurrent 当前页面
     * @param pageSize 页面大小
     * @return 资产明细
     */
    @GetMapping("/viewAssetDetails")
    public ResultInfo viewAssetDetails(@RequestParam(value = "trustId", required = false) Integer trustId,
                                       @RequestParam(value = "pageCurrent") Long pageCurrent,
                                       @RequestParam(value = "pageSize") Long pageSize,
                                       @RequestParam(value = "orderBy") String orderBy) {
        Page<List<Map<String, Object>>> page = new Page<>();
        page.setSize(pageSize);
        page.setCurrent(pageCurrent);
        IPage<List<Map<String, Object>>> data = assetDetailsMapper.viewAssetDetails(page, trustId, orderBy);
        List<List<Map<String, Object>>> records = data.getRecords();

        for (int i = 0; i < records.size(); i++) {
            Map<String, Object> record = (Map<String, Object>) records.get(i);
            String accountNo = (String) record.get("AccountNo");
            String productNo = (String) record.get("ProductNo");
            if (accountNo != null) {
                record.put("AccountNoDisplay", GroupIdUtils.replaceStr(accountNo, productNo));
            }
        }
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, data);
    }
}
