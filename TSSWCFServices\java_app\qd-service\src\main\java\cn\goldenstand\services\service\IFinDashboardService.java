/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.service;

import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.entity.drb.BondBalanceDTO;
import cn.goldenstand.services.entity.drb.BondBalanceQueryDTO;
import cn.goldenstand.services.entity.drb.TrustMetricDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Map;

public interface IFinDashboardService {

    /**
     * 获取报告日期列表
     *
     * @return 报告日期列表
     */
    ResultInfo getReportingDates();

    /**
     * 获取券端余额信息
     *
     * @param page 分页参数
     * @param queryDTO 查询参数
     * @return 结果信息
     */
    ResultInfo getBondBalancePage(Page<BondBalanceDTO> page, BondBalanceQueryDTO queryDTO);

    /**
     * 根据报告日期获取各个层级剩余本金
     *
     * @param reportingDate 报告日期
     * @return 结果信息
     */
    ResultInfo getBalanceByReportingDate(String reportingDate);

    /**
     * 根据报告日期获取券端自持剩余本金
     *
     * @param reportingDate 报告日期
     * @return 结果信息
     */
    ResultInfo getSelfHoldingCpbByReportingDate(String reportingDate);

    /**
     * 根据报告日期获取融资项目数量总览
     *
     * @param reportingDate 报告日期
     * @return 结果信息
     */
    ResultInfo getTrustCountByReportingDate(String reportingDate);

    /**
     * 获取资产端剩余本金信息
     *
     * @param page 分页参数
     * @param queryDTO 查询参数
     * @return 结果信息
     */
    ResultInfo getAssetRemainingPrincipal(Page<Map<String, Object>> page, BondBalanceQueryDTO queryDTO);

    /**
     * 获取存续产品指标信息
     *
     * @param page 分页参数
     * @param queryDTO 查询参数
     * @return 结果信息
     */
    ResultInfo getTrustMetricPage(Page<TrustMetricDTO> page, BondBalanceQueryDTO queryDTO);
}
