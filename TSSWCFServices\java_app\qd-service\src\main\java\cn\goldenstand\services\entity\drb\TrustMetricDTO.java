/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.entity.drb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "存续产品指标数据")
public class TrustMetricDTO {

    @ApiModelProperty(value = "专项计划ID")
    private Long trustId;

    @ApiModelProperty(value = "专项计划代码")
    private String trustCode;

    @ApiModelProperty(value = "专项计划名称")
    private String trustName;

    @ApiModelProperty(value = "产品类型")
    private String productNo;

    @ApiModelProperty(value = "业务类别")
    private String category;

    @ApiModelProperty(value = "可变报酬占比(%)")
    private BigDecimal varCompRatio;

    @ApiModelProperty(value = "可变报酬占比(含超额)(%)")
    private BigDecimal varCompRatioInclExcess;

    @ApiModelProperty(value = "90+不良率(%)")
    private BigDecimal nplRate90dPlus;

    @ApiModelProperty(value = "APR(MOB15)(%)")
    private BigDecimal aprMob15;

    @ApiModelProperty(value = "IRR(%)")
    private BigDecimal irr;

    @ApiModelProperty(value = "实收利率(%)")
    private BigDecimal actualInterestRate;

    @ApiModelProperty(value = "后端收益(元)")
    private BigDecimal backendRevenue;

    @ApiModelProperty(value = "兑付缺口(元)")
    private BigDecimal redemptionGap;
}
