#coding=utf-8
from simulation_adjustments import get_prepayment_rate_factor, get_default_rate_factor3

MONTH_DAYS = 30

def func(params):
    params["StartOpeningBalance"] = params["PerformingLoanBalance"] + params["CumulativeDefault"]
    if params["LoanAmount"] == 0:
        params["LoanAmount"] = params["StartOpeningBalance"]

    curve_length = process_curves(params)
    params["TransitionCurveLength"] = curve_length

    payments, mob = simulate_cashflow(params)
    return payments, mob


def simulate_cashflow(params):
    # 首先获取已经存在的M1 ~ M6+
    m1 = m2 = m3 = m4 = m5 = m6 = m6_plus = float(0)

    if "ExistingMOB" in params.keys():
        m1, m2, m3, m4, m5, m6, m6_plus = params["ExistingMOB"]
        # convert to float
        m1 = float(m1)
        m2 = float(m2)
        m3 = float(m3)
        m4 = float(m4)
        m5 = float(m5)
        m6 = float(m6)
        m6_plus = float(m6_plus)

    if "SpecifiedGroupID" in params.keys() and params["SpecifiedGroupID"] is not None:
        print("已发生的逾期:")
        print("M1:", m1)
        print("M2:", m2)
        print("M3:", m3)
        print("M4:", m4)
        print("M5:", m5)
        print("M6:", m6)
        print("M6+:", m6_plus)
        print("已发生的总逾期:", m1 + m2 + m3 + m4 + m5 + m6 + m6_plus)

    # 模拟现金流
    payments, mob = simulate_payments(params, m1, m2, m3, m4, m5, m6, m6_plus)
    return payments, mob


def get_rate_position(seasoning, i):
    # ---- 注意从离线数据平台对接到 AssetPaymentStatus 的 seasoning 是月数而不是天数
    # 20241127: 从离线数据平台对接到 AssetPaymentStatus 的 seasoning 已改为是天数
    days = seasoning + i
    # months = days / MONTH_DAYS and round up
    months = days // MONTH_DAYS + (1 if days % MONTH_DAYS > 0 else 0)

    return months

def get_prepayment_rate_position(seasoning, i):
    # 如果 seasoning <= 30, 则令seasoning = 30
    if seasoning <= 15:
        days = seasoning + 5 + i
    else:
        days = seasoning + i

    # months = days / MONTH_DAYS and round up
    months = days // MONTH_DAYS + (1 if days % MONTH_DAYS > 0 else 0)

    # if seasoning <=15 and i <= 30:
    #     print("seasoning=", seasoning, ", i=", i, ", days=", days)
    #     print("匹配到的早偿率位置:", months)
    return months

def get_daily_stress_rates(default_curve, prepayment_curve, default_rate_position, prepayment_rate_position):

    monthly_default_rate = default_curve[default_rate_position - 1] if default_rate_position <= len(default_curve) else 0
    monthly_default_rate = float(monthly_default_rate)
    
    daily_default_rate = monthly_default_rate / MONTH_DAYS

    monthly_prepayment_rate = prepayment_curve[prepayment_rate_position - 1] if prepayment_rate_position <= len(prepayment_curve) else 0
    monthly_prepayment_rate = float(monthly_prepayment_rate)

    daily_prepayment_rate = monthly_prepayment_rate / MONTH_DAYS

    return round(daily_default_rate, 8), round(daily_prepayment_rate, 8)

def get_default_rate_adjustment(seasoning_days, existing_default_amount, loan_amount, default_curve, factor):
    # 调整系数 = 原调整系数 * (1 - 实际发生逾期率 / 系数调整后违约率)
    # 实际发生逾期率 = 该组实际逾期金额 / 合同金额
    # 系数调整后违约率 = SUM(该组匹配M1违约率曲线) * 调整系数
    # default_curve is a list of str, convert to a list of float
    existing_default_rate = existing_default_amount / loan_amount
    #print("实际发生逾期率=", existing_default_amount, "/", loan_amount, "=", existing_default_rate)

    default_curve = [float(i) for i in default_curve]

    seasoning_position = seasoning_days // MONTH_DAYS
    #print("账龄=", seasoning_days, ",对应曲线第", seasoning_position, "期, 原原违约率调整系数=", factor)
    if seasoning_position + 1 > len(default_curve):
        return factor

    #print("违约率曲线:", default_curve)
    # Calculate the remaining days in the current month
    remaining_days_in_month = 30 - (seasoning_days - seasoning_position * MONTH_DAYS)
    # Convert remaining days to a portion of the month
    current_month_portion = remaining_days_in_month / MONTH_DAYS
    current_default_rate = default_curve[seasoning_position]
    current_default_sum = current_default_rate * current_month_portion
    # get sum of default_curve from seasoning_position+1 to the end
    following_sum = sum(default_curve[seasoning_position+1:])
    total_sum = current_default_sum + following_sum

    #print("SUM(该组匹配M1违约率曲线) =", total_sum)

    adjusted_default_rate = total_sum * factor
    #print("系数调整后违约率=", total_sum, "*", factor, "=", adjusted_default_rate)

    if adjusted_default_rate == 0:
        return factor
    
    if existing_default_rate > adjusted_default_rate:
        return factor

    new_factor = factor * (1 - existing_default_rate / adjusted_default_rate)
    #print("调整系数=", factor, "*", "(1 -", existing_default_rate, "/", adjusted_default_rate, ")=", new_factor)

    return new_factor

DEFAULT_ADJUSTMENT_TRUSTS = [
    506, 110, 344, 153, 217, 495
]

def simulate_payments(params, m1, m2, m3, m4, m5, m6, m6_plus):
    trust_id = int(params["TrustID"])
    account_no = params["AccountNo"]

    new_m2 = new_m3 = new_m4 = new_m5 = new_m6 = new_m6_plus = float(0)

    default_curve = params["CDR"].split(",")
    prepayment_curve = params["CPR"].split(",")

    # 尝试对前两个早偿率取平均值
    # sum_of_first_2_prepayment_rates = float(prepayment_curve[0]) + float(prepayment_curve[1])
    # avg_of_first_2_prepayment_rates = sum_of_first_2_prepayment_rates / 2
    # prepayment_curve[0] = float(prepayment_curve[0]) * float(1.56)
    # prepayment_curve[1] = float(prepayment_curve[1]) * float(0.68)

    #print("prepayment_curve:", prepayment_curve)

    # if account_no == '1_2_3_3_9':
    #     #print("default_curve:", default_curve)
    #     #print("prepayment_curve:", prepayment_curve)

    amortisation_curve = params["AmortisationCurve"] # amortisation_curve.append((day_seq_no, end_date, principal, interest, opening_balance, principal_percent, interest_rate))

    performing_loan_balance = float(params["PerformingLoanBalance"])
    cumulative_default = float(params["CumulativeDefault"])
    cumulative_prepayment = float(0)
    opening_balance = performing_loan_balance + cumulative_default

    default_rate_base = float(params["LoanAmount"])
    seasoning = params["Seasoning"]
    seasoning_at_purchase = params["SeasoningAtPurchase"] if "SeasoningAtPurchase" in params else seasoning

    seasoning_month = seasoning // MONTH_DAYS
    
    # if trust_id in DEFAULT_ADJUSTMENT_TRUSTS:
    seasoning_month = seasoning_at_purchase // MONTH_DAYS
    #print("使用加权入池时账龄", seasoning_at_purchase, "转换为月份", seasoning_month)

    default_rate_factor = get_default_rate_factor3(account_no, seasoning_month)
    #print("账龄调整后: default_rate_factor=", default_rate_factor)

    # if cumulative_default > 0:
    #     if trust_id in DEFAULT_ADJUSTMENT_TRUSTS:
    #         print("已使用入池时账龄，不对违约率系数做进一步调整")
    #     else:
    #         print("----------------------------------------------------------------------")
    #         print("对", account_no, "的违约率系数", default_rate_factor, "做进一步调整")
    #         default_rate_factor = get_default_rate_adjustment(seasoning_at_purchase, cumulative_default, default_rate_base, default_curve, default_rate_factor)
    # else:
    #     print(account_no, "无逾期，不作调整")

    #print("account_no=", account_no, ", seasoning=", seasoning, ", seasoning_month=", seasoning_month, ", default_rate_factor=", default_rate_factor)
    
    m1_transition_curve = params["M1Transition"]
    m2_transition_curve = params["M2Transition"]
    m3_transition_curve = params["M3Transition"]
    m4_transition_curve = params["M4Transition"]
    m5_transition_curve = params["M5Transition"]
    m6_transition_curve = params["M6Transition"]
    #m6_transition_rate = float(1.0)

    len_m1_transition_curve = len(m1_transition_curve)
    len_m2_transition_curve = len(m2_transition_curve)
    len_m3_transition_curve = len(m3_transition_curve)
    len_m4_transition_curve = len(m4_transition_curve)
    len_m5_transition_curve = len(m5_transition_curve)
    len_m6_transition_curve = len(m6_transition_curve)

    existing_m1, existing_m2, existing_m3, existing_m4, existing_m5, existing_m6 = m1, m2, m3, m4, m5, m6

    # 当前的逾期情况作为现状计入MOB跟踪表
    # columns=['AccountNo', 'PeriodId', 'M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus']
    mob = []
    mob.append((account_no, 0, performing_loan_balance, m1, m2, m3, m4, m5, m6, m6_plus))

    # 当前的逾期情况也作为第0日的新增计入迁移表
    transitions = []
    transitions.append((0, m1, m2, m3, m4, m5, m6, m6_plus))

    # columns=['AccountNo', 'PeriodId', 'Principal', 'Interest', 'Prepayment', 'DefaultPrincipal', 'RecoveryPrincipal', 'CumulativeDefault', 'CumulativePrepayment', 'OpeningBalance', 'DefaultRateBase', 'PrepaymentRateBase', 'LoanAmount']
    payments = []
    #payments.append((account_no, 0, 0, 0, 0, 0, 0, 0, 0, opening_balance, 0, 0, 0))

    # 迁移曲线剩余长度 与 现金流长度 较大者作为模拟天数，这是考虑到迁移曲线剩余长度可能比现金流长，需要继续回收
    transition_curve_total_days = params["TransitionCurveLength"] * MONTH_DAYS
    cashflow_remaining_term = params["RemainingTerm"]
    transition_curve_remaining_days = transition_curve_total_days - seasoning
    simulation_days = max(transition_curve_remaining_days, cashflow_remaining_term)
    #print("账龄=", seasoning, ", 迁移曲线总天数=", transition_curve_total_days, ", 迁移曲线剩余天数=", transition_curve_remaining_days, ", 还款计划剩余天数=", cashflow_remaining_term, ", 模拟天数=", simulation_days)

    ERROR_ALLOWED = 1.0

    prepayment_rate_base = performing_loan_balance

    i = 0

    # 逐日模拟
    # 当 i > cashflow_remaining_term 时，表示还款计划已经结束，之后的每日回收都是现金回收。如果 m1 + m2 + m3 + m4 + m5 + m6 接近0，则已无继续模拟的必要
    for i in range(1, simulation_days + 1):

        rate_position = get_rate_position(seasoning, i)

        prepayment_rate_position = rate_position

        daily_default_rate, daily_prepayment_rate = get_daily_stress_rates(default_curve, prepayment_curve, rate_position, prepayment_rate_position)

        prepayment_rate_factor = get_prepayment_rate_factor(account_no, prepayment_rate_position)
        if prepayment_rate_position <= 3:
            #print("account_no=", account_no, ", prepayment_rate_position=", prepayment_rate_position, ", prepayment_rate_factor=", prepayment_rate_factor)
            daily_prepayment_rate = daily_prepayment_rate * prepayment_rate_factor

        daily_default_rate = daily_default_rate * default_rate_factor

        print("账龄:", seasoning, ", 第", i, "日, 早偿率位置:", prepayment_rate_position, ", 本日早偿率:", daily_prepayment_rate)

        # amortisation_curve columns: AccountNo, SeqNo, EndDate, ScheduledPrincipal, ScheduledInterest, OpeningBalance, PrincipalPercent, InterestRate
        if i <= len(amortisation_curve):
            interest_rate = float(amortisation_curve[i - 1][7])
            principal_percentage = float(amortisation_curve[i - 1][6])
        else:
            interest_rate = float(0)
            # 当日期超过还款计划后，就没有本金摊还比例的概念了，并且performing_loan_balance必然降为0
            # 此后，每天的回收全部变为现金回收，即摊还比例为1
            principal_percentage = float(1)

        m1_transition_rate = m1_transition_curve[rate_position - 1] if len_m1_transition_curve >= rate_position else float(1.0)
        m2_transition_rate = m2_transition_curve[rate_position - 1] if len_m2_transition_curve >= rate_position else float(1.0)
        m3_transition_rate = m3_transition_curve[rate_position - 1] if len_m3_transition_curve >= rate_position else float(1.0)
        m4_transition_rate = m4_transition_curve[rate_position - 1] if len_m4_transition_curve >= rate_position else float(1.0)
        m5_transition_rate = m5_transition_curve[rate_position - 1] if len_m5_transition_curve >= rate_position else float(1.0)
        m6_transition_rate = m6_transition_curve[rate_position - 1] if len_m6_transition_curve >= rate_position else float(1.0)

        print("第", i, "日曲线位置:", rate_position, ", 本日违约率:", daily_default_rate, ", 本日早偿率:", daily_prepayment_rate, ", 本日本金摊还比率:", principal_percentage)
        #print("     还原到月违约率:", daily_default_rate * MONTH_DAYS, ", 月早偿率:", daily_prepayment_rate * MONTH_DAYS, ", 月本金摊还比率:", principal_percentage * MONTH_DAYS)

        #print("     本日5个迁移率:", m1_transition_rate, m2_transition_rate, m3_transition_rate, m4_transition_rate, m5_transition_rate)

        opening_balance = performing_loan_balance + cumulative_default
        #print("     opening_balance = ", performing_loan_balance, " + ", cumulative_default, " = ", opening_balance)

        if (i - 1) % MONTH_DAYS == 0:
            prepayment_rate_base = performing_loan_balance

        print("     新增早偿：", prepayment_rate_base, "*", daily_prepayment_rate, "=", prepayment_rate_base * daily_prepayment_rate)  
        prepayment_principal = prepayment_rate_base * daily_prepayment_rate
        prepayment_principal = min(prepayment_principal, performing_loan_balance)
        #print("     扣除新增早偿: performing_loan_balance = ", performing_loan_balance, "-", prepayment_principal, " = ", performing_loan_balance - prepayment_principal)

        performing_loan_balance = performing_loan_balance - prepayment_principal
        cumulative_prepayment += prepayment_principal

        #print("     新增违约：", default_rate_base, "*", daily_default_rate, "=", default_rate_base * daily_default_rate)
        default_principal = default_rate_base * daily_default_rate
        default_principal = min(default_principal, performing_loan_balance)

        #print("     扣除新增违约: performing_loan_balance = ", performing_loan_balance, "-", default_principal, " = ", performing_loan_balance - default_principal)
        performing_loan_balance = performing_loan_balance - default_principal

        new_m1 = default_principal
        # m1_transition_rate% of new_m1 will convert to new_m2 in MONTH_DAYS days
        # the rest will convert to m0 in MONTH_DAYS days

        m1 += new_m1

        new_m2 = new_m3 = new_m4 = new_m5 = new_m6 = new_m6_plus = float(0)

        recovery = float(0)

        # 前30天，从transitions表PeriodId=0处回收已存在的逾期，每天对其1/30进行迁移处理, 称之为transition_portion
        if i <= MONTH_DAYS:
            if existing_m1 > 0:
                transition_portion = existing_m1 / MONTH_DAYS
                new_m2 = round(transition_portion * m1_transition_rate, 4)
                m2 += new_m2
                m1 -= transition_portion
                recovery += transition_portion - new_m2

            if existing_m2 > 0:
                transition_portion = existing_m2 / MONTH_DAYS
                new_m3 = round(transition_portion * m2_transition_rate, 4)
                m3 += new_m3
                m2 -= transition_portion
                recovery += transition_portion - new_m3

            if existing_m3 > 0:
                transition_portion = existing_m3 / MONTH_DAYS
                new_m4 = round(transition_portion * m3_transition_rate, 4)
                m4 += new_m4
                m3 -= transition_portion
                recovery += transition_portion - new_m4

            if existing_m4 > 0:
                transition_portion = existing_m4 / MONTH_DAYS
                new_m5 = round(transition_portion * m4_transition_rate, 4)
                m5 += new_m5
                m4 -= transition_portion
                recovery += transition_portion - new_m5

            if existing_m5 > 0:
                transition_portion = existing_m5 / MONTH_DAYS
                new_m6 = round(transition_portion * m5_transition_rate, 4)
                m6 += new_m6
                m5 -= transition_portion
                recovery += transition_portion - new_m6

            if existing_m6 > 0:
                transition_portion = existing_m6 / MONTH_DAYS
                new_m6_plus = round(transition_portion * m6_transition_rate, 4)
                m6_plus += new_m6_plus
                m6 -= transition_portion
                recovery += transition_portion - new_m6_plus


        # look at 30 days ago and convert m1 to m2, m2 to m3, ..., m6 to m6_plus
        if i > MONTH_DAYS:
            thirty_days_ago = i - MONTH_DAYS
            # new_m1 at thirty_days_ago will convert to new_m2 today

            m1_thirty_days_ago = transitions[thirty_days_ago][1]
            new_m2 = m1_thirty_days_ago * m1_transition_rate
            ##print("m2 += ", new_m2, " = ", m2 + new_m2)
            m2 += new_m2
            # 30天前的新增M1将完全消失（或者变成今天的新增M2，或者回收掉回到M0）
            ##print("m1 -=", m1_thirty_days_ago, " = ", m1 - m1_thirty_days_ago)
            m1 -= m1_thirty_days_ago
            recovery += m1_thirty_days_ago * (1 - m1_transition_rate)
            #print("         从第", thirty_days_ago, "日新增的M1:", m1_thirty_days_ago, "中回收:", m1_thirty_days_ago * (1 - m1_transition_rate))

            m2_thirty_days_ago = transitions[thirty_days_ago][2]
            new_m3 = m2_thirty_days_ago * m2_transition_rate
            m3 += new_m3
            ##print("m2 -= ", m2_thirty_days_ago, " = ", m2 - m2_thirty_days_ago)
            m2 -= m2_thirty_days_ago
            recovery += m2_thirty_days_ago * (1 - m2_transition_rate)
            #print("         从第", thirty_days_ago, "日新增的M2:", m2_thirty_days_ago, "中回收:", m2_thirty_days_ago * (1 - m2_transition_rate))

            m3_thirty_days_ago = transitions[thirty_days_ago][3]
            new_m4 = m3_thirty_days_ago * m3_transition_rate
            m4 += new_m4
            m3 -= m3_thirty_days_ago
            recovery += m3_thirty_days_ago * (1 - m3_transition_rate)
            #print("         从第", thirty_days_ago, "日新增的M3:", m3_thirty_days_ago, "中回收:", m3_thirty_days_ago * (1 - m3_transition_rate))

            m4_thirty_days_ago = transitions[thirty_days_ago][4]
            new_m5 = m4_thirty_days_ago * m4_transition_rate
            m5 += new_m5
            m4 -= m4_thirty_days_ago
            recovery += m4_thirty_days_ago * (1 - m4_transition_rate)
            #print("         从第", thirty_days_ago, "日新增的M4:", m4_thirty_days_ago, "中回收:", m4_thirty_days_ago * (1 - m4_transition_rate))

            m5_thirty_days_ago = transitions[thirty_days_ago][5]
            new_m6 = m5_thirty_days_ago * m5_transition_rate
            m6 += new_m6
            m5 -= m5_thirty_days_ago
            recovery += m5_thirty_days_ago * (1 - m5_transition_rate)
            #print("         从第", thirty_days_ago, "日新增的M5:", m5_thirty_days_ago, "中回收:", m5_thirty_days_ago * (1 - m5_transition_rate))

            m6_thirty_days_ago = transitions[thirty_days_ago][6]
            new_m6_plus = m6_thirty_days_ago * m6_transition_rate
            m6_plus += new_m6_plus
            m6 -= m6_thirty_days_ago
            recovery += m6_thirty_days_ago * (1 - m6_transition_rate)
            #print("         从第", thirty_days_ago, "日新增的M6:", m6_thirty_days_ago, "中回收:", m6_thirty_days_ago * (1 - m6_transition_rate))

        #print("     加回新增回收: performing_loan_balance = ", performing_loan_balance, "+", recovery, " = ", performing_loan_balance + recovery)
        performing_loan_balance += recovery

        transitions.append((i, round(new_m1, 4), round(new_m2, 4), round(new_m3, 4), round(new_m4, 4), round(new_m5, 4), round(new_m6, 4), round(new_m6_plus, 4)))

        ##print("第", i, "日新增逾期表:")
        # print(transitions[i])

        ##print("第", i, "日MOB表:")
        # print(mob[i])

        cumulative_default = m1 + m2 + m3 + m4 + m5 + m6 + m6_plus

        # 完成了早偿、新增违约、迁移（回收），现在来计算正常还款本金和利息
        #print("     本金回收款 =", performing_loan_balance, "*", principal_percentage, " = ", performing_loan_balance * principal_percentage)
        normal_principal = performing_loan_balance * principal_percentage
        # 早偿部分也产生利息
        interest = (performing_loan_balance + prepayment_principal) * interest_rate

        payments.append((account_no, i, normal_principal, interest, prepayment_principal, default_principal, recovery, cumulative_default, cumulative_prepayment, opening_balance, default_rate_base, prepayment_rate_base, params["LoanAmount"], daily_prepayment_rate, daily_default_rate ))


        #print("     扣除正常本金：performing_loan_balance = ", performing_loan_balance, "-", normal_principal, " = ", performing_loan_balance - normal_principal)
        performing_loan_balance = performing_loan_balance - normal_principal

        mob.append((account_no, i, round(performing_loan_balance, 4), round(m1, 4), round(m2, 4), round(m3, 4), round(m4, 4), round(m5, 4), round(m6, 4), round(m6_plus, 4)))

        # 余额算法1: 正常余额 + 逾期余额
        #print("closing_balance_1 = ", performing_loan_balance, "+", cumulative_default, " = ", performing_loan_balance + cumulative_default)
        closing_balance_1 = performing_loan_balance + cumulative_default

        # 余额算法2: 开始余额 - 早偿本金 - 正常本金
        #print("closing_balance_2 = ", opening_balance, "-", prepayment_principal, "-", normal_principal, " = ", opening_balance - prepayment_principal - normal_principal)
        closing_balance_2 = opening_balance - prepayment_principal - normal_principal

        # #print("余额算法1(正常余额 + 逾期余额)", closing_balance_1, ", 余额算法2(开始余额 - 早偿本金 - 正常本金):", closing_balance_2)
        # #print("两者差值:", closing_balance_1 - closing_balance_2)

        if i > cashflow_remaining_term and m1 + m2 + m3 + m4 + m5 + m6 < 1:
            #print("现金流模拟结束于第", i, "日: m1:", m1, ", m2:", m2, ", m3:", m3, ", m4:", m4, ", m5:", m5, ", m6:", m6, ", m6+:", m6_plus)
            break

    # 完成“有迁移曲线或还款计划的现金流模拟”后，继续让逾期逐步迁移至M6+，直至M1,M2,M3,M4,M5,M6均为0
    # 逾期迁移的过程中，每日的逾期情况也要记录到MOB表中
    # FURHTER_TRANSITION_DAYS = 180
    # further_days = FURHTER_TRANSITION_DAYS
    if m1 + m2 + m3 + m4 + m5 + m6 > ERROR_ALLOWED:
        print("当前位置为", i, ", 继续迁移至M1 ~ M6均为0")

        new_m1 = 0

        while m1 + m2 + m3 + m4 + m5 + m6 > ERROR_ALLOWED:

            i += 1
            thirty_days_ago = i - MONTH_DAYS

            if len(transitions) < abs(thirty_days_ago):
                print("transitions表长度不足(", len(transitions), ")，无法回溯到第", thirty_days_ago, "日")
                break
            
            transitions_thirty_days_ago = transitions[thirty_days_ago]
            ##print("30日期前的迁移表:", transitions_thirty_days_ago)

            # new_m1 at thirty_days_ago will convert to new_m2 today
            m1_thirty_days_ago = transitions_thirty_days_ago[1]
            new_m2 = m1_thirty_days_ago
            m2 += new_m2
            ##print("m1 -= ", m1_thirty_days_ago, " = ", m1 - m1_thirty_days_ago)
            m1 -= m1_thirty_days_ago

            m2_thirty_days_ago = transitions_thirty_days_ago[2]
            new_m3 = m2_thirty_days_ago
            m3 += new_m3
            m2 -= m2_thirty_days_ago

            m3_thirty_days_ago = transitions_thirty_days_ago[3]
            new_m4 = m3_thirty_days_ago
            m4 += new_m4
            m3 -= m3_thirty_days_ago

            m4_thirty_days_ago = transitions_thirty_days_ago[4]
            new_m5 = m4_thirty_days_ago
            m5 += new_m5
            m4 -= m4_thirty_days_ago

            m5_thirty_days_ago = transitions_thirty_days_ago[5]
            new_m6 = m5_thirty_days_ago
            m6 += new_m6
            m5 -= m5_thirty_days_ago

            m6_thirty_days_ago = transitions_thirty_days_ago[6]
            new_m6_plus = m6_thirty_days_ago
            m6_plus += new_m6_plus
            m6 -= m6_thirty_days_ago

            #print("第", i, "日: m1:", m1, ", m2:", m2, ", m3:", m3, ", m4:", m4, ", m5:", m5, ", m6:", m6, ", m6+:", m6_plus)

            mob.append((account_no, i, 0, m1, m2, m3, m4, m5, m6, m6_plus))
            transitions.append((i, new_m1, new_m2, new_m3, new_m4, new_m5, new_m6, new_m6_plus))
            #payments.append((account_no, i, normal_principal, interest, prepayment_principal, default_principal, recovery, cumulative_default, cumulative_prepayment, opening_balance, default_rate_base, prepayment_rate_base, params["LoanAmount"] ))
            payments.append((account_no, i, 0, 0, 0, 0, 0, cumulative_default, cumulative_prepayment, cumulative_default, default_rate_base, prepayment_rate_base, params["LoanAmount"], 0, 0 ))

        print("模拟结束于第", i, "日: m1:", m1, ", m2:", m2, ", m3:", m3, ", m4:", m4, ", m5:", m5, ", m6:", m6, ", m6+:", m6_plus)

    return payments, mob

def process_curves(params):

    # params["M1Transition"] is a string like '0.000000,0.000000,0.188300,0.948200,0.949200,0.945200,0.942200,0.937600,0.933800,0.929200,0.927600,0.925100,0.922900,0.909500,0.894700,0.887900,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000,0.000000'
    # split it and convert to decimal and store the values in a list
    m1_transition = [float(x) for x in params["M1Transition"].split(',')]
    m2_transition = [float(x) for x in params["M2Transition"].split(',')]
    m3_transition = [float(x) for x in params["M3Transition"].split(',')]
    m4_transition = [float(x) for x in params["M4Transition"].split(',')]
    m5_transition = [float(x) for x in params["M5Transition"].split(',')]
    m6_transition = [float(x) for x in params["M6Transition"].split(',')]

    params["M1Transition"] = m1_transition
    params["M2Transition"] = m2_transition
    params["M3Transition"] = m3_transition
    params["M4Transition"] = m4_transition
    params["M5Transition"] = m5_transition
    params["M6Transition"] = m6_transition

    max_len = max(len(m1_transition), len(m2_transition), len(m3_transition), len(m4_transition), len(m5_transition), len(m6_transition))

    return max_len
