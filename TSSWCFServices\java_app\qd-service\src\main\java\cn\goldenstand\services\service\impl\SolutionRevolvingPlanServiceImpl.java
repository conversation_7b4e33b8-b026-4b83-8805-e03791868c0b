/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.service.impl;

import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.constant.Status;
import cn.goldenstand.services.entity.SolutionRevolvingPlan;
import cn.goldenstand.services.entity.drb.MobPriceSummaryDTO;
import cn.goldenstand.services.mapper.SolutionRevolvingPlanMapper;
import cn.goldenstand.services.service.ISolutionRevolvingPlanService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class SolutionRevolvingPlanServiceImpl 
extends ServiceImpl<SolutionRevolvingPlanMapper, SolutionRevolvingPlan> implements ISolutionRevolvingPlanService {

    @Resource
    SolutionRevolvingPlanMapper solutionRevolvingPlanMapper;

    /**
     * Description:循环购买设置列表
     */
    @Override
    public ResultInfo getSolutionRevolvingPool(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> structurePeriodsData =  solutionRevolvingPlanMapper.getStructurePeriodsData(trustId);
        List<Map<String, Object>> paymentTypeOptions =  solutionRevolvingPlanMapper.getPaymentTypeOptions();
        List<Map<String, Object>> templateOptions =  solutionRevolvingPlanMapper.getTemplateOptions();
        list.add(structurePeriodsData);
        list.add(paymentTypeOptions);
        list.add(templateOptions);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,list);
    }

    /**
     * Description:合格投资回报设置
     */
    @Override
    public ResultInfo getSolutionSettingCmb(String trustId) {
        List<Map<String, Object>> solutionSettingCmb =  solutionRevolvingPlanMapper.getSolutionSettingCmb(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,solutionSettingCmb);
    }

    /**
     * Description:清仓回购设置
     */
    @Override
    public ResultInfo getScenarios(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> conditionOptions = solutionRevolvingPlanMapper.getConditionOptions();
        List<Map<String, Object>> scenarios = solutionRevolvingPlanMapper.getScenarios(trustId);
        List<Map<String, Object>> containersOne = solutionRevolvingPlanMapper.getContainersOne(trustId);
        List<Map<String, Object>> containersTwo = solutionRevolvingPlanMapper.getContainersTwo(trustId);
        list.add(conditionOptions);
        list.add(scenarios);
        list.add(containersOne);
        list.add(containersTwo);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,list);
    }

    @Override
    public ResultInfo getExtSetting(String trustId) {
        List<Map<String, Object>> extSetting = solutionRevolvingPlanMapper.getExtSetting(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,extSetting);
    }

    @Override
    public ResultInfo getPortfolioPaymentSequence(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1 = new ArrayList<>();
        LinkedHashMap<String,Object> map = new LinkedHashMap<>();
        Boolean extSetting = solutionRevolvingPlanMapper.IsTopUpAvailable(trustId);
        if (extSetting) {
            map.put("TrustPeriod","RevolvingPeriod");
            map.put("PeriodName","循环期");
            list1.add(map);
        }
        map = new LinkedHashMap<>();
        map.put("TrustPeriod","AmotisationPeriod");
        map.put("PeriodName","摊还期");
        list1.add(map);
        list.add(list1);
        list1 = new ArrayList<>();

        map = new LinkedHashMap<>();
        map.put("ConditionCode","CumulativeDefaultRate");
        map.put("ConditionDesc","资产池累计损失率");
        list1.add(map);
        map = new LinkedHashMap<>();
        map.put("ConditionCode","M3DefaultRate");
        map.put("ConditionDesc","90+逾期率（M3）");
        list1.add(map);
        map = new LinkedHashMap<>();
        map.put("ConditionCode","PoolPrincipalBalance");
        map.put("ConditionDesc","资产池剩余本金规模");
        list1.add(map);
        map = new LinkedHashMap<>();
        map.put("ConditionCode","EquityTrancheBalance");
        map.put("ConditionDesc","次级债券本金余额");
        list1.add(map);
        list.add(list1);

        list1 = solutionRevolvingPlanMapper.getPaymentSequenceItems(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getEventItems(trustId);
        list.add(list1);

        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,list);
    }

    @Override
    public ResultInfo getBondPremiumSettings(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1;
        String tranche = solutionRevolvingPlanMapper.getTranche(trustId);
        if (tranche != null && tranche.indexOf(".") > 0) {
            list1 = solutionRevolvingPlanMapper.getBondNames(trustId);
        } else {
            list1 = solutionRevolvingPlanMapper.getBondNamesOther(trustId);
        }
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getStructurePremiumSettings(trustId);
        list.add(list1);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getTrustBondExtraBpSettings(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1 = solutionRevolvingPlanMapper.getTrustBondExtraBpSettings(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getStructurePremiumSettings(trustId);
        list.add(list1);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getFloatRateSettings(String trustId) {
        List<List<Map<String, Object>>> list = new ArrayList<>();
        List<Map<String, Object>> list1 = solutionRevolvingPlanMapper.getBondsFloatBPDatas(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getStructurePremiumSettings(trustId);
        list.add(list1);
        list1 = solutionRevolvingPlanMapper.getTrustFloatDatas(trustId);
        list.add(list1);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, list);
    }

    @Override
    public ResultInfo getSolutionSetting(String trustId) {
        List<Map<String, Object>> solutionSetting =  solutionRevolvingPlanMapper.getSolutionSetting(trustId);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message,solutionSetting);
    }

    @Override
    public ResultInfo getMobPriceSummary(Long trustId, String sessionId, Long scenarioId) {
        List<MobPriceSummaryDTO> mobPriceSummary =
                solutionRevolvingPlanMapper.getMobPriceSummary(trustId, sessionId, scenarioId);


        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, mobPriceSummary);
    }
}
