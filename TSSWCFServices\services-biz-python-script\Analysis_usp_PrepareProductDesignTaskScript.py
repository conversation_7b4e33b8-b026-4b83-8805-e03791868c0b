# coding=utf-8

import sys
import traceback
import urllib.parse
from decimal import Decimal
from itertools import product

from Analysis_usp_LoadBondExtraBPIntoCashflowModel import (
    func as Analysis_usp_LoadBondExtraBPIntoCashflowModel,
)
from Analysis_usp_LoadCashflowModelByExecutionMode import (
    func as Analysis_usp_LoadCashflowModelByExecutionMode,
)
from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from PythonFiles import RunBatchRecordHelper as rd
from PythonFiles import SQLParamsParser
from supporting_functions import get_default_asset_service_fee_rate
from usp_LoadBondPremiumIntoCashflowModel import (
    func as usp_LoadBondPremiumIntoCashflowModel,
)
from usp_LoadCallOptionIntoCashflowModel import (
    func as usp_LoadCallOptionIntoCashflowModel,
)
from usp_LoadInvestmentReturnIntoCashflowModel import (
    func as usp_LoadInvestmentReturnIntoCashflowModel,
)
from usp_LoadMiscInfoIntoCashflowModel import func as usp_LoadMiscInfoIntoCashflowModel
from usp_LoadMiscInfoIntoCashflowModelByExecutionMode import (
    func as usp_LoadMiscInfoIntoCashflowModelByExecutionMode,
)


def set_discount_rate(params):
    sql = '''
        select case c1.ItemValue when 'true' then '循环' else '静态' end as Revolving
        from Analysis_Trust as a
        left join TrustManagement_TrustExtension c1 on a.TrustId = c1.TrustId and c1.ItemCode = 'IsTopUpAvailable'
        where a.TrustId = @TrustID
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    revolving = MysqlAdapter.commonExecuteGetData(sql)[0][0]
    if revolving == '静态':
        return
    
    sql = '''
        select ItemValue
        from Analysis_SolutionExtension
        where ItemCode = 'CoverageRate' and SolutionID = @TrustID
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    result = MysqlAdapter.commonExecuteGetData(sql)
    if result is None or len(result) == 0:
        return
    coverage_rate = float(result[0][0])

    sql = '''
        select ifnull(DiscountRatePointValues, '') as DiscountRatePointValues
        from Analysis_ProductDesignSensitivitySettings
        where TrustID = @TrustID and AnalysisType = @AnalysisType and SensitivityMode='@SensitivityMode' and Type = 3
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    result = MysqlAdapter.commonExecuteGetData(sql)
    # 检查结果是否为空，避免索引错误
    if not result:
        discount_rate_point_values = []
    else:
        discount_rate_point_string = result[0][0] # 获取原始字符串
        # 分割字符串，并过滤掉空字符串
        # 使用列表推导式，只保留非空字符串
        discount_rate_point_values_str_list = [
            s.strip() for s in discount_rate_point_string.split(',') if s.strip()
        ]
        # 转换成float
        discount_rate_point_values = [float(i) for i in discount_rate_point_values_str_list]
    # 检查coverage_rate是否在discount_rate_point_values中，如果不在的话按顺序插入
    if coverage_rate not in discount_rate_point_values:
        discount_rate_point_values.append(coverage_rate)
        discount_rate_point_values.sort()
    # 转换成,分隔的字符串
    discount_rate_point_values = [str(i) for i in discount_rate_point_values]
    discount_rate_point_values = ','.join(discount_rate_point_values)

    sql = f'''
        update Analysis_ProductDesignSensitivitySettings
        set DiscountRatePointValues = '{discount_rate_point_values}'
        where TrustID = @TrustID and AnalysisType = @AnalysisType and SensitivityMode='@SensitivityMode' and Type in (1, 3)
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    MysqlAdapter.commonRunsql(sql)

def func(params):

    sql = """
       select count(0) from Analysis_ProductDesignSensitivitySettings where TrustID=@TrustID and AnalysisType=@AnalysisType and SensitivityMode='@SensitivityMode'
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    re = MysqlAdapter.commonExecuteGetData(sql)[0][0]
    if re == 0:
        return "维度区间数据未保存"
    
    set_discount_rate(params)

    # 获取维度设置
    sql = """
        select ServiceFeeRatePointValues, BondExtraBPPointValues, BondPremiumPointValues, AssetPoolRatePointValues, DisposalFeePointValues, DiscountRatePointValues, UseDaily
        from Analysis_ProductDesignSensitivitySettings
        where TrustID=@TrustID and AnalysisType=@AnalysisType and SensitivityMode='@SensitivityMode' and Type = 3
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    print(sql)
    res = MysqlAdapter.commonExecuteGetData(sql)
    params["PDim4Variable"] = res[0][0]
    params["PDim5Variable"] = res[0][1]
    params["PDim6Variable"] = res[0][2]
    params["PDim7Variable"] = res[0][3]
    params["PDim8Variable"] = res[0][4]
    params["PDim9Variable"] = res[0][5]
    params["UseDaily"] = res[0][6]

    print("PDim5Variable: ", params["PDim5Variable"])
    print("PDim6Variable: ", params["PDim5Variable"])

    sql = '''
        select ItemValue
        from Analysis_SolutionExtension
        where ItemCode = 'DiscountMethod' and SolutionID = @TrustID;
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    res = MysqlAdapter.commonExecuteGetData(sql)
    params["DiscountMethod"] = int(res[0][0]) if res and res[0][0] else 0

    sql = '''
        select ItemValue
        from TrustManagement_TrustExtension
        where ItemCode = 'IsTopUpAvailable' and TrustID = @TrustID;
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    res = MysqlAdapter.commonExecuteGetData(sql)
    params["TopUpAvailable"] = res[0][0] if res and res[0][0] else ''
    is_revolving = True if params["TopUpAvailable"] == 'true' else False

    sql = '''
        select ItemValue
        from Analysis_SolutionExtension
        where ItemCode = 'UseTermDiscountFactor' and SolutionID = @TrustID;
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    res = MysqlAdapter.commonExecuteGetData(sql)
    params["UseTermDiscountFactor"] = int(res[0][0]) if res and res[0][0] else 0

    params["NumOfServiceFeeRate"] = (
        len(params["PDim4Variable"].split(","))
        if params["PDim4Variable"] is not None
        else 0
    )
    params["NumOfBondExtraBP"] = (
        len(params["PDim5Variable"].split(","))
        if params["PDim5Variable"] is not None
        else 0
    )
    params["NumOfBondPremium"] = (
        len(params["PDim6Variable"].split(","))
        if params["PDim6Variable"] is not None
        else 0
    )
    params["NumOfAssetPoolRate"] = (
        len(params["PDim7Variable"].split(","))
        if params["PDim7Variable"] is not None
        else 0
    )
    params["NumOfDisposalFee"] = (
        len(params["PDim8Variable"].split(","))
        if params["PDim8Variable"] is not None
        else 0
    )
    params["NumOfDiscountRate"] = (
        len(params["PDim9Variable"].split(","))
        if params["PDim9Variable"] is not None
        else 0
    )
    # 如果是静态产品且设置了多个覆盖率，则默认只有一个
    if not is_revolving and params["NumOfDiscountRate"] > 1:
        params["NumOfDiscountRate"] = 1
    params["SettingType"] = 0

    adjust_dimension_numbers(params)

    sql = """
        select DefaultRatePointValues, PrepaymentRatePointValues
        from Analysis_SensitivityAnalysisMultipleSettings
        where TrustID = @TrustID and SensitivityMode = '@SensitivityMode' and Type = @SettingType;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    res = MysqlAdapter.commonExecuteGetData(sql)
    cdr_values = res[0][0]
    cpr_values = res[0][1]

    params["NumOfCDR"] = len(cdr_values.split(","))
    params["NumOfCPR"] = len(cpr_values.split(","))
    params["NumOfCRR"] = 1

    params["PDim1Variable"] = cdr_values
    params["PDim2Variable"] = cpr_values
    params["PDim3Variable"] = '0'

    sql = """
        select TrustCode from Analysis_Trust where TrustId = @TrustID;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["TrustCode"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]
    params["TaskCode"] = params["TrustCode"] + "_Cashflow_STBatch"

    sql = '''
    	select CodeDictionaryId from Task_CodeDictionary where CodeDictionaryCode = '@TaskCode' and CodeCategoryId = 4;
    '''
    sql = MysqlAdapter.prepareSql(sql, params)
    params["ProcessTaskCodeID"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select count(1)
        from TrustManagement_TrustPeriod
        where TrustId = @TrustID and TrustPeriodType = 'PaymentDate_CF';
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["vperiod"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select case when length(ItemValue) <> 10 then null else cast(ItemValue as date) end
	    from Analysis_SolutionExtension
        where SolutionID = @TrustID and ItemCode = 'SimulationStartDate'
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["SimulationStartDate"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select ifnull(min(PeriodId), 0) from (
            select ROW_NUMBER() over (partition by TrustId order by StartDate) - 1 as PeriodId, EndDate
            from TrustManagement_TrustPeriod where TrustId = @TrustID and TrustPeriodType = 'CollectionDate_NW'
        ) x where EndDate >= '@SimulationStartDate';
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["PeriodId"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select EndDate from (
            select ROW_NUMBER() over (partition by TrustId order by StartDate) - 1 as PeriodId, EndDate
            from TrustManagement_TrustPeriod where TrustId = @TrustID and TrustPeriodType = 'PaymentDate_CF'
        ) x where PeriodId = @PeriodId;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["ScheduledStartDate"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    Remaining = params["ExecutionMode"][0:1]

    if Remaining == "0":
        sql = """
            select min(PaymentPeriod)
            from (
                select EndDate,ROW_NUMBER() over (order by EndDate) as PaymentPeriod
                from TrustManagement_TrustPeriod
                where trustid=@TrustID and TrustPeriodType='PaymentDate_CF'
            ) x
            where EndDate>=@ScheduledStartDate;
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        StartPeriod = MysqlAdapter.commonExecuteGetData(sql)[0][0]
        params["vperiod"] = params["vperiod"] - StartPeriod + 1

    # 日循环方案数量
    params["NumOfDaily"] = 1

    if int(params["UseDaily"]) == 1:
        sql = """
            select count(distinct plan_id)
            from daily_rev_plans
            where trust_id = @TrustID and plan_id > 0;
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        res = MysqlAdapter.commonExecuteGetData(sql)
        params["NumOfDaily"] = res[0][0] if res and res[0][0] else 1

    sql = """
        insert into Task_SessionContext (SessionID, VariableName, VariableValue, VariableDataType, IsConstant, IsKey, KeyIndex)
        values
        ('@SessionId', 'Period', cast(@vperiod-1 as char(10)), 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfCDR', @NumOfCDR, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfCPR', @NumOfCPR, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfCRR', @NumOfCRR, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfBondExtraBP', @NumOfBondExtraBP, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfBondPremium', @NumOfBondPremium, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfAssetPoolRate', @NumOfAssetPoolRate, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfServiceFeeRate', @NumOfServiceFeeRate, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfDisposalFee', @NumOfDisposalFee, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfDiscountRate', @NumOfDiscountRate, 'varchar', 0, 1, 0),
        ('@SessionId', 'DiscountMethod', @DiscountMethod, 'varchar', 0, 1, 0),
        ('@SessionId', 'UseTermDiscountFactor', @UseTermDiscountFactor, 'varchar', 0, 1, 0),
        ('@SessionId', 'UseDaily', @UseDaily, 'varchar', 0, 1, 0),
        ('@SessionId', 'NumOfDaily', @NumOfDaily, 'varchar', 0, 1, 0)
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    print(sql)
    MysqlAdapter.commonRunsql(sql)
    Analysis_usp_LoadCashflowModelByExecutionMode(
        params={
            "TrustID": params["TrustID"],
            "TrustCode":params["TrustCode"],
            "ExecutionMode": params["ExecutionMode"],
            "ScheduledStartDate": params["ScheduledStartDate"],
            "ProcessTaskCodeID":params["ProcessTaskCodeID"]
        }
    )

    Analysis_usp_LoadBondExtraBPIntoCashflowModel(params={"TrustID": params["TrustID"],"ProcessTaskCodeID":params["ProcessTaskCodeID"]})

    usp_LoadBondPremiumIntoCashflowModel(params={"TrustID": params["TrustID"],"ProcessTaskCodeID":params["ProcessTaskCodeID"]})

    usp_LoadInvestmentReturnIntoCashflowModel(params={"TrustID": params["TrustID"],"ProcessTaskCodeID":params["ProcessTaskCodeID"]})

    usp_LoadMiscInfoIntoCashflowModel(params={"TrustID": params["TrustID"],"ProcessTaskCodeID":params["ProcessTaskCodeID"]})

    usp_LoadMiscInfoIntoCashflowModelByExecutionMode(
        params={
            "TrustID": params["TrustID"],
            "ExecutionMode": params["ExecutionMode"],
            "ScheduledStartDate": params["ScheduledStartDate"],
            "ProcessTaskCodeID":params["ProcessTaskCodeID"]
        }
    )

    usp_LoadCallOptionIntoCashflowModel(
        params={
            "TrustID": params["TrustID"],
            "ExecutionMode": params["ExecutionMode"],
            "ScheduledStartDate": params["ScheduledStartDate"],
            "ProcessTaskCodeID":params["ProcessTaskCodeID"]
        }
    )

    sql = """
        delete from Analysis_StressedCashflowArrearsDetails where SessionID = 'ProductDesign' and TrustID = @TrustID;
        delete from Analysis_StressedCashflowArrearsAggregation where SessionID = 'ProductDesign' and TrustID = @TrustID;
        delete from Analysis_StressedCashflowDetails where SessionID = 'ProductDesign' and TrustID = @TrustID;
        delete from Analysis_StressedCashflowTrustPeriodAggregation where SessionID = 'ProductDesign' and TrustID = @TrustID;
        delete from Analysis_DailyRevolving where SessionID = 'ProductDesign' and TrustID = @TrustID;
        delete from Analysis_SensitivityAnalysisScenario where TrustID = @TrustID and SessionID='ProductDesign';
        delete from Analysis_ProductDesignAnalysisResult where TrustID = @TrustID;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    MysqlAdapter.commonRunsql(sql)

    sql = """
        select ItemValue
	    from Analysis_SolutionExtension
        where SolutionID = @TrustID and ItemCode = 'CoverageRate';
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    res = MysqlAdapter.commonExecuteGetData(sql)
    generic_discount_rate = Decimal(res[0][0]) if res and res[0][0] else Decimal(100.0)

    # 如果没有设置服务费率区间，则使用产品结构设置中的服务费率
    default_service_fee_rate = get_default_asset_service_fee_rate(params["TrustID"])

    sql = """
        insert into Analysis_ProductDesignAnalysisResult (
            TrustID , CDR , CPR , CRR , ServiceFeeRate , Coupon , Premium , AssetPoolRate,DisposalFeeRate,DurationRate,PlanID
        )
        values
    """
    for cdr_scenario, cpr_scenario, crr_scenario, service_fee_scenario, bp_scenario, premium_scenario \
        , asset_pool_rate_scenario, disposal_fee_scenario, discount_rate_scenario, daily_scenario \
            in product(range(params["NumOfCDR"]), range(params["NumOfCPR"]), range(params["NumOfCRR"]) \
                , range(params["NumOfServiceFeeRate"]), range(params["NumOfBondExtraBP"]) \
                , range(params["NumOfBondPremium"]), range(params["NumOfAssetPoolRate"]) \
                , range(params["NumOfDisposalFee"]), range(params["NumOfDiscountRate"]), range(params["NumOfDaily"])):
        sql += inner_sql_joint(params, cdr_scenario, cpr_scenario, crr_scenario, service_fee_scenario, default_service_fee_rate
                        , bp_scenario, premium_scenario, asset_pool_rate_scenario
                        , disposal_fee_scenario, discount_rate_scenario, generic_discount_rate, daily_scenario, is_revolving)

    # change the last comma to a semicolon
    #print(sql)
    # find the last comma in sql
    last_comma = sql.rfind(",")
    sql = sql[:last_comma] + ";"
    sql = MysqlAdapter.prepareSql(sql, params)
    print(sql)
    MysqlAdapter.commonRunsql(sql)

    return ""

# Dim4 - Dim10: ServiceFeeRatePointValues, BondExtraBPPointValues, BondPremiumPointValues, AssetPoolRatePointValues, DisposalFeePointValues, DiscountRatePointValues, UseDaily
def inner_sql_joint(params, cdr_scenario, cpr_scenario, crr_scenario, service_fee_scenario, default_service_fee_rate,bp_scenario
        , premium_scenario, asset_pool_rate_scenario, disposal_fee_scenario, discount_rate_scenario
        , generic_discount_rate, daily_scenario, is_revolving):
    dim1 = params["PDim1Variable"].split(",")[cdr_scenario]
    dim2 = params["PDim2Variable"].split(",")[cpr_scenario]
    dim3 = params["PDim3Variable"].split(",")[crr_scenario]
    dim4 = (
        params["PDim4Variable"].split(",")[service_fee_scenario]
        if params["PDim4Variable"] is not None and params["PDim4Variable"] != ""
        else default_service_fee_rate
    )
    dim5 = (
        params["PDim5Variable"].split(",")[bp_scenario]
        if params["PDim5Variable"] is not None
        else 0
    )
    dim6 = (
        params["PDim6Variable"].split(",")[premium_scenario]
        if params["PDim6Variable"] is not None
        else 0
    )
    dim7 = (
        params["PDim7Variable"].split(",")[asset_pool_rate_scenario]
        if params["PDim7Variable"] is not None
        else 0
    )
    dim8 = (
        params["PDim8Variable"].split(",")[disposal_fee_scenario]
        if params["PDim8Variable"] is not None
        else 0
    )
    if is_revolving:
        dim9 = (
            params["PDim9Variable"].split(",")[discount_rate_scenario]
            if params["PDim9Variable"] is not None and params["PDim9Variable"] != ""
            else generic_discount_rate
        )
    else:
        dim9 = Decimal(100.0)

    if is_revolving:
        dim10 = daily_scenario + 1 if int(params["UseDaily"]) == 1 else 1
    else:
        dim10 = 0

    sql = f"""
        (@TrustID , {dim1}, {dim2}, {dim3}, {dim4}, {dim5}, {dim6}, {dim7}, {dim8}, {dim9}, {dim10}),
    """

    return sql


def adjust_dimension_numbers(params):
    if params["NumOfServiceFeeRate"] == 0:
        params["NumOfServiceFeeRate"] = 1
    if params["NumOfBondExtraBP"] == 0:
        params["NumOfBondExtraBP"] = 1
    if params["NumOfBondPremium"] == 0:
        params["NumOfBondPremium"] = 1
    if params["NumOfAssetPoolRate"] == 0:
        params["NumOfAssetPoolRate"] = 1
    if params["NumOfDisposalFee"] == 0:
        params["NumOfDisposalFee"] = 1
    if params["NumOfDiscountRate"] == 0:
        params["NumOfDiscountRate"] = 1
    if int(params["IsRegion"]) == 0:
        params["SettingType"] = 1
    else:
        params["SettingType"] = 3


if __name__ == "__main__":
    try:
        str_arg = """"""
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]

        arg = urllib.parse.unquote_plus(str_arg)
        params = SQLParamsParser.getParmas(arg)
        run_date = params["RunDate"]
        record = rd.RecordClass(run_date, 'ProductDesign')
        record.insert_record(params["TrustID"])
        try:
            record.update_record(params["TrustID"], 2, "开始运行", is_start=True)
            result = func(params)
            record.update_record(params["TrustID"], 2, "数据准备运行成功")
            print("$OUTPUT" + result)
        except Exception as e:
            error_info = traceback.format_exc()
            print('$ERROR', e, error_info)
            record.update_record(params["TrustID"], -1, error_info.strip().replace("'",'"').replace(";",''))
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
