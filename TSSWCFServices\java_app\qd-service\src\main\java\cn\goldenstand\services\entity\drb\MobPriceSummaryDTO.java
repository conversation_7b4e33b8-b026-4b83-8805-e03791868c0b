/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.entity.drb;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "回购价格数据汇总")
public class MobPriceSummaryDTO {

    @ApiModelProperty(value = "回购起算日")
    private String repurchaseCalculationDate;

    @ApiModelProperty(value = "资产分类")
    private String assetCategory;

    @ApiModelProperty(value = "回购起算日资产余额")
    private BigDecimal assetBalance;

    @ApiModelProperty(value = "回购价格")
    private BigDecimal repurchasePrice;

    @ApiModelProperty(value = "回购折价率(%)")
    private BigDecimal discountRate;
}
