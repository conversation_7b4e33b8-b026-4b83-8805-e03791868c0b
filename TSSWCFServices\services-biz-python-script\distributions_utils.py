
import pandas as pd
import random
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
random.seed(42)  # 使用固定的种子值

# Function to calculate the distribution of a given attribute
# ! 此函数不允许改动
def calculate_distribution(loans, attribute, buckets):
    # buckets: {(0.0, 1.0): 0.4, (1.0, 2.0): 0.6}
    # calculate the percentage of remaining_balance in each bucket
    total = loans["remaining_balance"].sum()
    distribution = {}
    for bucket in buckets.keys():
        left, right = bucket

        count = loans[
            (loans[attribute] > left) & (loans[attribute] <= right)
        ]["remaining_balance"].sum()
        distribution[bucket] = count / total

    return distribution

# 函数：处理分布数据
# ! 此函数不允许改动
def process_distribution_data(df, type):
    """
    从DataFrame中提取特定类型的分布数据
    
    Args:
        df: 包含分布数据的DataFrame
        type: 分布类型名称
    
    Returns:
        包含分布区间和对应比例的字典
    """
    df1 = df[df["distribution_name"] == type]

    buckets = {}
    for _, row in df1.iloc[:, [2, 3, 4]].dropna().iterrows():
        key = (row.iloc[0], row.iloc[1])  # 提取区间左右边界
        buckets[key] = row.iloc[2]  # 提取比例值

    return buckets

# ! 此函数不允许改动
def match_asset_group(
    df_asset_groups_interest_rates,
    grouping_configs,
    asset_category,
    asset_sub_category,
    loan_term,
    seasoning,
    credit_rating,
    asset_group_cache
):
    cache_key = (
        asset_category,
        asset_sub_category,
        loan_term,
        seasoning,
        credit_rating
    )
    if cache_key in asset_group_cache:
        return asset_group_cache[cache_key][0], asset_group_cache[cache_key][1]
    
    #print("asset_category=", asset_category, "asset_sub_category=", asset_sub_category, "loan_term=", loan_term, "seasoning=", seasoning, "credit_rating=", credit_rating)
    # first determine the group_id based on the asset_category, asset_sub_category, loan_term, seasoning, credit_rating, by looking up grouping_configs
    first_digit = asset_category
    second_digit = asset_sub_category

    credit_rating_config = grouping_configs[asset_category]["CreditRating"]
    '''
    "CreditRating": [
                    {"seq": 1, "left": 0, "right": 1},
                    {"seq": 2, "left": 1, "right": 2},
                    {"seq": 3, "left": 2, "right": 3}
                ]
    '''
    third_digit = next(
        (
            config["seq"]
            for config in credit_rating_config
            if config["left"] < credit_rating <= config["right"]
        ),
        None,
    )

    loan_term_config = grouping_configs[asset_category]["LoanTerm"]
    '''
    "LoanTerm": [
                    {"seq": 1, "left": 0, "right": 3},
                    {"seq": 2, "left": 3, "right": 6},
                    {"seq": 3, "left": 6, "right": 12}
                ]
    '''
    fourth_digit = next(
        (
            config["seq"]
            for config in loan_term_config
            if config["left"] < loan_term <= config["right"]
        ),
        None,
    )

    seasoning_config = grouping_configs[asset_category]["Seasoning"]
    '''
    "Seasoning": [
                    {"seq": 1, "left": -1, "right": 0},
                    {"seq": 2, "left": 0, "right": 1},
                    {"seq": 3, "left": 1, "right": 2},
                    {"seq": 4, "left": 2, "right": 3},
                    {"seq": 5, "left": 3, "right": 4},
                    {"seq": 6, "left": 4, "right": 5},
                    {"seq": 7, "left": 5, "right": 6},
                    {"seq": 8, "left": 6, "right": 12}
                ]
    '''
    fifth_digit = next(
        (
            config["seq"]
            for config in seasoning_config
            if config["left"] < seasoning <= config["right"]
        ),
        None,
    )

    if None in [third_digit, fourth_digit, fifth_digit]:
        print("未找到分组", asset_category, asset_sub_category, loan_term, seasoning, credit_rating)
        raise Exception("循环池资产未找到分组")
    
    # connect them using underscore to form group_id
    group_id = f"{first_digit}_{second_digit}_{third_digit}_{fourth_digit}_{fifth_digit}"

    # now look up interest_rate by looking up asset_category, asset_sub_category and loan_term in df_asset_groups_interest_rates
    '''
    asset_category	dimension1_right	dimension2_right	dimension3_right	interest_rate
    1	2	3	1	18.51809333
    1	2	6	2	19.92665238
    1	2	12	3	18.5613087
    2	2	12	4	16.43811538
    2	2	24	5	14.03544
    2	2	36	1	18
    '''
    matched = df_asset_groups_interest_rates[
        (df_asset_groups_interest_rates["asset_category"] == asset_category)
        & (df_asset_groups_interest_rates["dimension1_right"] == asset_sub_category)
        & (df_asset_groups_interest_rates["dimension2_right"] == credit_rating)
        & (df_asset_groups_interest_rates["dimension3_right"] == loan_term)
    ]

    if matched.empty:
        print("未找到利率", asset_category, asset_sub_category, credit_rating, loan_term)
        interest_rate = 0.00
    else:
        interest_rate = matched["interest_rate"].iloc[0]
        # to 2 decimal places
        interest_rate = round(interest_rate, 2)

    # Store the result in the cache
    asset_group_cache[cache_key] = (group_id, interest_rate)

    # if math.isnan(interest_rate):
    #     print("未获取到分组", group_id, "的利率")
    #     interest_rate = 0

    return group_id, interest_rate


# 函数：生成单个贷款的现金流
# ! 此函数不允许改动
def generate_cashflows_for_single_loan(
    index, group_id, remaining_balance, remaining_term, interest_rate, start_date, pool_date
):
    cashflows = []
    remaining_principal = remaining_balance
    monthly_payment = calculate_monthly_payment(remaining_balance, interest_rate, remaining_term)

    weighted_sum = 0
    total_interest = 0
    interest_days = (pool_date - start_date).days
    first_payment_date = None
    interest_value = None

    for month in range(1, remaining_term + 1):
        monthly_interest = remaining_principal * interest_rate / 12 / 100
        principal_paid = monthly_payment - monthly_interest
        payment_date = start_date + relativedelta(months=month)
        weighted_sum += month * principal_paid

        cashflows.append(
            {
                "loan_id": index,
                "group_id": group_id,
                "payment_date": payment_date,
                "total_payment": monthly_payment,
                "principal": principal_paid,
                "interest": monthly_interest
            }
        )

        remaining_principal -= principal_paid
        total_interest += monthly_interest

        if first_payment_date is None:
            first_payment_date = payment_date
            first_period_days = (first_payment_date - start_date).days
            interest_value = (
                1.0 * interest_days / first_period_days
            ) * monthly_interest

    remaining_term_days = (start_date + relativedelta(months=remaining_term) - start_date).days

    return cashflows, interest_value, remaining_term_days


# Function to generate a random date within the specified range
# ! 此函数不允许改动
def random_date(start_date, end_date):
    time_between_dates = end_date - start_date
    random_number_of_days = random.randrange(time_between_dates.days)
    return start_date + timedelta(days=random_number_of_days)

# Function to calculate monthly payment
# ! 此函数不允许改动
def calculate_monthly_payment(principal, annual_rate, term_months):
    if annual_rate == 0:
        return principal / term_months
    else:
        monthly_rate = annual_rate / 12 / 100
        # print("monthly_rate=", monthly_rate, "term_months=", term_months)
        return (
            principal
            * (monthly_rate * (1 + monthly_rate) ** term_months)
            / ((1 + monthly_rate) ** term_months - 1)
        )
