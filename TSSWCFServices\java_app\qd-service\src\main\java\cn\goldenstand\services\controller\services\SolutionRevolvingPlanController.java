/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.controller.services;


import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.mapper.SolutionRevolvingPlanMapper;
import cn.goldenstand.services.service.ISolutionRevolvingPlanService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/Services/solutionRevolvingPlan")
public class SolutionRevolvingPlanController {

    @Resource
    ISolutionRevolvingPlanService solutionRevolvingPlanService;
    @Resource
    SolutionRevolvingPlanMapper solutionRevolvingPlanMapper;

    /**
     * 获取循环购买设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("循环购买设置列表")
    @GetMapping("/getSolutionRevolvingPool")
    public ResultInfo getSolutionRevolvingPool(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getSolutionRevolvingPool(trustId);
    }

    /**
     * 获取合格投资回报设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("合格投资回报设置-1")
    @GetMapping("/getSolutionSettingCmb")
    public ResultInfo getSolutionSettingCmb(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getSolutionSettingCmb(trustId);
    }

    /**
     * 获取合格投资回报设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("合格投资回报设置-2")
    @GetMapping("/getSolutionSetting")
    public ResultInfo getSolutionSetting(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getSolutionSetting(trustId);
    }

    /**
     * 获取清仓回购设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("清仓回购设置")
    @GetMapping("/getScenarios")
    public ResultInfo getScenarios(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getScenarios(trustId);
    }

    /**
     * 获取ext设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("ext设置")
    @GetMapping("/getExtSetting")
    public ResultInfo getExtSetting(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getExtSetting(trustId);
    }

    /**
     * 获取触发事件设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("触发事件设置")
    @GetMapping("/getPortfolioPaymentSequence")
    public ResultInfo getPortfolioPaymentSequence(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getPortfolioPaymentSequence(trustId);
    }

    /**
     * 获取次级溢价发行设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("次级溢价发行设置")
    @GetMapping("/getBondPremiumSettings")
    public ResultInfo getBondPremiumSettings(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getBondPremiumSettings(trustId);
    }

    /**
     * 获取债券附加BP设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("债券附加BP设置")
    @GetMapping("/getTrustBondExtraBpSettings")
    public ResultInfo getTrustBondExtraBpSettings(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getTrustBondExtraBpSettings(trustId);
    }

    /**
     * 获取浮动利率设置
     *
     * @param trustId 产品代码
     * @return 循环购买设置
     */
    @ApiOperation("浮动利率设置")
    @GetMapping("/getFloatRateSettings")
    public ResultInfo getFloatRateSettings(@RequestParam(value = "trustId") String trustId) {
        return solutionRevolvingPlanService.getFloatRateSettings(trustId);
    }

    /**
     * 获取回购价格明细数据
     *
     * @param trustId 产品ID
     * @param sessionId 会话ID
     * @param scenarioId 情景ID
     * @return 回购价格明细数据
     */
    @ApiOperation("获取回购价格明细数据")
    @GetMapping("/getMobPriceSummary")
    public ResultInfo getMobPriceSummary(
            @ApiParam(value = "产品ID", required = true) @RequestParam(value = "trustId") Long trustId,
            @ApiParam(value = "会话ID", required = true) @RequestParam(value = "sessionId") String sessionId,
            @ApiParam(value = "情景ID", required = true) @RequestParam(value = "scenarioId") Long scenarioId) {
        return solutionRevolvingPlanService.getMobPriceSummary(trustId, sessionId, scenarioId);
    }
}
