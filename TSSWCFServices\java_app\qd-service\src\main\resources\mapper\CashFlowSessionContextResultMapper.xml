<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.goldenstand.services.mapper.CashFlowSessionContextResultMapper">
    <select id="getMaxPeriodsId" resultType="java.lang.Integer">
        select max(PeriodsId)
        from Task_SessionContextResult
        where SessionId = #{sessionId}
    </select>
    <select id="getQueryStoredProcedure" resultType="cn.goldenstand.services.entity.Period">
        select ItemName, ItemValue, PeriodsId
        from Task_SessionContextResult
        where SessionId = #{sessionId} and ItemName in ('兑付日','计算日')
        union
        select a.* from (
            select a.*
            from (
                select ItemName, max(ItemValue) as ItemValue, PeriodsId
                from Task_SessionContextResult b
                where SessionId = #{sessionId} and ItemName not in ('兑付日','计算日')
                group by ItemName, PeriodsId
            ) a
            left join (
                select min(SessionContextResultId) as orderNum, ItemName
                from Task_SessionContextResult
                where SessionId = #{sessionId}
                group by ItemName
            ) c on a.ItemName = c.ItemName
            order by c.orderNum, a.PeriodsId
        ) a;
    </select>
    <select id="getCodeDictionaryId" resultType="java.lang.Long">
        SELECT CodeDictionaryId
        from Task_CodeDictionary d
                 inner join Task_CodeCategory c
                            on d.CodeCategoryId = c.CodeCategoryId and c.CategoryCode = 'ProcessTaskType'
        where d.CodeDictionaryCode = #{processTaskCode};
    </select>
    <select id="getCashFlowRunResultBySessionIdSimplified"
            resultType="cn.goldenstand.services.entity.Period">
        select a.*
        from (select ItemName
                   , IF(locate('.', ItemValue) > 0 and ItemName not like '%率',
                        round(ItemValue, 2), ItemValue) as ItemValue
                   , PeriodsId
              From Task_SessionContextResult
              where SessionId = #{sessionId}
                and (
                          ItemName like '%累%早偿%' or
                          ItemName like '%累%违约%' or
                          ItemName like '%天数%' or
                          ItemName like '%触发%' or
                          ItemName like '%当期%金额%' or
                          ItemName like '%中登%' or
                          ItemName like '%调整后%' or
                          ItemName like '%合格投资%利息%' or
                          ItemName like '%合格投资%本金%' or
                          ItemName like '%%实付%' or
                          ItemName like '%当期利息分配%' or
                          ItemName like '%当期本金分配%' or
                          ItemName like '%期末本金余额%' or
                          ItemName like '%循环购买%回%' or
                          ItemName like '模拟循环购买额' or
                          ItemName = '计息期间循环购买额(如有)' or
                          ItemName like '%利息部分%' or
                          ItemName like '%本金部分%' or
                          ItemName like '%保费%' or
                          ItemName like '信托%账户%余额' or
                          ItemName like '其他合并入项' or
                          ItemName = '溢价发行收入' or
                          ItemName = '当期清仓回购款总额' or
                          ItemName = '期初闲置资金返还' or
                          ItemName = '信保基金返还' or
                          ItemName = '罚息收入' or
                          ItemName = '当期其他收入总额' or
                          ItemName like '%余额%' or
                          ItemName like '%当期合格投资收入总额%' or
                          ItemName like '%长款%'
                  )) a
                 left join (select min(SessionContextResultId) as orderNum, ItemName
                            from Task_SessionContextResult
                            where SessionId = #{sessionId}
                            group by ItemName) c on a.ItemName = c.ItemName
        order by c.orderNum, a.PeriodsId;
    </select>
    <select id="getCashFlowRunResultDatesBySessionIdOdl" resultType="java.util.Map">
        select PeriodsId               as PeriodId,
               cast(ItemValue as date) as pStartDate,
               cast(ItemValue as date) as pEndDate,
               ''                      as cStartDate,
               ''                      as cEndDate
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and ItemName = 'DateIndex'
    </select>
    <select id="selectTrustIdBySessionId" resultType="java.lang.String">
        select VariableValue
        from Task_SessionContext
        where SessionId = #{sessionId}
          and VariableName = 'TrustID'
        limit 1
    </select>
    <select id="selectIsTheSame" resultType="java.lang.Integer">
        SELECT count(*)
        FROM TrustManagement_TrustBond
        WHERE TrustID = #{trustId}
          and Itemcode = 'InterestStartDate'
        group by ItemValue
    </select>
    <select id="selectTrustBondByTrustId" resultType="java.lang.String">
        select ItemValue
        FROM TrustManagement_TrustBond
        WHERE TrustID = #{trustId}
          and ItemCode = 'InterestStartDate'
        group by ItemValue;
    </select>
    <select id="getCashFlowRunResultDatesBySessionId" resultType="java.util.Map">
        select -1                as PeriodId
             , #{trustStartDate} as pStartDate
             , #{trustStartDate} as pEndDate
             , 0                 as pDays
             , #{trustStartDate} as cStartDate
             , #{trustStartDate} as cEndDate
             , 0                 as cDays
        union
        select p.PeriodId
             , p.StartDate                      as pStartDate
             , p.EndDate                        as pEndDate
             , datediff(p.EndDate, p.StartDate) as pDays
             , c.StartDate                      as cStartDate
             , c.EndDate                        as cEndDate
             , datediff(c.EndDate, c.StartDate) as cDays
        from (
            select @rowNumber := @rowNumber + 1        as PeriodId
               , date_add(StartDate, interval 1 day) as StartDate
               , date_add(EndDate, interval 1 day)   as EndDate
            from TrustManagement_TrustPeriod,(Select (@rowNumber := -1)) b
            where TrustID = #{trustId} and TrustPeriodType = 'PaymentDate_CF'
        ) p
        left join (select @rowNum := @rowNum + 1              as PeriodId
             , date_add(StartDate, interval 1 day) as StartDate
             , date_add(EndDate, interval 1 day)   as EndDate
        from TrustManagement_TrustPeriod, (Select (@rowNum := -1)) b
        where TrustID = #{trustId} and TrustPeriodType = 'CollectionDate_NW') c
            on p.PeriodId = c.PeriodId
        order by pEndDate
    </select>
    <select id="getReconcileIndicatorsEx" resultType="java.util.Map">
        select distinct ItemName, 'Cashflow' as ReconcileCategory, 'In' as IsInOut, 'true' as ExpandData
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and (ItemName like '%利息部分%' or ItemName like '%本金部分%'
            or ItemName like '%循环购买%回%' or ItemName like '当期计划%'
            or ItemName = '信托储备账户期初余额' or ItemName = '上期长款结转'
            or ItemName like '其他合并入项' or ItemName = '当期清仓回购款总额'
            or ItemName = '期初闲置资金返还' or ItemName = '信保基金返还'
            or ItemName = '罚息收入' or ItemName = '当期其他收入总额'
            or ItemName = '本金分账户期初余额' or ItemName = '收入分账户期初余额'
            or ItemName = '当期合格投资收入总额')
          and ItemName not like '调整后当期%'
          and ItemName != '当期合格投资收入(利息部分)'
          and ItemName != '当期实际发生合格投资收入(利息部分)'
        union all
        select distinct ItemName, 'Cashflow' as ItemCategory, 'Out', 'true'
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and (ItemName like '%中登费%' or ItemName = '代理机构（长款）' or ItemName = '溢价发行收入'
            or (ItemName like '%实付%' and ItemName not like '%资金成本实付'
                and ItemName not like '%收入转本金账户补足累计违约额实付%')
        )
        union all
        select concat(ItemValue, '当期利息分配'), 'Cashflow', 'Out', 'true'
        from TrustManagement_TrustBond
        where TrustId = #{trustId} and ItemCode = 'ShortName'
        union all
        select concat(ItemValue, '当期本金分配'), 'Cashflow', 'Out', 'true'
        from TrustManagement_TrustBond
        where TrustId = #{trustId} and ItemCode = 'ShortName'
        union all
        select distinct ItemName, 'Cashflow' as ItemCategory, 'Out', 'true'
        from Task_SessionContextResult
        where SessionId = #{sessionId} and ItemName like '信托%账户期末余额'
        union all
        select distinct ItemName, 'LossRecovery' as ItemCategory, 'Out', 'true'
        from Task_SessionContextResult
        where SessionId = #{sessionId} and ItemName = '当期发生违约金额'
        union all
        select distinct ItemName, 'LossRecovery' as ItemCategory, 'In', 'true'
        from Task_SessionContextResult
        where SessionId = #{sessionId} and ItemName = '当期回收金额'
        union all
        select distinct ItemName, 'Others' as ItemCategory, 'In', 'true'
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and (ItemName = '模拟循环购买额' or ItemName like '%期末本金余额%')
        union all
        select distinct ItemName, 'Others' as ItemCategory, 'Out', 'true'
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and (ItemName like '%保费%')
          and (ItemName != '循环购买趸交保费')
    </select>
    <select id="selectIsExit" resultType="java.lang.Integer">
        select count(1)
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and ItemName like '%扣除保费后循环购买金额%'
    </select>
    <select id="getDistinct" resultType="java.util.Map">
        select concat(ItemValue, '期初本金余额') as ItemName
        from TrustManagement_TrustBond
        where TrustId = #{trustId}
          and ItemCode = 'ShortName'
        union all
        select concat(ItemValue, '期末本金余额') as ItemName
        from TrustManagement_TrustBond
        where TrustId = #{trustId}
          and ItemCode = 'ShortName'
        union all
        select distinct ItemName
        from Task_SessionContextResult
        where SessionId = #{sessionId}
          and ItemName like '%余额%'
          and ItemName not like '%期%本金余额%';
    </select>
    <select id="getPoolCloseDate" resultType="java.lang.String">
        select ItemValue
        from TrustManagement_TrustExtension
        where TrustId = #{trustId}
          and ItemCode = #{itemCode}
        limit 1
    </select>
    <select id="getRevolvingPurchaseDetailsFromSession" resultType="java.util.Map">
        select @rowNumber := @rowNumber + 1 as PeriodId, date_add(t.EndDate, interval 1 day) as EndDate
        from (select * from TrustManagement_TrustPeriod where TrustId = #{trustId} and TrustPeriodType = 'PaymentDate_CF' order by EndDate) t,
        (Select (@rowNumber := -1)) b
        union all
        select -1 PeriodId, #{poolCloseDate} EndDate
        order by PeriodId
    </select>
    <select id="getInterest" resultType="java.util.Map">
        select PeriodsId, sum(cast(ItemValue as decimal(19, 6))) as Amount
        from Task_SessionContextResult
        where SessionID = #{sessionId}
          and ItemName in ('循环购买利息回收款', '循环购买本金回流')
        group by PeriodsId;
    </select>
    <select id="getInterestOther" resultType="java.util.Map">
        select PeriodsId, cast(ItemValue as decimal(19, 6)) as Amount
        from Task_SessionContextResult
        where SessionID = #{sessionId}
          and ItemName = '循环购买本金回流';
    </select>
    <select id="getDetails" resultType="java.util.Map">
        with temp_OfferAmountWithPremium as (
            select tb.TrustBondId, c1.VariableValue
            from TrustManagement_TrustBond tb
            inner join Task_ProcessTaskContext c1
                on c1.ProcessTaskCodeId = (select cd.CodeDictionaryId
                       from Task_SessionContext sc
                       inner join Task_CodeDictionary cd on sc.VariableValue = cd.CodeDictionaryCode
                       where SessionId = #{sessionId}
                         and VariableName = 'TaskCode' and cd.CodeCategoryId = 4 limit 1)
                    and c1.VariableName like concat('%InitialInvestedAmount_%', tb.ItemValue, '_WithPremium')
            where tb.TrustId = (select VariableValue
                                from Task_SessionContext
                                where SessionId = #{sessionId} and VariableName = 'TrustID' limit 1)
                and tb.ItemCode = 'SecurityExchangeCode'
        ), tempA as (
            select ItemCode V, ItemValue T, TrustBondId
            FROM TrustManagement_TrustBond tb
            where trustid = (select VariableValue
                             from Task_SessionContext
                             where SessionId = #{sessionId} and VariableName = 'TrustID' limit 1)
              and itemcode in ('ShortName', 'SecurityExchangeCode', 'OfferAmount', 'IssueDate')
        )
        select V, ifnull(b.VariableValue, a.T) as T, a.TrustBondId
        from tempA a
        left join temp_OfferAmountWithPremium b on a.V = 'OfferAmount' and a.TrustBondId = b.TrustBondId;
    </select>
    <select id="selectScenarioIdBySessionId" resultType="java.lang.String">
        select VariableValue
        from Task_SessionContext
        where SessionId = #{sessionId}
          and VariableName = #{variableName}
        limit 1;
    </select>
    <select id="selectMaxEndDateBySessionId" resultType="java.lang.String">
        select max(Enddate)
        from Analysis_StressedCashflowTrustPeriodAggregation
        where trustid = #{trustId}
          and RevolvingPurchaseID = 0
          and IFNUll((Principal + Interest + Prepayment), 0) > 0
          and ScenarioID = #{scenarioId}
    </select>
    <select id="selectRowNumberBySessionId" resultType="java.lang.Integer">
        select count(*)
        from Analysis_StressedCashflowTrustPeriodAggregation
        where trustid = #{trustId}
          and RevolvingPurchaseID = 0
          and ifnull((Principal + Interest + Prepayment), 0) > 0
          and ScenarioID = #{scenarioId}
    </select>
    <select id="getTableOne" resultType="java.util.Map">
        select A.EndDate as Enddate, A.AmountCashed, A.OpeningBalance, IfNull(SR.ItemValue, 0) as ItemValue
        from (select distinct Enddate,
                              IfNull((Principal + Interest + Prepayment), 0) as AmountCashed,
                              OpeningBalance,
                              ROW_NUMBER() over (order by EndDate) - 1       as Periods
              from Analysis_StressedCashflowTrustPeriodAggregation
              where trustid = #{trustId}
                and RevolvingPurchaseID = 0
                and Enddate <![CDATA[ <= ]]> #{maxEndDate}
                and ScenarioID = #{scenarioId}) A
                 left join Task_SessionContextResult SR
                           on A.Periods = SR.PeriodsId and SR.SessionId = #{sessionId} and
                              SR.ItemName = '当期本息总分配'
        order by Enddate
    </select>
    <select id="getTableTwo" resultType="java.util.Map">
        select ItemName, cast(ItemValue as decimal(20, 2)) as ItemValue
        from Task_SessionContextResult
        where ItemName = '当期费用总支出'
          and sessionid = #{sessionId}
          and PeriodsId <![CDATA[ < ]]> #{rowNumber}
    </select>
    <select id="getTableThree" resultType="java.util.Map">
        select *
        from Task_SessionContextResult
        where ItemName = '资产池累计损失金额'
          and sessionid = #{sessionId}
        order by PeriodsId desc
        limit 1
    </select>
    <select id="getTableFour" resultType="java.util.Map">
        select *
        from Task_SessionContextResult
        where ItemName = '期末高收益档累计收益'
          and sessionid = #{sessionId}
        order by PeriodsId desc
        limit 1
    </select>
    <select id="getTableFive" resultType="java.util.Map">
        select sum(format(ItemValue, 2)) / 2 as RiskCost
        from Task_SessionContextResult
        where ItemName in (N'资产池期初余额', N'期末资产池未还本金总额')
          and sessionid = #{sessionId}
          and PeriodsId = 0;
    </select>
    <select id="getCashFlowRunCalculateResultBySessionId" resultType="java.util.Map">
        SELECT *
        FROM Task_SessionVariableResult
        WHERE SessionId = #{sessionId}
        ORDER BY SessionVariableResultId
    </select>
    <select id="getCashflowReportByDays" resultType="java.util.Map">
        select ItemName,
               format(ItemValue, 2) as            ItemValue,
               concat('D', replace(Day, '-', '')) DDay
        From Task_SessionContextResultByDays
        where SessionId = #{sessionId}
          and (
                    ItemName like N'%累%早偿%' or
                    ItemName like N'%累%违约%' or
                    ItemName like N'%天数%' or
                    ItemName like N'%触发%' or
                    ItemName like N'%当期%金额%' or
                    ItemName like N'%中登%' or
                    ItemName like N'%调整后%' or
                    ItemName like N'%合格投资%利息%' or
                    ItemName like N'%合格投资%本金%' or
                    ItemName like N'%%实付%' or
                    ItemName like N'%当期利息分配%' or
                    ItemName like N'%当期本金分配%' or
                    ItemName like N'%循环购买%回%' or
                    ItemName like N'模拟循环购买额' or
                    ItemName like N'%利息部分%' or
                    ItemName like N'%本金部分%' or
                    ItemName like N'%保费%' or
                    ItemName like N'信托%账户%余额' or
                    ItemName like N'其他合并入项'
            )
        order by ItemName, DDay
    </select>
    <select id="getPoolCashflowHistoryReports" resultType="java.util.Map">
        select date_format(EndDate, '%Y-%m-%d') as EndDate,
               ScheduledPrincipal               as PrincipalDue,
               ScheduledInterest                as InterestDue,
               PrincipalCollection              as PrincipalPaid,
               InterestCollection               as InterestPaid
        from Analysis_PoolCashflowHistory
        where TrustID = #{trustId}
        order by EndDate;
    </select>
    <select id="getPaymentScheduleAggregationReports" resultType="java.util.Map">
        select date_format(EndDate, '%Y-%m-%d') as EndDate,
               PrincipalAmount                  as PrincipalDue,
               InterestAmount                   as InterestDue,
               0                                as PrincipalPaid,
               0                                as InterestPaid
        from Analysis_PaymentScheduleAggregation
        where TrustID = #{trustId}
        order by EndDate
    </select>
    <select id="getPaymentRecordsReports" resultType="java.util.Map">
        select date_format(DueDate, '%Y-%m')    as EndDate
             , ifnull(sum(PrincipalDue), 0.00)  as PrincipalDue
             , ifnull(sum(InterestDue), 0.00)   as InterestDue
             , ifnull(sum(PrincipalPaid), 0.00) as PrincipalPaid
             , ifnull(sum(InterestPaid), 0.00)  as InterestPaid
        from PaymentManagement_PaymentRecords
        where TrustID = #{trustId}
        group by date_format(DueDate, '%Y-%m')
        order by EndDate;;
    </select>
    <select id="getSolutionTrustInfo" resultType="java.util.Map">
        select t.*, tc.CautionType
        from TrustManagement_Trust t
                 left join Analysis_TrustCaution tc on t.TrustId = tc.TrustId
        where t.TrustId = #{trustId}
    </select>
    <select id="getExtension" resultType="java.util.Map">
        select ItemCode, ItemValue
        from Analysis_SolutionExtension
        where SolutionID = #{trustId};
    </select>
    <select id="getStressTestSummaryCalculateResult" resultType="java.util.Map">
        select ItemName, ItemValue
        from Task_SessionVariableResult
        where SessionId = #{sessionId}
    </select>
    <select id="getInsurancePremiumRate" resultType="java.util.Map">
        select ItemValue / 100 as InsuranncePremiumRate
        from Analysis_SolutionExtension
        where SolutionID = #{trustId}
          and ItemCode = 'InsurancePremium';
    </select>
    <select id="getAssetDetails" resultType="java.util.Map">
        select AccountNo, RemainingBalance, CouponRate, LoanTerm
        from Analysis_AssetDetails
        where TrustID = #{trustId}
        limit 100;
    </select>
    <select id="getSolutionRevolvingPlan" resultType="java.util.Map">
        select t.PeriodsId + 1              as PeriodsId,
               t.TopUpAmount * p.Percentage as ItemValue,
               p.InterestRate,
               p.LoanRemainingTerm          as LoanTerm
        from (select PeriodsId, cast(ItemValue as decimal(19, 2)) as TopUpAmount
              from Task_SessionContextResult
              where SessionId = #{sessionId}
                and ItemName = '模拟循环购买额') t
                 left join Analysis_SolutionRevolvingPlan p
                           on p.TrustID = #{trustId} and
                              (t.PeriodsId + 1) between p.RevolvingPeriodFrom and p.RevolvingPeriodTo
        where p.Percentage is not null
    </select>
    <select id="getTrustBondInfo" resultType="java.util.Map">
        select tb.TrustBondId as tbId,
               tb.ItemCode,
               tb1.ItemValue
        from (select distinct tb.TrustBondId,
                              ite.ItemCode,
                              ite.IsCalculated,
                              ite.IsCompulsory,
                              ite.IsPrimary,
                              ite.DataType,
                              ite.SequenceNo,
                              ite.Precise,
                              ite.UnitOfMeasure
              from TrustManagement_TrustBond tb
                       cross join TrustManagement_ItemTableExtension ite
              where tb.TrustId = #{trustId}
                and ite.TableName = 'TrustBond') tb
                 Left Join TrustManagement_TrustBond tb1
                           on tb.TrustBondId = tb1.TrustBondId and tb.ItemCode = tb1.ItemCode and
                              tb1.TrustId = #{trustId}
                 left join TrustManagement_Item i on tb.ItemCode = i.ItemCode
                 left join TrustManagement_ItemAlias ia on i.ItemId = ia.ItemId
        where i.CategoryId = (select CategoryId from TrustManagement_ItemCategory where CategoryCode = 'TrustBondItem')
          and ia.ItemAliasSetName = 'zh-CN';
    </select>
    <select id="selectSimulationType" resultType="java.lang.String">
        select ItemValue
        from Analysis_SolutionExtension
        where SolutionID = #{trustId}
          and ItemCode = #{itemCode}
        limit 1
    </select>
    <select id="selectTotalLoanAmount" resultType="java.lang.Long">
        select ifnull(sum(PrincipalDue),0)
        from PaymentManagement_PaymentRecords
        where TrustID = #{trustId}
    </select>
    <select id="selectTotalPaid" resultType="java.lang.Long">
        select ifnull(sum(PrincipalPaid),0)
        from PaymentManagement_PaymentRecords
        where TrustID = #{trustId}
          and PayDate <![CDATA[ <= ]]>
        #{poolCloseDate}
    </select>
    <select id="selectInitialPoolBalance" resultType="java.lang.Long">
        select ifnull(sum(PrincipalAmount),0)
        from Analysis_PaymentScheduleAggregation
        where TrustID = #{trustId}
    </select>
    <select id="getPrincipalInterests" resultType="java.util.Map">
        with temp1 as (
            select RevolvingPurchaseID - 1                                                   as PurchasePeriod
                 , ROW_NUMBER() over (partition by RevolvingPurchaseID order by EndDate) - 1 as TermID
                 , RevolvingPurchaseID                                                       as AccountNo
                 , Principal + Interest + Prepayment                                         as Payment
            from Analysis_StressedCashflowTrustPeriodAggregation
            where TrustID = #{trustId} and SessionID = #{analysisMode} and ScenarioID = #{scenarioId}
        ), temp2 as (
            select ROW_NUMBER() over (order by EndDate) - 1 as Period,
                   Principal + Interest + Prepayment        as Payment
            from Analysis_StressedCashflowTrustPeriodAggregation
            where TrustID = #{trustId} and SessionID = #{analysisMode}
              and ScenarioID = #{scenarioId} and RevolvingPurchaseID = 0
        ), temp3 as (
            select a.PurchasePeriod, TermID, AccountNo, if(b.Period is null, a.Payment, b.Payment) as Payment
            from temp1 a
            left join temp2 b on a.AccountNo = 0 and a.TermID = b.Period
        ), temp4 as (
            select PeriodsId as PurchasePeriod, ItemValue as PurchaseAmount
            from Task_SessionContextResult
            where SessionID = #{sessionId} and ItemName = '模拟循环购买额'
            union all
            select -1 PurchasePeriod, #{initialPoolBalance} PurchaseAmount
        ), temp5 as (
            select a.PurchasePeriod, TermID, AccountNo
                 , if(b.PurchasePeriod is null, Payment, (cast(b.PurchaseAmount as decimal(19, 6)) / 10000.00) * Payment) as Payment
            from temp3 a
            left join temp4 b on b.PurchasePeriod >= 0 and a.PurchasePeriod = b.PurchasePeriod
        ), temp6 as (
            select a.PurchasePeriod, TermID, AccountNo
                 , if(b.PurchasePeriod is null, Payment, -1 * cast(b.PurchaseAmount as decimal(19,6))) as Payment
            from temp5 a
            left join temp4 b on a.TermID = a.PurchasePeriod and b.PurchasePeriod = a.PurchasePeriod
        )
        select -1 as PurchasePeriod, -1 as TermID, '0' as AccountNo, -1 * #{initialPoolBalance} as Payment
        union all
        select *
        from temp6
        order by AccountNo, TermID;
    </select>
    <select id="getUnPrincipalInterests" resultType="java.util.Map">
        with temp1 as (
            select RevolvingPurchaseID - 1                                                   as PurchasePeriod
                 , ROW_NUMBER() over (partition by RevolvingPurchaseID order by EndDate) - 1 as TermID
                 , RevolvingPurchaseID                                                       as AccountNo
                 , Principal + Prepayment                                         as Payment
            from Analysis_StressedCashflowTrustPeriodAggregation
            where TrustID = #{trustId} and SessionID = #{analysisMode} and ScenarioID = #{scenarioId}
        ), temp2 as (
            select ROW_NUMBER() over (order by EndDate) - 1 as Period,
                   Principal + Prepayment        as Payment
            from Analysis_StressedCashflowTrustPeriodAggregation
            where TrustID = #{trustId} and SessionID = #{analysisMode}
              and ScenarioID = #{scenarioId} and RevolvingPurchaseID = 0
        ), temp3 as (
            select a.PurchasePeriod, TermID, AccountNo, if(b.Period is null, a.Payment, b.Payment) as Payment
            from temp1 a
            left join temp2 b on a.AccountNo = 0 and a.TermID = b.Period
        ), temp4 as (
            select PeriodsId as PurchasePeriod, ItemValue as PurchaseAmount
            from Task_SessionContextResult
            where SessionID = #{sessionId} and ItemName = '模拟循环购买额'
            union all
            select -1 PurchasePeriod, #{initialPoolBalance} PurchaseAmount
        ), temp5 as (
            select a.PurchasePeriod, TermID, AccountNo
                 , if(b.PurchasePeriod is null, Payment, (cast(b.PurchaseAmount as decimal(19, 6)) / 10000.00) * Payment) as Payment
            from temp3 a
            left join temp4 b on b.PurchasePeriod >= 0 and a.PurchasePeriod = b.PurchasePeriod
        ), temp6 as (
            select a.PurchasePeriod, TermID, AccountNo
                 , if(b.PurchasePeriod is null, Payment, -1 * cast(b.PurchaseAmount as decimal(19,6))) as Payment
            from temp5 a
            left join temp4 b on a.TermID = a.PurchasePeriod and b.PurchasePeriod = a.PurchasePeriod
        )
        select -1 as PurchasePeriod, -1 as TermID, '0' as AccountNo, -1 * #{initialPoolBalance} as Payment
        union all
        select *
        from temp6
        order by AccountNo, TermID;
    </select>

    <!-- 日期报表查询语句 -->
    <select id="getCashFlowDatesBySessionId" resultType="java.util.Map">
        select cf.PeriodId as 期数, date_format(cf.StartDate, '%Y-%m-%d') as 兑付周期开始日期, date_format(cf.EndDate, '%Y-%m-%d') as 兑付周期结束日期
        , cf.Days as 兑付周期天数
        , date_format(nw.StartDate, '%Y-%m-%d') as 归集周期开始日期, date_format(nw.EndDate, '%Y-%m-%d') as 归集周期结束日期, nw.Days as 归集周期天数
        from (
        select ROW_NUMBER() OVER (order by EndDate) - 1 as PeriodId, StartDate, EndDate, datediff(EndDate, StartDate) + 1 as Days
        from TrustManagement_TrustPeriod where TrustID = #{trustId} and TrustPeriodType = 'PaymentDate_CF'
        ) cf
        inner join (
        select ROW_NUMBER() OVER (order by EndDate) - 1 as PeriodId, StartDate, EndDate, datediff(EndDate, StartDate) + 1 as Days
        from TrustManagement_TrustPeriod where TrustID = #{trustId} and TrustPeriodType = 'CollectionDate_NW'
        ) nw on cf.PeriodId = nw.PeriodId
        order by cf.PeriodId asc
    </select>


    <select id="getVariableValueBySessionIdAndVariableName" resultType="java.lang.String">
        select VariableValue from Task_SessionContext
        where SessionId = #{sessionId} and VariableName = #{variableName}
    </select>

    <select id="getDailyRevolving" resultType="java.util.Map">
        select date_format(RevolvingDate, '%Y-%m-%d') as 日期, RevolvingAmount as 循环购买额, ReserveAmountGap as 待补足储备金额
        from Analysis_DailyRevolving
        where TrustID = #{trustId} and SessionID = #{executionSessionID} and ScenarioID = #{scenarioID}
        order by RevolvingDate asc
    </select>

    <select id="getStressedCashflowDetails" resultType="java.util.Map">
        select @rowNumber := @rowNumber + 1  as PeriodId, date_format(PayDate, '%Y-%m-%d') as PayDate, Principal, Interest, DefaultPrincipal, Prepayment
        from Analysis_StressedCashflowDetails, (Select (@rowNumber := 0)) b
        where TrustID = #{trustId} and SessionID = #{executionSessionID}  and ScenarioID = #{scenarioID}  and RevolvingPurchaseID = #{revolvingPurchaseID}
        order by PayDate asc
    </select>
</mapper>

