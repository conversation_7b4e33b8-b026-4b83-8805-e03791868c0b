import sys
import os
import urllib.parse
import traceback
import json
from PythonFiles import SQLParamsParser

CONFIG_FOLDER_NAME = "biz_config"
DISTRIBUTION_FILE_NAME = "DistributionAdjustment.json"

def get_distribution_file_path(trust_id):
    """
    check if the config file for specific trust exists, if not, use the default config file
    """
    trust_config_file_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        CONFIG_FOLDER_NAME,
        str(trust_id),
        DISTRIBUTION_FILE_NAME,
    )
    if os.path.exists(trust_config_file_path):
        file_path = trust_config_file_path
    else:
        file_path = os.path.join(
            os.path.dirname(__file__), "..", CONFIG_FOLDER_NAME, DISTRIBUTION_FILE_NAME
        )
    return file_path

def get_distribution_adjustment_config(trust_id):
    # there are 2 elements in the config file, each element for a specific AssetCategory
    """
    DistributionAdjustment.json example content:
    {
        "AssetSubCategory": {
            "original": [1, 1]
        },
        "LoanTerm": {
            "original": [1, 1]
        },
        "CreditRating": {
            "higher": [1.2, 0.8],
            "original": [1, 1],
            "lower": [0.8, 1.2]
        },
        "RemainingTerm": {
            "higher": [1.2, 0.8],
            "original": [1, 1],
            "lower": [0.8, 1.2]
        }
    }
    """
    # default config file is ../../config/DistributionAdjustment.json
    # config file for specific trust is ../../config/{trust_id}/DistributionAdjustment.json
    file_path = get_distribution_file_path(trust_id)

    if os.path.exists(file_path):

        with open(file_path, "r") as f:
            # Directly parse the file content into a JSON object
            configs = json.load(f)

            return configs

    return None

if __name__ == "__main__":
    try:
        arg = urllib.parse.unquote_plus(sys.argv[1])
        arg = json.dumps(json.loads(arg)['Params'])
        params = SQLParamsParser.getParmas(arg)

        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 Analysis_Trust_DistributionConfigs_Get.py '{"Params":{"SPName":"Analysis_Trust_DistributionConfigs_Get","SQLParams":[{"Name":"TrustID","Value":"88","DBType":"int"}]},"Method":"set","Conn":"FixedIncomeSuite"}'

        result = get_distribution_adjustment_config(params["TrustID"])
        
        print("$OUTPUT" + json.dumps(result))
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
