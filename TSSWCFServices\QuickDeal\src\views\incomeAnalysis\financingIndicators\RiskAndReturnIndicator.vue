<template>
    <div class="risk-return-indicator-page">
        <div class="asset-package-header">
            <el-form inline>
                <el-form-item label="专项计划" required label-width="110px">
                    <el-select v-model="form.trustId" filterable @change="getDateOptions" style="width: 480px;"
                        popper-class="max-height-select-dropdown">
                        <el-option v-for="item in trustOptions" :key="item.TrustId"
                            :label="item.TrustCode + '_' + item.TrustName + '( ID: ' + item.TrustId + ' )'"
                            :value="item.TrustId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-button type="primary" @click="getData" :disabled="loading">检索</el-button>
                </el-form-item>
                <div>
                    <el-form-item label="封包日期" label-width="110px">
                        <el-input v-model="trustInfo.TrustStartDate" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="产品类型" label-width="110px">
                        <el-input v-model="trustInfo.ProductNo" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="产品结构" label-width="110px">
                        <el-input v-model="trustInfo.Revolving" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="状态" label-width="110px">
                        <el-input v-model="trustInfo.TrustStatus" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="清仓回购起算日" label-width="110px">
                        <el-input v-model="trustInfo.RepurchaseValueDate" disabled></el-input>
                    </el-form-item>
                </div>

            </el-form>
        </div>
        <div class="asset-package-content">
            <div class="base-custom-box">
                <div class="base-custom-header">
                    <div class="base-custom-header--left base-custom-tabs">
                        <span class="tab-active-spea" :style="tabOffset"></span>
                        <div class="base-custom-tab-item" v-for="(item, index) in ['APR', 'IRR', '实收收益']"
                            :class="{ 'tab-active': activeTab === index }" :ref="'tab' + index" :key="index"
                            @click="activeTab = index">{{ item }}</div>
                    </div>
                </div>
                <div v-show="activeTab === 0" class="base-custom-body">
                    <base-box :hideFold="false">
                        <template v-slot:title>
                            <span class="base-box-title">APR对比图表</span>
                        </template>
                        <div class="apr-chart">
                            <div class="chart-display-panel">
                                <chart :options="chartOptions"></chart>
                            </div>
                            <div class="date-selector-panel">
                                <div class="date-title">日期选择对比</div>
                                <div class="date-select">
                                    <el-checkbox-group v-model="selectedDates" @change="handleSelectedDate">
                                        <el-checkbox v-for="item in Object.keys(chartData)" :label="item"></el-checkbox>
                                    </el-checkbox-group>
                                </div>
                            </div>
                        </div>
                    </base-box>
                    <base-box :hideFold="false">
                        <template v-slot:title>
                            <span class="base-box-title">资产包实际APR</span>
                            <span class="base-box-meta">注：当前最新资产切片可获取的实际apr</span>
                        </template>
                        <el-table :data="latestSliceData">
                            <el-table-column prop="sliceDate" width="130px">
                                <template v-slot:header>
                                    <span>资产切片日期</span>
                                    <el-tooltip  placement="top" effect="dark"
                                        content="每行数据基于左侧日期的资产切片生成" class="ml-5">
                                        <i class="el-icon-info"></i>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column :label="item" v-for="(item, index) in periods.slice(0, actualPeriods)" width="110px">
                                <template v-slot="{ row }">
                                    <div v-if="row[index] && row[index].actual !== null">
                                        {{ toPercent(row[index].actual) }}
                                    </div>
                                    <div v-else>-</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </base-box>
                    <base-box :hideFold="false" key="forcast">
                        <template v-slot:title>
                            <span class="base-box-title">QD预测APR</span>
                            <span class="base-box-meta">注：黑色为实际apr，彩色为预测apr</span>
                        </template>
                        <el-table :data="APRTableData">
                            <el-table-column prop="sliceDate" width="130px">
                                <template v-slot:header>
                                    <span>资产切片日期</span>
                                    <el-tooltip  placement="top" effect="dark"
                                        content="每行数据基于左侧日期的资产切片生成" class="ml-5">
                                        <i class="el-icon-info"></i>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column :label="item" v-for="(item, index) in periods" width="110px">
                                <template v-slot="{ row }">
                                    <div v-if="row[index] && row[index].actual !== null">
                                        {{ toPercent(row[index].actual) }}
                                    </div>
                                    <div v-else-if="row[index] && row[index].forecast !== null" class="forcast-color">
                                        {{ toPercent(row[index].forecast) }}
                                    </div>
                                    <div v-else>-</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </base-box>
                    <base-box :hideFold="false" key="devitaion">
                        <template v-slot:title>
                            <span class="base-box-title">偏离度</span>
                            <span class="base-box-meta">注：偏离度 = (实际值-预测值 ) / 实际值 × 100%</span>
                        </template>
                        <el-table :data="APRDeviation">
                            <el-table-column prop="sliceDate" width="130px">
                                <template v-slot:header>
                                    <span>资产切片日期</span>
                                    <el-tooltip  placement="top" effect="dark"
                                        content="每行数据基于左侧日期的资产切片生成" class="ml-5">
                                        <i class="el-icon-info"></i>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                            <el-table-column :label="item" v-for="(item, index) in periods.slice(0, actualPeriods)" width="110px">
                                <template v-slot="{ row }">
                                    <div v-if="row[index] && row[index].actual !== null">
                                        {{ toPercent(row[index].actual) }}
                                    </div>
                                    <div v-else-if="row[index] && row[index].forecast !== null" class="forcast-color">
                                        {{ toPercent(row[index].forecast) }}
                                    </div>
                                    <div v-else>-</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </base-box>
                </div>
                <div v-show="activeTab === 1" class="base-custom-body">
                    <base-box :hideFold="false">
                        <template v-slot:title>
                            <span class="base-box-title">IRR对比图表</span>
                        </template>
                        <div style="height: 400px;">
                            <chart :options="IRRChartOptions" style="height: 400px"></chart>
                        </div>
                    </base-box>
                    <base-box :hideFold="false">
                        <template v-slot:title>
                            <span class="base-box-title">IRR对比表</span>
                        </template>
                        <div class="alert-irr">
                            资产包实际IRR
                            <el-tooltip  placement="top" effect="dark">
                                <span slot="content" >基于清仓回购起算日数据计算</span>
                                <i class="el-icon-info"></i>
                            </el-tooltip>： <span>{{ IRRPage.actual_irr }}%</span>
                        </div>
                        <el-table :data="IRRPage.table">
                            <el-table-column prop="item_name" width="130px">
                                <template v-slot:header>
                                    <span>资产切片日期</span>
                                    <el-tooltip  placement="top" effect="dark"
                                        content="每行数据基于左侧日期的资产切片生成" class="ml-5">
                                        <i class="el-icon-info"></i>
                                    </el-tooltip>
                                </template>
                                <template v-slot="{ row }">
                                    <div v-if="row.item_name === '偏离度'">
                                        {{ row.item_name }} 
                                        <el-tooltip placement="top" effect="dark">
                                            <span slot="content" >偏离度 = (实际值-预测值) / 实际值 x 100% </span>
                                            <i class="el-icon-info" style="color: #888;"></i>
                                        </el-tooltip>
                                    </div>
                                    <div v-else>
                                        {{ row.item_name }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :prop="item" v-for="(item, index) in IRRPage.dates" :label="item">
                            </el-table-column>
                        </el-table>
                    </base-box>
                </div>
                <div v-show="activeTab === 2" class="base-custom-body">
                    <base-box :hideFold="false">
                        <template v-slot:title>
                            <span class="base-box-title">实收利率对比图表</span>
                        </template>
                        <div style="height: 400px">
                            <chart :options="RIRChartOptions" style="height: 400px"></chart>
                        </div>
                    </base-box>
                    <base-box :hideFold="false">
                        <template v-slot:title>
                            <span class="base-box-title">实收利率对比表</span>
                        </template>
                        <div class="alert-irr">
                            资产包实际实收利率
                            <el-tooltip  placement="top" effect="dark">
                                <span slot="content" >基于清仓回购起算日数据计算</span>
                                <i class="el-icon-info"></i>
                            </el-tooltip>： <span>{{ RIRPage.actual_irr }}%</span>
                        </div>
                        <el-table :data="RIRPage.table">
                            <el-table-column prop="item_name" width="130px">
                                <template v-slot:header>
                                    <span>资产切片日期</span>
                                    <el-tooltip  placement="top" effect="dark"
                                        content="每行数据基于左侧日期的资产切片生成" class="ml-5">
                                        <i class="el-icon-info"></i>
                                    </el-tooltip>
                                </template>
                                <template v-slot="{ row }">
                                    <div v-if="row.item_name === '偏离度'">
                                        {{ row.item_name }} 
                                        <el-tooltip placement="top" effect="dark">
                                            <span slot="content" >偏离度 = (实际值-预测值) / 实际值 x 100% </span>
                                            <i class="el-icon-info" style="color: #888;"></i>
                                        </el-tooltip>
                                    </div>
                                    <div v-else>
                                        {{ row.item_name }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column :prop="item" v-for="(item, index) in RIRPage.dates" :label="item">
                            </el-table-column>
                        </el-table>
                    </base-box>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import utils from '@/utils/';
import dynamicTab from '@/mixins/dynamicTab.js';
import BaseBox from '../../../components/base/BaseBox.vue';
import api from '@/api/Api';
import Chart from '@/components/common/Highcharts.vue'

export default {
    components: {
        BaseBox,
        Chart
    },
    mixins: [dynamicTab],
    data() {
        return {
            form: {
                trustId: '',
                sliceDate: '',
                currentDate: '',
                callDate: '',
            },
            loading: false,
            trustOptions: [],
            dateOptions: [],
            activeTab: -1,

            periods: [],
            tPlusDates: [],

            actualPeriods: 0,

            latestSliceData: [],
            APRTableData: [],
            APRDeviation: [],

            chartData: {},
            selectedDates: [],

            chartOptions: null,

            IRRPage: {
                dates: [],
                table: [],
                actual_irr: 0,
            },
            RIRPage: {
                dates: [],
                table: [],
                actual_irr: 0,
            },
            IRRChartOptions: null,
            RIRChartOptions: null
        }
    },
    computed: {
        trustInfo() {
            const item = this.trustOptions.find(v => v.TrustId === this.form.trustId);
            if (item) return item;
            return {};
        },
        tabOffset() {
            const tab = 'tab' + this.activeTab;
            if (!this.$refs[tab]) return {};
            const tabItem = this.$refs[tab][0];
            return {
                left: tabItem.offsetLeft + 'px',
                width: tabItem.offsetWidth + 'px'
            };
        }
    },
    mounted() {
        this.newTab({
            id: 'FinancingIndicatorsRiskAndReturnIndicator',
            name: 'FinancingIndicatorsRiskAndReturnIndicator',
            title: '风险和收益指标'
        })
        this.getTrustOptions();

        this.$nextTick(_ => {
            this.activeTab = 0;
        })
    },
    methods: {
        toPercent(val) {
            return val + '%';
        },
        getTrustOptions() {
            api.getTrustInfo().then(res => {
                if (res) {
                    this.trustOptions = res;
                }
            });
        },
        getDateOptions() {

        },
        getData() {
            this.loading = true;
            api.getAPRMetricsResult(this.form.trustId).then(res => {

                this.loading = false;
                if (res.periods) {
                    this.periods = res.periods;
                }

                if (res.end_date) {
                    this.tPlusDates = res.end_date;
                }

                if (res.data) {
                    const APRTableData = [];
                    let maxActualPeriods = 0;
                    for (let date in res.data) {
                        const row = {
                            sliceDate: date
                        }

                        let index = 0;
                        let currentMaxActualPeriods = 0;
                        for (let value of res.data[date].actual) {
                            if (!row[index]) {
                                row[index] = {};
                            }
                            row[index].actual = value;
                            index++;
                            if(value !== null) {
                                currentMaxActualPeriods++;
                            }
                        }

                        if(currentMaxActualPeriods > maxActualPeriods) {
                            maxActualPeriods = currentMaxActualPeriods;
                        }

                        index = 0;
                        for (let value of res.data[date].forecast) {
                            if (!row[index]) {
                                row[index] = {};
                            }
                            row[index].forecast = value;
                            index++;
                        }
                        APRTableData.push(row);
                    }
                    this.APRTableData = APRTableData;

                    this.latestSliceData = APRTableData.length > 0 ? [APRTableData.at(-1)] : [];

                    this.actualPeriods = maxActualPeriods;
                    this.chartData = res.data;
                    this.selectedDates = APRTableData.length > 0 ? [Object.keys(res.data).at(-1)] : [];

                    this.handleSelectedDate(this.selectedDates);
                }

                if (res.deviation) {
                    const APRDeviation = [];
                    for (let date in res.deviation) {
                        const row = {
                            sliceDate: date
                        }

                        let index = 0;
                        for (let value of res.deviation[date].actual) {
                            if (!row[index]) {
                                row[index] = {};
                            }
                            row[index].actual = value;
                            index++;
                        }

                        index = 0;
                        for (let value of res.deviation[date].forecast) {
                            if (!row[index]) {
                                row[index] = {};
                            }
                            row[index].forecast = value;
                            index++;
                        }
                        APRDeviation.push(row);
                    }
                    this.APRDeviation = APRDeviation;
                }
            })

            const calcDevi = (predict, actual) => {
                console.log(predict, actual)
                if (actual === null) return '-';
                // 偏离度 = (实际值-预测值 ) / 实际值 × 100
                return utils.floatMul(utils.floatDiv(utils.floatSub(actual, predict), actual), 100) + '%'
            }

            api.getIRRMetricsResult(this.form.trustId).then(res => {
                const IRRData = [
                    {
                        item_name: 'QD预测',
                    },
                    {
                        item_name: '偏离度'
                    }
                ];
                const IRRSeries = [
                    {
                        name: 'QD预测',
                        data: [],
                        color: '#13C790',
                    },
                    {
                        name: '资产包实际IRR',
                        data: [],
                        color: '#407DFC',
                    }
                ]
                const len = Math.max(res.archive_date.length, res.metrics_result.length);
                for( let i = 0; i < len; i++) {
                    if (!res.archive_date[i]) continue;
                    IRRData[0][res.archive_date[i]] = res.metrics_result[i] != undefined ? res.metrics_result[i] + '%' : '';
                    IRRData[1][res.archive_date[i]] = res.metrics_result[i] != undefined ? calcDevi(res.metrics_result[i], res.actual_irr) : '';
                    
                    IRRSeries[0].data.push(Number(res.actual_irr));
                    IRRSeries[1].data.push(res.metrics_result[i] != undefined ? Number(res.metrics_result[i]) : null);
                }
                this.IRRPage.dates = res.archive_date;
                this.IRRPage.table = IRRData;
                this.IRRPage.actual_irr = res.actual_irr;

                this.IRRChartOptions = this.createChartOptions('IRR对比图', res.archive_date, IRRSeries)
            })

            api.getRIRMetricsResult(this.form.trustId).then(res => {
                const RIRData = [
                    {
                        item_name: 'QD预测',
                    },
                    {
                        item_name: '偏离度'
                    }
                ];
                const RIRSeries = [
                    {
                        name: 'QD预测',
                        data: [],
                        color: '#13C790',
                    },
                    {
                        name: '资产包实收利率',
                        data: [],
                        color: '#407DFC',
                    }
                ]
                const len = Math.max(res.archive_date.length, res.metrics_result.length);
                for (let i = 0; i < len; i++) {
                    if (!res.archive_date[i]) continue;
                    RIRData[0][res.archive_date[i]] = res.metrics_result[i] != undefined ? res.metrics_result[i] + '%' : '';
                    RIRData[1][res.archive_date[i]] = res.metrics_result[i] != undefined ? calcDevi(res.metrics_result[i], res.actual_rir) : '';
                    
                    RIRSeries[0].data.push(Number(res.actual_rir));
                    RIRSeries[1].data.push(res.metrics_result[i] != undefined ? Number(res.metrics_result[i]) : null);
                }
                this.RIRPage.dates = res.archive_date;
                this.RIRPage.table = RIRData;
                this.RIRPage.actual_irr = res.actual_rir;

                this.RIRChartOptions = this.createChartOptions('实收利率对比图', res.archive_date, RIRSeries)
            })
        },
        handleSelectedDate(selected) {
            const series = [];
            const getRandomPastelColor = function () {
                const hue = Math.floor(Math.random() * 360); // 随机色相 (0-359)
                const saturation = 80; // 饱和度
                const lightness = 60; // 亮度
                return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            }
            selected.forEach((date, i) => {
                if (this.chartData[date]) {
                    const actual = this.chartData[date].actual.filter(v => v !== null);
                    const forcast = this.chartData[date].forecast.filter(v => v !== null);
                    series.push({
                        name: date,
                        data: [...actual, ...forcast],
                        color: getRandomPastelColor(),
                        zoneAxis: 'x',
                        zones: [
                            {
                                value: actual.length, // 实际值范围
                                dashStyle: 'Solid'
                            },
                            {
                                dashStyle: 'shortdash' // 预测值范围
                            }
                        ]
                    })
                }
            });
            const categories = [];
            this.periods.forEach((period, i) => {
                categories.push(`${period} (${this.tPlusDates[i]})`)
            });
            this.chartOptions = this.createCombinedChartOptions('APR对比图表', categories, series);
        },
        createChartOptions(title, categories, series) {
            return {
                chart: {
                    type: 'line',
                    spacingTop: 25,
                },
                title: {
                    text: title
                },
                legend: {
                    align: 'center', // 图例水平居中对齐
                    verticalAlign: 'top', // 图例垂直方向顶部对齐
                    layout: 'horizontal', // 图例项水平排列
                    symbolWidth: 18,
                    symbolHeight: 1,
                    symbolRadius: 0,
                },
                xAxis: {
                    categories,
                    title: {
                        text: ''
                    },
                    labels: {
                        rotation: -45 // 标签斜着的角度，正值向右倾斜，负值向左倾斜
                    }
                },
                yAxis: {
                    title: {
                        text: '(%)'
                    },
                    labels: {
                        formatter: function () {
                            return this.value.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }
                    },
                    lineWidth: 1
                },
                plotOptions: {
                    series: {
                        marker: {
                            symbol: 'circle',
                            radius: 4,
                            lineWidth: 2,
                            lineColor: '#FFFFFF',
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    crosshairs: true,
                    valueDecimals: 2,
                    valueSuffix: '%'
                },
                // 系列数据配置
                series
            }
        },
        createCombinedChartOptions(title, categories, series) {
            return {
                chart: {
                    type: 'spline',
                    spacingTop: 25,
                },
                title: {
                    text: title
                },
                legend: {
                    enabled: false
                },
                xAxis: {
                    categories,
                    tickmarkPlacement: 'on',
                    title: {
                        text: ''
                    },
                    labels: {
                        rotation: -45 // 标签斜着的角度，正值向右倾斜，负值向左倾斜
                    }
                },
                yAxis: {
                    title: {
                        text: '(%)'
                    },
                    labels: {
                        formatter: function () {
                            return this.value.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }
                    },
                    lineWidth: 1
                },
                plotOptions: {
                    series: {
                        marker: {
                            enabled: false
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    crosshairs: true,
                    valueDecimals: 2,
                    valueSuffix: '%'
                },
                // 系列数据配置
                series
            }
        },
    }
}
</script>

<style lang="scss">
.risk-return-indicator-page {
    position: relative;
    border-radius: 4px;

    .asset-package-header {
        background-color: #fff;
        margin-left: -15px;
        margin-right: -15px;
        margin-top: -15px;
        padding: 10px 15px 0 15px;
    }

    .asset-package-content {
        margin-top: 14px;

        &.two-columns {
            display: grid;
            grid-template-columns: minmax(0, 1fr) minmax(0, 1fr);
            gap: 15px;
        }
    }

    .base-custom-box {
        background-color: #fff;
        padding: 0 10px;
        border-radius: 4px;
        overflow: hidden;

        .base-custom-header {
            height: 42px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #EBEEF5;
        }

        .base-custom-body {
            padding: 10px 0;
        }
    }

    .base-custom-tabs {
        position: relative;
        height: 100%;
        line-height: 100%;
        display: flex;

        .base-custom-tab-item {
            margin-right: 20px;
            margin-left: 10px;
            color: #4D4D4D;
            cursor: pointer;
            height: 100%;
            display: flex;
            align-items: center;

            &:hover,
            &.tab-active {
                color: $--color-primary;
            }
        }

        .tab-active-spea {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 30px;
            height: 2px;
            background-color: $--color-primary;
            transition: all 0.3s ease-in-out;
        }
    }

    .base-box-title {
        font-weight: bold;
        color: #323232;
    }

    .base-box-meta {
        font-size: 12px;
        color: #4D4D4D;
        line-height: 17px;
        text-align: left;
        margin-left: 12px;
    }

    .apr-chart {
        display: grid;
        grid-template-columns: minmax(0, 1fr) minmax(0, 230px);

        .chart-display-panel {
            height: 400px;
            overflow: hidden;
        }

        .date-selector-panel {
            background-color: #F3F5FA;
            width: 230px;
        }

        .date-title {
            padding-bottom: 10px;
            border-bottom: 1px solid #e3e6ee;
            font-weight: 400;
            color: #242424;
            padding: 10px 15px;
        }

        .date-select{
            padding: 10px 15px;
            overflow: auto;

            .el-checkbox{
                margin-bottom: 6px;
            }
        }
    }

    .forcast-color {
        color: #57A925;
    }

    .alert-irr{
        background-color: rgba(19,199,144,0.05);
        border-radius: 4px;
        padding: 0 10px;
        height: 38px;
        line-height: 38px;
        margin-bottom: 10px;
        color: #4D4D4D;

        span{
            font-size: 18px;
            font-weight: bold;
            color: #13C790;
        }

        .el-icon-info{
            color: #a3aca9;
        }
    }
}
</style>