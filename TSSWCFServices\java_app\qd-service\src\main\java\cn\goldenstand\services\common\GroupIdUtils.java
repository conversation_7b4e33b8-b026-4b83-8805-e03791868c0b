/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.common;

import java.util.Objects;

/**
 * 账户编号工具类
 * 用于处理账户编号的字符替换映射
 * 
 * <AUTHOR>
 */
public class GroupIdUtils {

    // 微贷分组逻辑映射-小额
    public static final String[][] REPLACEMENT_RULES_WD_1 = {
        {"小额", "中额"},
        {"生意贷", "生活费"},
        {"A", "B", "C/D/NULL"},
        {"(0,3]", "(3,6]", "(6,12]"},
        {"(-1,0.5]", "(0.5,1]", "(1,2]", "(2,3]", "(3,4]", "(4,5]", "(5,6]", "(6,12+]"}
    };

    // 微贷分组逻辑映射-中额
    public static final String[][] REPLACEMENT_RULES_WD_2 = {
        {"小额", "中额"},
        {"生意贷", "生活费"},
        {"A", "B", "C/D/NULL"},
        {"(0,12]", "(12,24]", "(24,36]"},
        {"(-1,0.5]", "(0.5,1]", "(1,2]", "(2,3]", "(3,4]", "(4,5]", "(5,6]"
            , "(6,7]", "(7,9]", "(9,11]", "(11,13]", "(13,15]", "(15,17]", "(17,19]"
            , "(19,21]", "(21,24]", "(24,27]", "(27,30]", "(30,33]", "(33,36+]"
        }
    };

    // 月付分组逻辑映射-非分期
    public static final String[][] REPLACEMENT_RULES_YF_1 = {
        {"非分期", "分期"},
        {"消费", "循环"},
        {"[A1,A3]", "[B1,B3]", "[C1,C3]", "[D1,D2]", "NULL"},
        {"(0,1]"},
        {"(0,1]"}
    };

    // 月付分组逻辑映射-分期
    public static final String[][] REPLACEMENT_RULES_YF_2 = {
        {"非分期", "分期"},
        {"分期"},
        {"[A1,A3]", "[B1,B3]", "[C1,C3]", "[D1,D2]", "NULL"},
        {"(0,3]", "(3,6]", "(6,9]", "(9,12]"},
        {"(-1,0.5]", "(0.5,1]", "(1,2]", "(2,3]", "(3,4]", "(4,5]", "(5,6]", "(6,12+]"}
    };

    /**
     * 字符替换映射
     * 例：将1_2_1_1_8 映射成 小额_生活费_A_(0,3]_(6,12+]
     * 
     * @param groupId 账户编号
     * @param productNo 产品编号
     * @return 替换后的字符串
     */
    public static String replaceStr(String groupId, String productNo) {
        if (groupId == null || productNo == null) {
            return groupId;
        }
        
        String[] arr = groupId.split("_");
        if (arr.length == 5) {
            StringBuilder output = new StringBuilder();
            for (int i = 0; i < arr.length; i++) {
                String part = arr[i];
                try {
                    if (Objects.equals(productNo, "00010")) {
                        // 00010为月付产品
                        if (arr[0].equals("1")) { // 非分期
                            output.append(REPLACEMENT_RULES_YF_1[i][Integer.parseInt(part) - 1]).append("_");
                        } else if (arr[0].equals("2")) { // 分期
                            output.append(REPLACEMENT_RULES_YF_2[i][Integer.parseInt(part) - 1]).append("_");
                        }
                    } else {
                        // 其余为微贷产品
                        if (arr[0].equals("1")) { // 小额
                            output.append(REPLACEMENT_RULES_WD_1[i][Integer.parseInt(part) - 1]).append("_");
                        } else if (arr[0].equals("2")) { // 中额
                            output.append(REPLACEMENT_RULES_WD_2[i][Integer.parseInt(part) - 1]).append("_");
                        }
                    }
                } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
                    // 如果解析失败或索引越界，返回原始字符串
                    return groupId;
                }
            }
            return output.substring(0, output.length() - 1);
        }
        return groupId;
    }
}
