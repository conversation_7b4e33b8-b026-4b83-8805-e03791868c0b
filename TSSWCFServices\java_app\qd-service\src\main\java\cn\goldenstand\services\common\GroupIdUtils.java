/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.common;

import java.util.Objects;

/**
 * 账户编号工具类
 * 用于处理账户编号的字符替换映射
 * 
 * <AUTHOR>
 */
public class GroupIdUtils {

    // 微贷分组逻辑映射-小额
    public static final String[][] REPLACEMENT_RULES_WD_1 = {
        {"小额", "中额"},
        {"生意贷", "生活费"},
        {"A", "B", "C/D/NULL"},
        {"(0,3]", "(3,6]", "(6,12]"},
        {"(-1,0.5]", "(0.5,1]", "(1,2]", "(2,3]", "(3,4]", "(4,5]", "(5,6]", "(6,12+]"}
    };

    // 微贷分组逻辑映射-中额
    public static final String[][] REPLACEMENT_RULES_WD_2 = {
        {"小额", "中额"},
        {"生意贷", "生活费"},
        {"A", "B", "C/D/NULL"},
        {"(0,12]", "(12,24]", "(24,36]"},
        {"(-1,0.5]", "(0.5,1]", "(1,2]", "(2,3]", "(3,4]", "(4,5]", "(5,6]"
            , "(6,7]", "(7,9]", "(9,11]", "(11,13]", "(13,15]", "(15,17]", "(17,19]"
            , "(19,21]", "(21,24]", "(24,27]", "(27,30]", "(30,33]", "(33,36+]"
        }
    };

    // 月付分组逻辑映射-非分期
    public static final String[][] REPLACEMENT_RULES_YF_1 = {
        {"非分期", "分期"},
        {"消费", "循环"},
        {"[A1,A3]", "[B1,B3]", "[C1,C3]", "[D1,D2]", "NULL"},
        {"(0,1]"},
        {"(0,1]"}
    };

    // 月付分组逻辑映射-分期
    public static final String[][] REPLACEMENT_RULES_YF_2 = {
        {"非分期", "分期"},
        {"分期"},
        {"[A1,A3]", "[B1,B3]", "[C1,C3]", "[D1,D2]", "NULL"},
        {"(0,3]", "(3,6]", "(6,9]", "(9,12]"},
        {"(-1,0.5]", "(0.5,1]", "(1,2]", "(2,3]", "(3,4]", "(4,5]", "(5,6]", "(6,12+]"}
    };

    /**
     * 字符替换映射
     * 例：将1_2_1_1_8 映射成 小额_生活费_A_(0,3]_(6,12+]
     * 或：将1_2_1_1 映射成 小额_生活费_A_(0,3]
     *
     * @param groupId 账户编号（支持4位或5位格式）
     * @param productNo 产品编号
     * @return 替换后的字符串
     */
    public static String replaceStr(String groupId, String productNo) {
        if (groupId == null || productNo == null) {
            return groupId;
        }

        String[] arr = groupId.split("_");
        // 支持4位和5位格式
        if (arr.length == 4 || arr.length == 5) {
            StringBuilder output = new StringBuilder();
            for (int i = 0; i < arr.length; i++) {
                String part = arr[i];
                try {
                    String[][] rules = getRules(productNo, arr[0]);
                    if (rules != null && i < rules.length) {
                        int index = Integer.parseInt(part) - 1;
                        if (index >= 0 && index < rules[i].length) {
                            output.append(rules[i][index]).append("_");
                        } else {
                            // 索引越界，返回原始字符串
                            return groupId;
                        }
                    } else {
                        // 规则不存在，返回原始字符串
                        return groupId;
                    }
                } catch (NumberFormatException e) {
                    // 如果解析失败，返回原始字符串
                    return groupId;
                }
            }
            return output.substring(0, output.length() - 1);
        }
        return groupId;
    }

    /**
     * 根据产品编号和第一位数字获取对应的规则数组
     *
     * @param productNo 产品编号
     * @param firstDigit 第一位数字
     * @return 对应的规则数组
     */
    private static String[][] getRules(String productNo, String firstDigit) {
        if (Objects.equals(productNo, "00010")) {
            // 00010为月付产品
            if ("1".equals(firstDigit)) { // 非分期
                return REPLACEMENT_RULES_YF_1;
            } else if ("2".equals(firstDigit)) { // 分期
                return REPLACEMENT_RULES_YF_2;
            }
        } else {
            // 其余为微贷产品
            if ("1".equals(firstDigit)) { // 小额
                return REPLACEMENT_RULES_WD_1;
            } else if ("2".equals(firstDigit)) { // 中额
                return REPLACEMENT_RULES_WD_2;
            }
        }
        return null;
    }


}
