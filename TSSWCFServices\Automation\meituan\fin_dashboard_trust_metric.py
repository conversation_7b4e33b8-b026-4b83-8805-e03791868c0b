import json
import os
import sys
import urllib.parse
from datetime import datetime, timedelta

import pandas as pd
from get_standard_session_id import get_standard_session_id
from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from PythonFiles import SQLParamsParser
from PythonFiles.logger_config import Logger

script_name = os.path.basename(__file__)
logger_instance = Logger()
log = logger_instance.get_logger(script_name)


def date_add(date_str, days):
    date = datetime.strptime(date_str, "%Y-%m-%d")
    new_date = date + timedelta(days=days)
    return new_date.strftime("%Y-%m-%d")

def clean_up(report_date, trust_id=None):
    filter_trust = f"trust_id = {trust_id}" if trust_id else "1=1"
    sql = f'''
        delete from daily_trust_metric where reporting_date = '{report_date}' and {filter_trust}
    '''
    MysqlAdapter.commonRunsql(sql)

def get_metric(session_id):
    sql = f'''
        select
            variable_remuneration_ratio * 100 as var_comp_ratio, 
            variable_remuneration_ratio_excess * 100 as var_comp_ratio_incl_excess, 
            ifnull(asset_service_fee_paid,0) + ifnull(full_excess_service_fee,0) as backend_revenue,
            payment_gap as redemption_gap
        from product_design_risk_transfer_report
        where task_session_id = '{session_id}'
    '''
    df_risk = MysqlAdapter.exec_sql_to_df(sql)

    # 如果df_risk为空，则创建包含同样字段的空dataframe
    if df_risk.empty:
        df_risk = pd.DataFrame(columns=['var_comp_ratio', 'var_comp_ratio_incl_excess', 'backend_revenue', 'redemption_gap'])

    # 90d_plus_npl_rate
    sql = f'''
        select
            cast(s1.ItemValue as decimal(19,6))*100 as 90d_plus_npl_rate
        from Task_SessionVariableResult as s1
        where s1.SessionId = '{session_id}' and s1.ItemName = '90+不良率'
    '''
    df_90dplus = MysqlAdapter.exec_sql_to_df(sql)

    # 如果df_metric为空，则创建包含同样字段的空dataframe 
    if df_90dplus.empty:
        df_90dplus = pd.DataFrame(columns=['90d_plus_npl_rate'])
    
    # apr_mob15, irr, actual_interest_rate
    sql = f'''
        SELECT id, category, trust_id, session_id, scenario_id, asset_type, end_date
            , metrics_type, metrics_result, numerator, denominator, created_at
        FROM abs_metrics_calculate_result
        WHERE session_id = '{session_id}'
        ORDER BY metrics_type,end_date ASC
    '''
    df_metric = MysqlAdapter.exec_sql_to_df(sql)
    if not df_metric.empty:
        # 添加row_number, 从1开始
        df_metric['rn'] = df_metric.groupby(['metrics_type']).cumcount() + 1
        # 获取metrics_type='APR' and rn=15的数值，并乘以100
        apr_mob15 = df_metric[(df_metric['metrics_type'] == 'APR') & (df_metric['rn'] == 15)]['metrics_result'].values[0] * 100
        irr = df_metric[(df_metric['metrics_type'] == 'IRR') & (df_metric['asset_type'] == '总')]['metrics_result'].values[0] * 100
        actual_interest_rate = df_metric[(df_metric['metrics_type'] == 'RIR') & (df_metric['asset_type'] == '总')]['metrics_result'].values[0] * 100
        df_metric = pd.DataFrame({
            'apr_mob15': [apr_mob15],
            'irr': [irr],
            'actual_interest_rate': [actual_interest_rate]
        })
    else:
        df_metric = pd.DataFrame(columns=['apr_mob15', 'irr', 'actual_interest_rate'])

    # concat df_risk, df_90dplus, df_metric
    df_metric = pd.concat([df_risk, df_90dplus, df_metric], axis=1)
    return df_metric

def get_formal_trust(trust_id=None):
    filter_trust = f"t.TrustId = {trust_id}" if trust_id else "1=1"
    sql = f'''
        select t.TrustId as trust_id, t.TrustCode as trust_code, t.TrustName as trust_name,
            replace(replace(replace(replace(replace(te.ItemValue, '00010', '月付'), '00001', '生活费小额')
                , '00007', '生意贷小额'), '00017', '生意贷中额'), '00027', '生活费中额') as product_no,
            case when te2.ItemValue = 'true' then '循环' else '静态' end as revolving,
            case when te.ItemValue like '%00010%' then '月付' else '微贷' end as category
        from TrustManagement_Trust as t
        inner join Analysis_Trust as a on t.TrustId = a.TrustId
        inner join TrustManagement_TrustInfoExtension as te on t.TrustId = te.TrustId and te.ItemCode = 'ProductNo'
        left join TrustManagement_TrustExtension as te2 on t.TrustId = te2.TrustId and te2.ItemCode = 'IsTopUpAvailable'
        where t.TrustId != 0 and a.IsCalculate = 1 and t.IsMarketProduct = 1
            and a.TrustCalculationStatus in (2, 3) and {filter_trust}
    '''
    return MysqlAdapter.exec_sql_to_df(sql)

def main(report_date, trust_id=None):
    # 获取正式发行 & 是否测算=是 的产品清单
    df_trust = get_formal_trust(trust_id)

    filter_trust = f"trust_id = {trust_id}" if trust_id else "1=1"
    # 获取当天运行成功的记录
    sql = f'''
        select distinct trust_id
        from automation_run_schedule
        where run_date = '{report_date}' and {filter_trust}
            and run_type = 'ProductDesign' and run_status = 3
    '''
    df_cal_trust = MysqlAdapter.exec_sql_to_df(sql)

    df_cal_trust = df_cal_trust.merge(df_trust, on='trust_id', how='inner')

    df_tmp = pd.DataFrame(columns=['trust_id','var_comp_ratio','var_comp_ratio_incl_excess','90d_plus_npl_rate',
                                   'apr_mob15','irr','actual_interest_rate','backend_revenue','redemption_gap'])
    for index, row in df_cal_trust.iterrows():
        try:
            t_id = row['trust_id']
            # 获取标准场景的session_id
            session_id = get_standard_session_id(t_id, row['revolving'])
            # 通过session_id获取存续产品指标
            if session_id is not None:
                df_metric = get_metric(session_id)
                df_metric['trust_id'] = t_id
                df_tmp = pd.concat([df_tmp, df_metric], ignore_index=True)
        except Exception as e:
            log.error(f"获取存续产品指标失败: {e}", exc_info=True)

    df_trust_list = df_trust[['trust_id', 'trust_code', 'trust_name', 'product_no', 'category']]
    df_tmp = df_trust_list.merge(df_tmp, on='trust_id', how='left')
    df_tmp['reporting_date'] = report_date

    clean_up(report_date, trust_id)
    MysqlAdapter.dataframe_tosql(df_tmp, 'daily_trust_metric')

if __name__ == "__main__":
    try:
        log.info("开始统计存续产品指标")
        str_arg = ""
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]
            arg = urllib.parse.unquote_plus(str_arg)
            arg = json.loads(arg)
            if 'Params' not in arg:
                params = arg
            else:
                arg = json.dumps(arg['Params'])
                params = SQLParamsParser.getParmas(arg)
        else:
            params = {}

        today_date = datetime.now().strftime("%Y-%m-%d")
        report_date = params['report_date'] if 'report_date' in params else today_date
        trust_id = params['trust_id'] if 'trust_id' in params else None

        main(report_date, trust_id)
        log.info("统计存续产品指标完成")
        sys.exit(0)
    except Exception as e:
        log.error(f"统计存续产品指标失败: {e}", exc_info=True)
        sys.exit(1)