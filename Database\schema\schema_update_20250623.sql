ALTER TABLE Analysis_BondRepurchaseSettingExt ADD is_calculated tinyint(1) DEFAULT 0 COMMENT '是否计算';
ALTER TABLE archive_analysis_bond_repurchase_setting_ext ADD is_calculated tinyint(1) DEFAULT 0 COMMENT '是否计算' AFTER trigger_next_month;

CREATE TABLE analysis_grouped_mob_price_summary (
    id bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
    trust_id int NOT NULL COMMENT '产品ID',
    session_id varchar(50) NOT NULL COMMENT '会话ID',
    scenario_id bigint NOT NULL COMMENT '情景ID',
    scenario_desc varchar(200) NULL COMMENT '情景描述',
    repurchase_calculation_date date NOT NULL COMMENT '回购起算日',
    simulation_start_date date NOT NULL COMMENT '模拟起始日',
    m0 decimal(19,6) NULL COMMENT 'M0',
    m1 decimal(19,6) NULL COMMENT 'M1',
    m2 decimal(19,6) NULL COMMENT 'M2',
    m3 decimal(19,6) NULL COMMENT 'M3',
    m4 decimal(19,6) NULL COMMENT 'M4',
    m5 decimal(19,6) NULL COMMENT 'M5',
    m6 decimal(19,6) NULL COMMENT 'M6',
    m6plus decimal(19,6) NULL COMMENT 'M6plus',
    pricing_m0 decimal(15,6) NULL COMMENT 'pricing_m0',
    pricing_m1 decimal(15,6) NULL COMMENT 'pricing_m1',
    pricing_m2 decimal(15,6) NULL COMMENT 'pricing_m2',
    pricing_m3 decimal(15,6) NULL COMMENT 'pricing_m3',
    pricing_m4 decimal(15,6) NULL COMMENT 'pricing_m4',
    pricing_m5 decimal(15,6) NULL COMMENT 'pricing_m5',
    pricing_m6 decimal(15,6) NULL COMMENT 'pricing_m6',
    pricing_m6plus decimal(15,6) NULL COMMENT 'pricing_m6plus',
    price_m0 decimal(19,6) NULL COMMENT 'price_m0',
    price_m1 decimal(19,6) NULL COMMENT 'price_m1',
    price_m2 decimal(19,6) NULL COMMENT 'price_m2',
    price_m3 decimal(19,6) NULL COMMENT 'price_m3',
    price_m4 decimal(19,6) NULL COMMENT 'price_m4',
    price_m5 decimal(19,6) NULL COMMENT 'price_m5',
    price_m6 decimal(19,6) NULL COMMENT 'price_m6',
    price_m6plus decimal(19,6) NULL COMMENT 'price_m6plus',
    PRIMARY KEY (`id`),
    KEY `idx_trust_session` (`trust_id`,`session_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='回购价格汇总表';