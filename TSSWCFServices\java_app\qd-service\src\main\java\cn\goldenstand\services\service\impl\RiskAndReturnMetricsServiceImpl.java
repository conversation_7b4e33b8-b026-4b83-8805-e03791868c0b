/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import cn.goldenstand.services.mapper.RiskAndReturnMetricsMapper;
import cn.goldenstand.services.service.RiskAndReturnMetricsService;


/**
 * <AUTHOR>
 */
@Service
public class RiskAndReturnMetricsServiceImpl implements RiskAndReturnMetricsService {

    private static final Logger logger = LoggerFactory.getLogger(RiskAndReturnMetricsServiceImpl.class);

    @Resource
    RiskAndReturnMetricsMapper riskAndReturnMetricsMapper;

    /**
     * 获取产品的信息
     * @return 返回产品的信息
     */
    @Override
    public List<Map<String, Object>> getTrustInfo() {
        logger.info("调用 getTrustInfo 方法");
        List<Map<String, Object>> result = riskAndReturnMetricsMapper.getTrustBaseInfo();
        logger.info("getTrustInfo 返回结果: {}", result);
        return result;
    }

    /**
     * 获取产品的APR指标结果
     * @param trustId 产品ID
     * @return 返回APR指标结果
     */
    @Override
    public Map<String, Object> getAPRMetricsResult(Integer trustId) {
        logger.info("调用 getAPRMetricsResult 方法, trustId={}", trustId);
        try {
            Map<String, String> trustDates = calculateTrustDates(trustId);
            if (trustDates == null) {
                logger.warn("calculateTrustDates 返回 null, trustId={}", trustId);
                return new HashMap<>();
            }
            String trustStartDate = trustDates.get("startDate");
            logger.debug("trustStartDate: {}", trustStartDate);
            String trustEndDate = trustDates.get("endDate");
            List<Map<String, String>> result = riskAndReturnMetricsMapper.getAPRMetricsResult(trustId, trustStartDate, trustEndDate);

            // 过滤出月末数据
            result = result.stream()
                .filter(item -> {
                    LocalDate date = LocalDate.parse(item.get("end_date"), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    return date.getDayOfMonth() == date.lengthOfMonth();
                })
                .collect(Collectors.toList());

            return transformResult(result);
        } catch (Exception e) {
            logger.error(String.format("getAPRMetricsResult 执行异常, trustId=%s, error: %s", trustId, e.getMessage()));
            return new HashMap<>();
        }
    }

    /**
     * 计算产品的开始日期和结束日期
     * @param trustId 产品ID
     * @return 包含开始日期和结束日期的Map，如果获取数据失败返回null
     */
    public Map<String, String> calculateTrustDates(Integer trustId) {
        // 获取产品的成立日
        Map<String, Object> data = riskAndReturnMetricsMapper.getTrustDateInfo(trustId);
        if (data == null) {
            return null;
        }

        // 获取产品成立日
        String trustStartDate = (String) data.get("TrustStartDate");
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 解析日期
        LocalDate startDate = LocalDate.parse(trustStartDate, formatter);

        String isTopUpAvailable = (String) data.get("IsTopUpAvailable");
        int revolvingPeriod = Integer.parseInt((String) data.get("RevolvingPeriod"));

        LocalDate newDate = startDate.plusMonths(36);
        // 如果是循环产品，则结束日期为成立日+revolvingPeriod月+36月
        if (isTopUpAvailable.equals("true")) {
            // trustStartDate日期上增加revolvingPeriod月
            newDate = newDate.plusMonths(revolvingPeriod);
        }

        // 清仓回购起算日，如无则为空
        String repurchaseDate = (String) data.get("RepurchaseValueDate");

        Map<String, String> result = new HashMap<>();
        result.put("startDate", trustStartDate);
        result.put("endDate", newDate.format(formatter));
        result.put("repurchaseDate", repurchaseDate);

        return result;
    }

    /**
     * 转换结果
     * @return 返回转换后的结果
     */
    public Map<String, Object> transformResult(List<Map<String, String>> result) {
        if (result == null || result.isEmpty()) {
            return new HashMap<>();
        }

        // 收集日期数据
        Map<String, Object> dateData = collectDateData(result);
        @SuppressWarnings("unchecked")
        List<String> endDates = (List<String>) dateData.get("endDates");
        @SuppressWarnings("unchecked")
        List<String> archiveDates = (List<String>) dateData.get("archiveDates");
        @SuppressWarnings("unchecked")
        List<LocalDate> archiveLocalDates = (List<LocalDate>) dateData.get("archiveLocalDates");
        @SuppressWarnings("unchecked")
        List<LocalDate> endLocalDates = (List<LocalDate>) dateData.get("endLocalDates");

        // 构造周期列表和组织原始结果
        List<String> periods = createPeriodsList(endDates.size());
        Map<String, Map<String, String>> archiveToEndDateMap = organizeResultsByDate(result);

        // 组装返回结果
        Map<String, Object> finalMap = new LinkedHashMap<>();
        finalMap.put("periods", periods);
        finalMap.put("end_date", endDates);

        // 构建数据映射
        Map<String, Map<String, List<Double>>> dataMap = buildDataMap(
                archiveDates, archiveLocalDates, endDates, endLocalDates, archiveToEndDateMap);
        finalMap.put("data", dataMap);

        // 计算偏离度
        Map<String, Map<String, List<Double>>> deviationMap = calculateDeviation(
                archiveDates, archiveLocalDates, endLocalDates, dataMap);
        finalMap.put("deviation", deviationMap);

        return finalMap;
    }

    /**
     * 收集日期数据
     * @param result 原始结果数据
     * @return 包含各种日期数据的Map
     */
    private Map<String, Object> collectDateData(List<Map<String, String>> result) {
        Map<String, Object> dateData = new HashMap<>();

        // 收集所有报告周期（end_date），去重、按日期升序排列
        List<String> endDates = result.stream()
                .map(r -> r.get("end_date"))
                .distinct()
                .sorted()
                .collect(Collectors.toList());
        dateData.put("endDates", endDates);

        // 收集所有归档日期（archive_date），去重并按日期升序排列
        List<String> archiveDates = result.stream()
                .map(r -> r.get("archive_date")).distinct().sorted().collect(Collectors.toList());
        dateData.put("archiveDates", archiveDates);

        // 预解析所有归档日期为LocalDate，减少多次解析开销
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        List<LocalDate> archiveLocalDates = archiveDates.stream()
            .map(d -> LocalDate.parse(d, fmt))
            .collect(Collectors.toList());
        dateData.put("archiveLocalDates", archiveLocalDates);

        // 将 endDates 转为 LocalDate 列表，便于后续日期比较
        List<LocalDate> endLocalDates = endDates.stream()
            .map(d -> LocalDate.parse(d, fmt))
            .collect(Collectors.toList());
        dateData.put("endLocalDates", endLocalDates);

        return dateData;
    }

    /**
     * 创建周期列表
     * @param size 周期数量
     * @return 周期列表
     */
    private List<String> createPeriodsList(int size) {
        List<String> periods = new ArrayList<>();
        for (int i = 1; i <= size; i++) {
            periods.add("T+" + i);
        }
        return periods;
    }

    /**
     * 将原始结果按 archive_date 和 end_date 组织到嵌套映射
     * @param result 原始结果数据
     * @return 组织后的映射
     */
    private Map<String, Map<String, String>> organizeResultsByDate(List<Map<String, String>> result) {
        Map<String, Map<String, String>> archiveToEndDateMap = new HashMap<>();
        for (Map<String, String> row : result) {
            String archive = row.get("archive_date");
            String end = row.get("end_date");
            String value = row.get("metrics_result");
            archiveToEndDateMap
                .computeIfAbsent(archive, k -> new HashMap<>())
                .put(end, value);
        }
        return archiveToEndDateMap;
    }

    /**
     * 构建数据映射
     * @param archiveDates 归档日期列表
     * @param archiveLocalDates 归档日期的LocalDate列表
     * @param endDates 结束日期列表
     * @param endLocalDates 结束日期的LocalDate列表
     * @param archiveToEndDateMap 按日期组织的原始结果
     * @return 数据映射
     */
    private Map<String, Map<String, List<Double>>> buildDataMap(
            List<String> archiveDates,
            List<LocalDate> archiveLocalDates,
            List<String> endDates,
            List<LocalDate> endLocalDates,
            Map<String, Map<String, String>> archiveToEndDateMap) {

        Map<String, Map<String, List<Double>>> dataMap = new LinkedHashMap<>();
        for (int idx = 0; idx < archiveDates.size(); idx++) {
            String archive = archiveDates.get(idx);
            LocalDate archiveLocal = archiveLocalDates.get(idx);

            Map<String, List<Double>> subMap = createDataSubMap(
                    archive, archiveLocal, endDates, endLocalDates, archiveToEndDateMap);
            dataMap.put(archive, subMap);
        }
        return dataMap;
    }

    /**
     * 为单个归档日期创建数据子映射
     * @param archive 归档日期
     * @param archiveLocal 归档日期的LocalDate
     * @param endDates 结束日期列表
     * @param endLocalDates 结束日期的LocalDate列表
     * @param archiveToEndDateMap 按日期组织的原始结果
     * @return 数据子映射
     */
    private Map<String, List<Double>> createDataSubMap(
            String archive,
            LocalDate archiveLocal,
            List<String> endDates,
            List<LocalDate> endLocalDates,
            Map<String, Map<String, String>> archiveToEndDateMap) {

        Map<String, List<Double>> subMap = new LinkedHashMap<>();
        List<Double> actualList = new ArrayList<>(endDates.size());
        List<Double> forecastList = new ArrayList<>(endDates.size());

        Map<String, String> endDateMap = archiveToEndDateMap.getOrDefault(archive, Collections.emptyMap());
        for (int j = 0; j < endLocalDates.size(); j++) {
            Double value = Optional.ofNullable(endDateMap.get(endDates.get(j)))
                .map(s -> {
                    BigDecimal bd = new BigDecimal(s);
                    return bd.multiply(new BigDecimal(100)).setScale(4, RoundingMode.HALF_UP).doubleValue();
                }).orElse(null);

            if (archiveLocal.isAfter(endLocalDates.get(j)) || archiveLocal.isEqual(endLocalDates.get(j))) {
                actualList.add(value);
                forecastList.add(null);
            } else {
                actualList.add(null);
                forecastList.add(value);
            }
        }

        subMap.put("actual", actualList);
        subMap.put("forecast", forecastList);
        return subMap;
    }

    /**
     * 计算偏离度
     * @param archiveDates 归档日期列表
     * @param archiveLocalDates 归档日期的LocalDate列表
     * @param endLocalDates 结束日期的LocalDate列表
     * @param dataMap 数据映射
     * @return 偏离度映射
     */
    private Map<String, Map<String, List<Double>>> calculateDeviation(
            List<String> archiveDates,
            List<LocalDate> archiveLocalDates,
            List<LocalDate> endLocalDates,
            Map<String, Map<String, List<Double>>> dataMap) {

        // 基准为最后一个 archive 的 actual 列表
        String baselineArchive = archiveDates.get(archiveDates.size() - 1);
        List<Double> baselineActualList = dataMap.get(baselineArchive).get("actual");

        int n = baselineActualList.indexOf(null);
        if (n < 0) n = baselineActualList.size();

        return buildDeviationMap(
                archiveDates, archiveLocalDates, endLocalDates, dataMap, baselineActualList, n);
    }

    /**
     * 构建偏离度映射
     * @param archiveDates 归档日期列表
     * @param archiveLocalDates 归档日期的LocalDate列表
     * @param endLocalDates 结束日期的LocalDate列表
     * @param dataMap 数据映射
     * @param baselineActualList 基准实际值列表
     * @param n 有效数据长度
     * @return 偏离度映射
     */
    private Map<String, Map<String, List<Double>>> buildDeviationMap(
            List<String> archiveDates,
            List<LocalDate> archiveLocalDates,
            List<LocalDate> endLocalDates,
            Map<String, Map<String, List<Double>>> dataMap,
            List<Double> baselineActualList,
            int n) {

        Map<String, Map<String, List<Double>>> deviationMap = new LinkedHashMap<>();

        for (int idx = 0; idx < archiveDates.size(); idx++) {
            String archive = archiveDates.get(idx);
            LocalDate archiveLocal = archiveLocalDates.get(idx);

            Map<String, List<Double>> devSubMap = calculateSingleDeviationMap(
                    archive, archiveLocal, endLocalDates, dataMap, baselineActualList, n);
            deviationMap.put(archive, devSubMap);
        }

        return deviationMap;
    }

    /**
     * 计算单个归档日期的偏离度映射
     * @param archive 归档日期
     * @param archiveLocal 归档日期的LocalDate
     * @param endLocalDates 结束日期的LocalDate列表
     * @param dataMap 数据映射
     * @param baselineActualList 基准实际值列表
     * @param n 有效数据长度
     * @return 单个归档日期的偏离度映射
     */
    private Map<String, List<Double>> calculateSingleDeviationMap(
            String archive,
            LocalDate archiveLocal,
            List<LocalDate> endLocalDates,
            Map<String, Map<String, List<Double>>> dataMap,
            List<Double> baselineActualList,
            int n) {

        List<Double> actualList = dataMap.get(archive).get("actual");
        List<Double> forecastList = dataMap.get(archive).get("forecast");

        List<Double> devActualList = new ArrayList<>(n);
        List<Double> devForecastList = new ArrayList<>(n);

        for (int k = 0; k < n; k++) {
            Double baseVal = baselineActualList.get(k);

            if (archiveLocal.isAfter(endLocalDates.get(k)) || archiveLocal.equals(endLocalDates.get(k))) {
                Double val = actualList.get(k);
                devActualList.add(calculateDeviationValue(baseVal, val));
                devForecastList.add(null);
            } else {
                devActualList.add(null);
                Double fVal = forecastList.get(k);
                devForecastList.add(calculateDeviationValue(baseVal, fVal));
            }
        }

        Map<String, List<Double>> devSubMap = new LinkedHashMap<>();
        devSubMap.put("actual", devActualList);
        devSubMap.put("forecast", devForecastList);

        return devSubMap;
    }

    /**
     * 计算单个偏离度值
     * @param baseVal 基准值
     * @param val 比较值
     * @return 偏离度值
     */
    private Double calculateDeviationValue(Double baseVal, Double val) {
        if (baseVal == null || val == null || baseVal == 0) {
            return null;
        }
        BigDecimal base = BigDecimal.valueOf(baseVal);
        BigDecimal compare = BigDecimal.valueOf(val);
        BigDecimal deviation = base.subtract(compare).divide(base, 6, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100));
        return deviation.setScale(4, RoundingMode.HALF_UP).doubleValue();
    }

    /**
     * 获取产品的IRR指标结果
     * @param trustId 产品ID
     * @return 返回IRR指标结果
     */
    @Override
    public Map<String, Object> getIRRMetricsResult(Integer trustId) {
        logger.info("调用 getIRRMetricsResult 方法, trustId={}", trustId);
        Map<String, Object> finalResult = new HashMap<>();
        try {
            Map<String, String> trustDates = calculateTrustDates(trustId);
            logger.debug("calculateTrustDates 返回: {}", trustDates);
            if (trustDates == null) {
                logger.warn("calculateTrustDates 返回 null, trustId={}", trustId);
                return finalResult;
            }
            String trustStartDate = trustDates.get("startDate");
            String trustEndDate = trustDates.get("endDate");
            String repurchaseDate = trustDates.get("repurchaseDate");
            logger.debug("trustStartDate: {}, trustEndDate: {}, repurchaseDate: {}", trustStartDate, trustEndDate, repurchaseDate);
            List<Map<String, String>> result = riskAndReturnMetricsMapper.getIRRMetricsResult(trustId, trustStartDate, trustEndDate);
            logger.info("IRR 查询结果条数: {}", result != null ? result.size() : 0);
            if (result != null && !result.isEmpty()) {
                logger.debug("IRR 查询结果前3条: {}", result.subList(0, Math.min(3, result.size())));
            }

            logger.debug("准备调用 transformArchiveAndMetrics");
            finalResult = transformArchiveAndMetrics(result);
            logger.info("transformArchiveAndMetrics 处理后结果: {}", finalResult);

            // 计算获取实际IRR
            String actualIRR = "-";
            if (repurchaseDate != null && !repurchaseDate.isEmpty()) {
                Map<String, String> actualResult = riskAndReturnMetricsMapper.getActualIRROrRIR(trustId, repurchaseDate, "IRR");
                actualIRR = actualResult != null ? actualResult.get("metrics_result") : "-";
                logger.info("实际IRR: {}", actualIRR);
            }
            finalResult.put("actual_irr", actualIRR);
        } catch (Exception e) {
            logger.error(String.format("getIRRMetricsResult 执行异常, trustId=%s, error: %s", trustId, e.getMessage()));
        }
        return finalResult;
    }

    /**
     * 获取产品的RIR指标结果
     * @param trustId 产品ID
     * @return 返回RIR指标结果
     */
    @Override
    public Map<String, Object> getRIRMetricsResult(Integer trustId) {
        logger.info("调用 getRIRMetricsResult 方法, trustId={}", trustId);
        Map<String, Object> finalResult = new HashMap<>();
        try {
            Map<String, String> trustDates = calculateTrustDates(trustId);
            logger.debug("calculateTrustDates 返回: {}", trustDates);
            if (trustDates == null) {
                logger.warn("calculateTrustDates 返回 null, trustId={}", trustId);
                return finalResult;
            }
            String trustStartDate = trustDates.get("startDate");
            String trustEndDate = trustDates.get("endDate");
            logger.debug("trustStartDate: {}, trustEndDate: {}", trustStartDate, trustEndDate);
            List<Map<String, String>> result = riskAndReturnMetricsMapper.getRIRMetricsResult(trustId, trustStartDate, trustEndDate);
            logger.info("RIR 查询结果条数: {}", result != null ? result.size() : 0);
            if (result != null && !result.isEmpty()) {
                logger.debug("RIR 查询结果前3条: {}", result.subList(0, Math.min(3, result.size())));
            }
            logger.debug("准备调用 transformArchiveAndMetrics");
            finalResult = transformArchiveAndMetrics(result);
            logger.info("transformArchiveAndMetrics 处理后结果: {}", finalResult);

            // 计算获取实际RIR
            String actualRIR = "-";
            String repurchaseDate = trustDates.get("repurchaseDate");
            if (repurchaseDate!= null &&!repurchaseDate.isEmpty()) {
                Map<String, String> actualResult = riskAndReturnMetricsMapper.getActualIRROrRIR(trustId, repurchaseDate, "RIR");
                actualRIR = actualResult!= null? actualResult.get("metrics_result") : "-";
                logger.info("实际RIR: {}", actualRIR);
            }
            finalResult.put("actual_rir", actualRIR);
        } catch (Exception e) {
            logger.error(String.format("getRIRMetricsResult 执行异常, trustId=%s, error: %s", trustId, e.getMessage()));
        }
        return finalResult;
    }

    /**
     * 转换 archive_date 和 metrics_result 列表
     * @param result 查询结果
     * @return 包含归档日期和指标结果的Map
     */
    private Map<String, Object> transformArchiveAndMetrics(List<Map<String, String>> result) {
        List<String> archiveDates = result.stream()
                .map(row -> row.get("archive_date"))
                .collect(Collectors.toList());

        List<String> metricsResults = result.stream()
                .map(row -> {
                    String value = row.get("metrics_result");
                    if (value != null && !value.isEmpty()) {
                        BigDecimal bd = new BigDecimal(value);
                        return bd.multiply(new BigDecimal(100)).setScale(4, RoundingMode.HALF_UP).toString();
                    }
                    return value;
                })
                .collect(Collectors.toList());

        Map<String, Object> transformedResult = new HashMap<>();
        transformedResult.put("archive_date", archiveDates);
        transformedResult.put("metrics_result", metricsResults);

        return transformedResult;
    }

}
