import sys
import os
import urllib.parse
import traceback
import json
from PythonFiles import SQLParamsParser
import re

CONFIG_FOLDER_NAME = "biz_config"
DISTRIBUTION_FILE_NAME = "DistributionAdjustment.json"

def get_distribution_file_path(trust_id):
    """
    check if the config file for specific trust exists, if not, use the default config file
    """
    trust_config_file_path = os.path.join(
        os.path.dirname(__file__),
        "..",
        CONFIG_FOLDER_NAME,
        str(trust_id),
        DISTRIBUTION_FILE_NAME,
    )
    if os.path.exists(trust_config_file_path):
        file_path = trust_config_file_path
    else:
        file_path = os.path.join(
            os.path.dirname(__file__), "..", CONFIG_FOLDER_NAME, DISTRIBUTION_FILE_NAME
        )
    return file_path

def save_distribution_adjustment_config(trust_id, distribution_adjustment):
    # there are 2 elements in the config file, each element for a specific AssetCategory
    """
    distribution_adjustment example content:
    {
        "AssetSubCategory": {
            "original": [1, 1]
        },
        "LoanTerm": {
            "original": [1, 1]
        },
        "CreditRating": {
            "higher": [1.2, 0.8],
            "original": [1, 1],
            "lower": [0.8, 1.2]
        },
        "RemainingTerm": {
            "higher": [1.2, 0.8],
            "original": [1, 1],
            "lower": [0.8, 1.2]
        }
    }
    """
    # config file for specific trust is ../../config/{trust_id}/DistributionAdjustment.json
    trust_config_dir = os.path.join(
        os.path.dirname(__file__),
        "..",
        CONFIG_FOLDER_NAME,
        str(trust_id)
    )
    
    # Create directory if it doesn't exist
    if not os.path.exists(trust_config_dir):
        os.makedirs(trust_config_dir)
        
    trust_config_file_path = os.path.join(trust_config_dir, DISTRIBUTION_FILE_NAME)

    # 使用自定义格式化函数确保数组在同一行显示
    json_str = json.dumps(distribution_adjustment, indent=4, separators=(',', ': '))
    
    # 处理数组格式，将数组元素合并到同一行
    # 匹配格式为: [\n    x,\n    y\n] 替换为 [x, y]
    array_pattern = re.compile(r'\[\s+([\d\.]+),\s+([\d\.]+)\s+\]')
    json_str = array_pattern.sub(r'[\1, \2]', json_str)
    
    # 写入文件
    with open(trust_config_file_path, "w") as f:
        f.write(json_str)

    return 'success'

if __name__ == "__main__":
    try:
        arg = urllib.parse.unquote_plus(sys.argv[1])       
        arg = json.dumps(json.loads(arg)['Params'])
        params = SQLParamsParser.getParmas(arg)
        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 Analysis_Trust_DistributionConfigs_Save.py '{"Params":{"SPName":"Analysis_Trust_DistributionConfigs_Save","SQLParams":[{"Name":"TrustID","Value":"88","DBType":"int"},{"Name":"DistributionAdjustment","Value":"{\"AssetSubCategory\": {\"original\": [1, 1]}, \"LoanTerm\": {\"original\": [1, 1]}, \"CreditRating\": {\"higher\": [1.2, 0.8], \"original\": [1, 1], \"lower\": [0.8, 1.2]}, \"RemainingTerm\": {\"higher\": [1.2, 0.8], \"original\": [1, 1], \"lower\": [0.8, 1.2]}}}","DBType":"varchar"}]},"Method":"set","Conn":"FixedIncomeSuite"}'
        trust_id = params["TrustID"]
        # 检查DistributionAdjustment是否为字符串，如果是则需要解析成Python对象
        distribution_adjustment = params["DistributionAdjustment"]
        if isinstance(distribution_adjustment, str):
            # 添加异常处理并打印调试信息
            try:
                # 先检查字符串是否带有转义字符
                if distribution_adjustment.startswith('"') and distribution_adjustment.endswith('"'):
                    # 移除外层引号并解析JSON
                    distribution_adjustment = json.loads(distribution_adjustment[1:-1])
                else:
                    distribution_adjustment = json.loads(distribution_adjustment)
            except json.JSONDecodeError as e:
                # 处理可能的转义问题
                print(f"JSON解析错误: {str(e)}")
                print(f"尝试修复JSON字符串...")
                
                # 尝试解析经过转义处理的JSON字符串
                try:
                    # 移除外层引号和转义反斜杠
                    temp_str = distribution_adjustment.strip('"')
                    distribution_adjustment = json.loads(temp_str)
                except Exception as ex:
                    raise Exception(f"无法解析DistributionAdjustment参数: {str(ex)}\n原始值: {distribution_adjustment}")
            
        result = save_distribution_adjustment_config(trust_id, distribution_adjustment)
        
        print("$OUTPUT" + result)
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
