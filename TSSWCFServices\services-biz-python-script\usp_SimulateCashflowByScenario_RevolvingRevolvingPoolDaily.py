#coding=utf-8
import datetime
import sys
import urllib.parse
import traceback
import uuid
import pandas as pd
from PythonFiles import SQLParamsParser

'''
注意此函数不仅要对现金流进行叠加，还要对MOB进行叠加
'''
def func(params):
    revolving_amount = float(0)

    # 用df代替原有的 temp_scfd
    df_pool_cashflow = params["StressedCashflowDetails"]
    df_revolving_pool_cashflow = params["RevolvingPoolCashflowDetails"]

    df_pool_mob = params["PoolMOB"]
    df_revolving_pool_mob = params["RevolvingPoolMOB"]
    df_grouped_revolving_pool_mob = params["GroupedRevolvingPoolMOB"] if "GroupedRevolvingPoolMOB" in params.keys() else None
    df_adjusted_grouped_mob = None

    list_daily_revolving = params["DailyRevolving"]

    gap_reserve_amount = float(params["GapReserveAmount"])

    reserve_amount = float(params["ReserveAmount"])

    df_today = df_pool_cashflow[df_pool_cashflow['PayDate'] == params["RevolvingPurchaseDate"]]

    if not df_today.empty:
        today_sums = df_today[['Principal','Prepayment','Interest']].sum()
        available_amount = float(today_sums.sum())
    else:
        available_amount = float(0)

    #print("回收款 =", available_amount, ", AdditionalFund =", params["AdditionalFund"], ", RemainingAmount =", params["RemainingAmount"])

    # 可用金额 = 当期回收款 + 初始额外金额 + 上一日剩余金额
    available_amount += float(params["AdditionalFund"]) + float(params["RemainingAmount"])

    #print("当日可用金额为", available_amount, ", 其中顺延的可用金额为", params["AdditionalFund"])

    # 当日需储备金额 = 当日储备要求 + 前一日储备缺口
    reserved_amount = min(available_amount, reserve_amount + gap_reserve_amount)
    gap_reserve_amount = max(0, reserve_amount + gap_reserve_amount - reserved_amount)

    if "SkipPurchase" in params.keys() and params["SkipPurchase"] == True:
        # 如果此日为“不循环购买日”，则循环购买金额为0
        #print("非循环购买日")
        revolving_amount = float(0)
    else:
        # 如果此日为循环购买日，则循环购买金额为可用金额减去储备金额
        revolving_amount = available_amount - reserved_amount
        #print("循环购买金额 =", available_amount, "-", reserved_amount, "=", revolving_amount)

    # 去除储备金额后，剩余金额顺延至下一日
    available_amount = available_amount - reserved_amount - revolving_amount

    #print("扣除ReserveAmount和Gap之后的可用于循环购买金额:", revolving_amount)
    revolving_cpb = float(0)

    asset_value = float(params["AssetValue"])

    total_collection = None
    interest_collection = None
    return_rate = None
    interest_return_rate = None
    profit = None
    revolving_cashflow_length = None

    if revolving_amount > 0:
        # 若有循环覆盖率（CoverageRate），例如0.985，则100元循环购买金额可购买98.5元资产余额
        if params["DiscountRate"] is not None:
            params["Factor"] = revolving_amount / asset_value * float(params["DiscountRate"])
            #print("Factor =", revolving_amount, "/", params["AssetValue"], "*", params["DiscountRate"], "=", params["Factor"])
        else:
            params["Factor"] = revolving_amount / asset_value
            #print("Factor =", revolving_amount, "/", params["AssetValue"], "=", params["Factor"])

        # 循环购买入池本金
        revolving_cpb = float(params["AssetPrincipal"]) * params["Factor"]
        #print("可用金额:", revolving_amount, "循环购买入池本金:", revolving_cpb, "导入现金流价值:", params["AssetValue"], "循环覆盖率:", params["DiscountRate"], "现金流缩放倍数:", params["Factor"], "偏移天数:", params["DaysShift"])

        params["AccountNo"] = str(uuid.uuid1())

        multiplier = params["Factor"]

        # 首先处理循环购买现金流
        df_adjusted = df_revolving_pool_cashflow.copy()
        df_adjusted['PayDate'] = df_adjusted['PayDate'] + pd.to_timedelta(params["DaysShift"], unit='D')

        df_adjusted["RevolvingPurchaseID"] = params["RevolvingPurchaseID"]
        revolving_cashflow_length = df_adjusted.shape[0]

        columns_to_adjust = [
            'Principal', 'Interest', 'DefaultPrincipal', 'Prepayment', 'RecoveryPrincipal',
            'CumulativeDefault', 'OpeningBalance', 'InterestPenalty', 'CumulativePrepayment',
            'DefaultRateBase', 'PrepaymentRateBase', 'RecoveryRateBase'
        ]

        df_adjusted[columns_to_adjust] = df_adjusted[columns_to_adjust] * multiplier

        df_adjusted['LoanAmount'] = revolving_amount

        df_pool_cashflow = pd.concat([df_pool_cashflow, df_adjusted])

        # 获取回收款总额
        collection_sums = df_adjusted[['Principal', 'Prepayment', 'Interest']].sum()
        total_collection = collection_sums['Principal'] + collection_sums['Prepayment'] + collection_sums['Interest']
        interest_collection = collection_sums['Interest']

        return_rate = total_collection / revolving_amount
        interest_return_rate = interest_collection / revolving_amount
        profit = total_collection - revolving_amount
        #print("无截断, 此日循环购买回收率为", return_rate, "利息回收率为", interest_return_rate)

        # 然后处理循环购买MOB
        df_adjusted_mob = df_revolving_pool_mob.copy()
        df_adjusted_mob["PayDate"] = df_adjusted_mob["PayDate"] + pd.to_timedelta(params["DaysShift"], unit='D')
        df_adjusted_mob["RevolvingPurchaseID"] = params["RevolvingPurchaseID"]

        columns_to_adjust_mob = [
            'M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus'
        ]

        df_adjusted_mob[columns_to_adjust_mob] = df_adjusted_mob[columns_to_adjust_mob] * multiplier

        df_pool_mob = pd.concat([df_pool_mob, df_adjusted_mob])

        # 处理循环购买分组MOB
        if df_grouped_revolving_pool_mob is not None:
            df_adjusted_grouped_mob = df_grouped_revolving_pool_mob.copy()

            df_adjusted_grouped_mob["PayDate"] = df_adjusted_grouped_mob["PayDate"] + pd.to_timedelta(params["DaysShift"], unit='D')
            df_adjusted_grouped_mob["RevolvingPurchaseID"] = params["RevolvingPurchaseID"]

            df_adjusted_grouped_mob[columns_to_adjust_mob] = df_adjusted_grouped_mob[columns_to_adjust_mob] * multiplier

            # do not concat grouped mob here
            #df_grouped_pool_mob = pd.concat([df_grouped_pool_mob, df_adjusted_grouped_mob])

    list_daily_revolving.append({
        'RevolvingDate': params["RevolvingPurchaseDate"],
        'RevolvingAmount': revolving_amount,
        'ReserveAmountGap': gap_reserve_amount,
        'RevolvingCPB': revolving_cpb,
        'ReservedAmount': reserved_amount,
        'TotalCollection': total_collection,
        'InterestCollection': interest_collection,
        'ReturnRate': return_rate,
        'InterestReturnRate': interest_return_rate,
        'Profit': profit,
        'RevolvingCashflowLength': revolving_cashflow_length,
        'PlanID': params["PlanID"]
    })

    #print(" df_pool_cashflow现在共有", df_pool_cashflow.shape[0], "条现金流, 共有", df_pool_cashflow["RevolvingPurchaseID"].nunique(), "个RevolvingPurchaseID")
    #print(" list_daily_revolving现在共有", len(list_daily_revolving), "条循环购买记录")

    return gap_reserve_amount, available_amount, df_pool_cashflow, df_pool_mob, df_adjusted_grouped_mob

if __name__ == '__main__' :
    try:
        str_arg = ''''''
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]

        arg = urllib.parse.unquote_plus(str_arg)
        params = SQLParamsParser.getParmas(arg)
        result = func(params)
        print('$OUTPUT' + result)
    except Exception as e:
        print('$ERROR', e, traceback.format_exc())
