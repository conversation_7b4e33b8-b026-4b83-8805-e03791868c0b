#coding=utf-8

from decimal import Decimal
import pandas as pd
import datetime
from supporting_functions import get_curve_data_by_group
from usp_SimulatePayments_DailyTransition import func as usp_SimulatePayments_DailyTransition

def func(params):
    print("==== 美团循环池分池加压 ====")

    if "UseFirstRevolvingPlan" not in params.keys() or params["UseFirstRevolvingPlan"] == False:
        raise Exception("若采用分池测算, 则'使用第一个循环池方案'选项必须为True")

    trust_id = params["TrustID"]

    max_collection_date = params["MaxCollectionDate"]

    prediction_final_date = params["PredictionFinalDate"] if "PredictionFinalDate" in params else None

    if prediction_final_date is None or prediction_final_date < max_collection_date:
        prediction_final_date = max_collection_date

    simulation_start_date = params["SimulationStartDate"]

    dict_all_curves = params["AllStressCurves"]
    
    amortisation_curves = params["RevolvingPoolAmortisationCurves"]

    # 遍历循环池分组
    asset_groups = params["RevolvingPoolAssetGroups"]

    if len(asset_groups) == 0:
        raise Exception("循环池分池现金流表daily_rev_plan_cashflows_grouped中未找到资产组")
    else:
        print("循环池分组数量为", len(asset_groups))

    time1 = datetime.datetime.now()

    dict_casflow_details = {}
    dict_mob_details = {}

    # 记录循环池的分组MOB，用于叠加计算任意日期每个group的MOB
    dict_grouped_mob_details = {}

    max_revolving_pool_length = params["MaxRevolvingPoolLength"]
    min_revolving_pool_length = params["MinRevolvingPoolLength"]

    df_grouped_cashflow_details = None

    # store curve data from get_curve_data_by_group in a dictionary (key = group_id_to_match, value is a tuple for default_curve_data, prepayment_curve_data, m1_curve_data, m2_curve_data, m3_curve_data, m4_curve_data, m5_curve_data, m6_curve_data)
    # so we don't have to call it multiple times
    dict_curves_for_group_cache = {}
    dict_amortisation_curve_cache = {}

    part1time = Decimal(0)
    part2time = Decimal(0)

    part_a_time = Decimal(0)
    part_b_time = Decimal(0)
    part_c_time = Decimal(0)
    part_d_time = Decimal(0)

    part_a1_time = Decimal(0)
    part_a2_time = Decimal(0)
    part_a3_time = Decimal(0)
    part_a4_time = Decimal(0)
    part_a5_time = Decimal(0)

    dict_cashflow_length_irr = {}

    # iterate over cashflow_length from max_cashflow_length to min_cashflow_length
    for cashflow_length in range(max_revolving_pool_length, min_revolving_pool_length - 1, -1):
        print("开始模拟", cashflow_length, "个月的循环池现金流")
        time2_1 = datetime.datetime.now()

        list_payments_all = []
        list_mob_all = []

        dict_group_seasoning = {}

        for asset_group in asset_groups:
            group_id = str(asset_group[0])
            #print("group_id =", group_id)
            # 资产的group_id构成方式与于静态池相同，但多了账龄维度，例如：1_2_1_1_1
            # 1：一级分类
            # 2：二级分类
            # 3：风险分组
            # 4：合同期限
            # 5：账龄
            loan_amount = asset_group[1]
            performing_loan_balance = asset_group[2]
            seasoning = asset_group[3]
            remaining_term = asset_group[4]
            cumulative_default = 0

            dict_group_seasoning[group_id] = seasoning

            # 从group_id中去掉账龄维度，与曲线group_id匹配
            # 例如：1_2_1_1_1 -> 1_2_1_1
            group_id_to_match = "_".join(group_id.split("_")[:-1])

            time_a3_start = datetime.datetime.now()
            # 通过group_id获取该分组的曲线数据
            if dict_curves_for_group_cache.get(group_id_to_match) is None:
                default_curve_data, prepayment_curve_data, m1_curve_data, m2_curve_data, m3_curve_data, m4_curve_data, m5_curve_data, m6_curve_data = get_curve_data_by_group(dict_all_curves, group_id_to_match)
                dict_curves_for_group_cache[group_id_to_match] = (default_curve_data, prepayment_curve_data, m1_curve_data, m2_curve_data, m3_curve_data, m4_curve_data, m5_curve_data, m6_curve_data)
            else:
                default_curve_data, prepayment_curve_data, m1_curve_data, m2_curve_data, m3_curve_data, m4_curve_data, m5_curve_data, m6_curve_data = dict_curves_for_group_cache[group_id_to_match]
                
            time_a3_end = datetime.datetime.now()
            part_a3_time += Decimal((time_a3_end - time_a3_start).total_seconds())

            # print("DefaultCurve=", default_curve_data)
            # print("PrepaymentCurve=", prepayment_curve_data)

            # print("M1Transition=", m1_curve_data)
            # print("M2Transition=", m2_curve_data)
            # print("M3Transition=", m3_curve_data)
            # print("M4Transition=", m4_curve_data)
            # print("M5Transition=", m5_curve_data)
            # print("M6Transition=", m6_curve_data)

            # 该分池的摊还表，包括：期数、日期、应还本金、应还利息、余额、摊还比率、有效利率（应还利息/余额）
            amortisation_curve = dict_amortisation_curve_cache.get(group_id) 
            
            if amortisation_curve is None:
                amortisation_curve = [curve for curve in amortisation_curves if curve[0] == group_id]
                dict_amortisation_curve_cache[group_id] = amortisation_curve

            time_a4_end = datetime.datetime.now()
            part_a4_time += Decimal((time_a4_end - time_a3_end).total_seconds())

            # 现在根据cashflow_length对还款计划进行截取
            # cashflow_length 是需要从现金流尾部截取的月数
            cashflow_length_days = cashflow_length * 30
            if cashflow_length_days < remaining_term:
                # 从 amortisation_curve 尾部截取 cashflow_length_days 天的数据
                amortisation_curve = amortisation_curve[-cashflow_length_days:]
                cut_days = remaining_term - cashflow_length_days
                remaining_term = cashflow_length_days
                seasoning += cut_days
                #print("经缩短后, 该分组的剩余期限为", remaining_term, "天，账龄为", seasoning, "天")
            else:
                #print("无需缩短循环池现金流")
                pass
                # loan_amount, performing_loan_balance, cumulative_default 不变

            time_x1 = datetime.datetime.now()
            part_a5_time += Decimal((time_x1 - time_a4_end).total_seconds())

            payments, mob = usp_SimulatePayments_DailyTransition(params={
                "TrustID": trust_id,
                "AccountNo": group_id,
                "AmortisationCurve": amortisation_curve,
                "LoanAmount": loan_amount,
                "PerformingLoanBalance": performing_loan_balance,
                "CDR": default_curve_data,
                "CPR": prepayment_curve_data,
                "RemainingTerm": remaining_term,
                "Seasoning": seasoning,
                "CumulativeDefault": cumulative_default,
                "M1Transition": m1_curve_data,
                "M2Transition": m2_curve_data,
                "M3Transition": m3_curve_data,
                "M4Transition": m4_curve_data,
                "M5Transition": m5_curve_data,
                "M6Transition": m6_curve_data,
                "UsePerformingLoanBalance": params.get("UsePerformingLoanBalance", True)
            })
            time_x2 = datetime.datetime.now()
            part_a1_time += Decimal((time_x2 - time_x1).total_seconds())

            # # Get total principal+prepayment from payments
            # total_principal_prepayment = sum([payment[2] + payment[4] for payment in payments])
            # print("分组", group_id, "的现金流总和为", total_principal_prepayment, "占剩余本金的比例为", total_principal_prepayment / performing_loan_balance)

            # columns of mob = ['AccountNo', 'PeriodId', 'M0', 'M1', 'M2', 'M3', 'M4', 'M5', 'M6', 'M6plus']
            # columns of payments = ['AccountNo', 'PeriodId', 'Principal', 'Interest', 'Prepayment', 'DefaultPrincipal', 'RecoveryPrincipal', 'CumulativeDefault', 'CumulativePrepayment', 'OpeningBalance', 'DefaultRateBase', 'PrepaymentRateBase', 'LoanAmount']
            list_payments_all.extend(payments)

            # mob is a list, not a dataframe
            # e.g. mob = []
            # mob.append((account_no, 0, performing_loan_balance, m1, m2, m3, m4, m5, m6, m6_plus))
            last_m6_plus = mob[-1][-1]
            last_period_id = mob[-1][1]
            last_date = simulation_start_date + datetime.timedelta(days=last_period_id - 1)
            days_to_fill = (prediction_final_date - last_date).days
            # fill more data into list mob until the last collection date
            if days_to_fill > 0:
                mob.extend([(group_id, last_period_id + i, 0, 0, 0, 0, 0, 0, 0, last_m6_plus) for i in range(1, days_to_fill + 1)])

            list_mob_all.extend(mob)
            time_x3 = datetime.datetime.now()
            part_a2_time += Decimal((time_x3 - time_x2).total_seconds())

        time2_2 = datetime.datetime.now()
        #print("[CPU] 循环池分池现金流模拟完成，总用时", (time2_2 - time2_1).total_seconds(), "秒")
        part1time += Decimal((time2_2 - time2_1).total_seconds())

        # # Get total principal+prepayment from list_payments_all
        # total_principal_prepayment = sum([payment[2] + payment[4] for payment in list_payments_all])
        # print("所有分组的现金流总和为", total_principal_prepayment)

        #print("保存Analysis_GroupedStressedCashflowDetails")
        part_a_start = datetime.datetime.now()

        # create dataframes from list_payments_all and list_mob_all
        df_payments_all = pd.DataFrame(list_payments_all, columns=["GroupID", "PeriodID", "Principal", "Interest", "Prepayment", "DefaultPrincipal", "RecoveryPrincipal", "CumulativeDefault", "CumulativePrepayment", "OpeningBalance", "DefaultRateBase", "PrepaymentRateBase", "LoanAmount", "PrepaymentRate", "DefaultRate", "rate_position"])

        # convert "Principal", "Interest", "Prepayment", "DefaultPrincipal", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "DefaultRateBase", "PrepaymentRateBase", "LoanAmount" to 2 decimal places
        # df_payments_all[["Principal", "Interest", "Prepayment", "DefaultPrincipal", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "DefaultRateBase", "PrepaymentRateBase", "LoanAmount"]] = df_payments_all[["Principal", "Interest", "Prepayment", "DefaultPrincipal", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "DefaultRateBase", "PrepaymentRateBase", "LoanAmount"]].applymap(lambda x: round(x, 2))
        df_payments_all = df_payments_all.round(4)
        # df_payments_all[["Principal", "Interest", "Prepayment", "DefaultPrincipal", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "DefaultRateBase", "PrepaymentRateBase", "LoanAmount"]] = df_payments_all[["Principal", "Interest", "Prepayment", "DefaultPrincipal", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "DefaultRateBase", "PrepaymentRateBase", "LoanAmount"]].round(2)
        
        df_payments_all["PayDate"] = df_payments_all["PeriodID"].apply(lambda x: simulation_start_date + datetime.timedelta(days=x - 1))
        df_payments_all["SessionID"] = params["SessionID"]
        df_payments_all["ScenarioID"] = params["ScenarioID"]
        df_payments_all["RevolvingPurchaseID"] = -2
        df_payments_all["TrustID"] = trust_id

        if cashflow_length == max_revolving_pool_length:
            # only keep the columns we need for Analysis_GroupedStressedCashflowDetails
            df_grouped_cashflow_details = df_payments_all[["SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "GroupID", "PeriodID", "PayDate", "Principal", "Interest", "DefaultPrincipal", "Prepayment", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "LoanAmount", "PrepaymentRate", "DefaultRate", "rate_position", "DefaultRateBase", "PrepaymentRateBase"]]

            # print("向Analysis_GroupedStressedCashflowDetails写入", df_to_db.shape[0], "行数据")
            # MysqlAdapter.dataframe_tosql(df_to_db, "Analysis_GroupedStressedCashflowDetails")

        part_a_end = datetime.datetime.now()
        part_a_time += Decimal((part_a_end - part_a_start).total_seconds())

        # 保存分池MOB明细
        part_b_start = datetime.datetime.now()
        #print("保存Analysis_GroupedArrearsDetails")
        df_mob_all = pd.DataFrame(list_mob_all, columns=["GroupID", "PeriodID", "M0", "M1", "M2", "M3", "M4", "M5", "M6", "M6plus"])

        df_mob_all = df_mob_all.round(4)

        group_seasoning_series = pd.Series(dict_group_seasoning)
        # 使用映射和向量加法
        df_mob_all["seasoning"] = group_seasoning_series.loc[df_mob_all["GroupID"]].values + df_mob_all["PeriodID"]

        # # 为mob添加seasoning列，每个行数据的seasoning等于该分组的seasoning + PeriodID
        # df_mob_all["seasoning"] = df_mob_all.apply(
        #     lambda row: dict_group_seasoning[row["GroupID"]] + row["PeriodID"], 
        #     axis=1
        # )
        df_mob_all["PayDate"] = pd.to_datetime(simulation_start_date) + pd.to_timedelta(df_mob_all["PeriodID"] - 1, unit='D')
        # df_mob_all["PayDate"] = df_mob_all["PeriodID"].apply(lambda x: simulation_start_date + datetime.timedelta(days=x - 1))  
        df_mob_all["SessionID"] = params["SessionID"]
        df_mob_all["ScenarioID"] = params["ScenarioID"]
        df_mob_all["RevolvingPurchaseID"] = -2
        df_mob_all["TrustID"] = trust_id

        # 记录每个长度的循环池的分组MOB情况
        df_grouped_mob = df_mob_all[["SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "GroupID", "PeriodID", "PayDate", "seasoning", "M0", "M1", "M2", "M3", "M4", "M5", "M6", "M6plus"]]
        dict_grouped_mob_details[cashflow_length] = df_grouped_mob

        part_b_end = datetime.datetime.now()
        part_b_time += Decimal((part_b_end - part_b_start).total_seconds())

        #print("汇总循环池加压明细数据")
        part_c_start = datetime.datetime.now()
        max_pay_date = df_payments_all["PayDate"].max()
        date_range_end = max(max_pay_date, max_collection_date)
        date_range = pd.date_range(start=simulation_start_date, end=date_range_end, freq='D')

        # construnct a dataframe with date_range as "PayDate", and PeriodId as ROW_NUMBER() over (order by "PayDate")
        date_range = pd.DataFrame({"PayDate": date_range})
        date_range["PeriodID"] = date_range.index + 1

        # df_date_range left join df_payments_all on PeriodId
        df_results = date_range.merge(df_payments_all, on="PeriodID", how="left")
        df_results["PayDate"] = df_results["PayDate_x"]
        df_results = df_results.drop(columns=["PayDate_x", "PayDate_y"])

        df_results = df_results.groupby(["PayDate", "PeriodID"]).agg({
            "Principal": "sum",
            "Interest": "sum",
            "DefaultPrincipal": "sum",
            "Prepayment": "sum",
            "RecoveryPrincipal": "sum",
            "CumulativeDefault": "sum",
            "OpeningBalance": "sum",
            "CumulativePrepayment": "sum",
            "DefaultRateBase": "sum",
            "PrepaymentRateBase": "sum",
            "LoanAmount": "sum"
        }).reset_index()

        df_results["AccountNo"] = "AllAssets"
        df_results["SessionID"] = params["SessionID"]
        df_results["TrustID"] = trust_id
        df_results["ScenarioID"] = params["ScenarioID"]
        df_results["RevolvingPurchaseID"] = -2
        df_results["InterestPenalty"] = 0
        df_results["RecoveryRateBase"] = 0

        initial_opening_balance = df_results.loc[0, "OpeningBalance"]
        initial_cumulative_default = df_results.loc[0, "CumulativeDefault"]
        initial_cumulative_prepayment = df_results.loc[0, "CumulativePrepayment"]

        # Vectorized calculation for OpeningBalance
        df_results["OpeningBalance"] = (
            df_results["OpeningBalance"].shift(1)
            - df_results["Principal"].shift(1)
            - df_results["Prepayment"].shift(1)
        )

        # Vectorized calculation for CumulativeDefault
        df_results["CumulativeDefault"] = (
            df_results["CumulativeDefault"].shift(1)
            + df_results["DefaultPrincipal"]
            - df_results["RecoveryPrincipal"]
        )

        # Vectorized calculation for CumulativePrepayment
        df_results["CumulativePrepayment"] = (
            df_results["CumulativePrepayment"].shift(1) + df_results["Prepayment"]
        )

        # Fill the first row with initial values
        df_results.loc[0, "OpeningBalance"] = initial_opening_balance
        df_results.loc[0, "CumulativeDefault"] = initial_cumulative_default
        df_results.loc[0, "CumulativePrepayment"] = initial_cumulative_prepayment

        # only keep the columns we need for Analysis_StressedCashflowDetails
        df_casflow_details = df_results[["SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "AccountNo", "PeriodID", "PayDate", "Principal", "Interest", "DefaultPrincipal", "Prepayment", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "InterestPenalty", "CumulativePrepayment", "DefaultRateBase", "PrepaymentRateBase", "RecoveryRateBase", "LoanAmount"]]
        
        #print(df_casflow_details.head(30))

        # 计算模拟现金流的XIRR，这样我们可以观察不同长度的现金流的XIRR是否接近一致
        xirr = calculate_xirr(df_casflow_details, initial_opening_balance, simulation_start_date)

        # 计算模拟现金流的总本金、总利息、总收益、总违约
        total_principal = df_casflow_details["Principal"].sum()
        total_prepayment = df_casflow_details["Prepayment"].sum()
        total_interest = df_casflow_details["Interest"].sum()
        total_collection = total_principal + total_prepayment + total_interest
        total_default = df_casflow_details["DefaultPrincipal"].sum()

        dict_cashflow_length_irr[cashflow_length] = (xirr, total_principal, total_prepayment, total_interest, total_collection, total_default)
        
        print("长度为", cashflow_length, "的现金流的XIRR:", xirr, "总本金:", total_principal, "总利息:", total_interest, "总收益:", total_collection, "总违约:", total_default)

        part_c_end = datetime.datetime.now()
        part_c_time += Decimal((part_c_end - part_c_start).total_seconds())

        #print("汇总MOB明细数据:", datetime.datetime.now())
        part_d_start = datetime.datetime.now()
        # date_range left join df_mob_all on PeirodId, and sum the columns
        df_results = date_range.merge(df_mob_all, on="PeriodID", how="left")
        df_results["PayDate"] = df_results["PayDate_x"]
        df_results = df_results.drop(columns=["PayDate_x", "PayDate_y"])

        df_results = df_results.groupby(["PayDate", "PeriodID"]).agg({
            "M0": "sum",
            "M1": "sum",
            "M2": "sum",
            "M3": "sum",
            "M4": "sum",
            "M5": "sum",
            "M6": "sum",
            "M6plus": "sum"
        }).reset_index()

        df_results["AccountNo"] = "AllAssets"
        df_results["SessionID"] = params["SessionID"]
        df_results["TrustID"] = trust_id
        df_results["ScenarioID"] = params["ScenarioID"]
        df_results["RevolvingPurchaseID"] = -2
        df_results["RepurchaseValue"] = 0

        # only keep the columns we need for Analysis_StressedCashflowArrearsDetails
        df_mob_details = df_results[["SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "AccountNo", "PeriodID", "PayDate", "M0", "M1", "M2", "M3", "M4", "M5", "M6", "M6plus", "RepurchaseValue"]]

        part_d_end = datetime.datetime.now()
        part_d_time += Decimal((part_d_end - part_d_start).total_seconds())

        dict_casflow_details[cashflow_length] = df_casflow_details
        dict_mob_details[cashflow_length] = df_mob_details

        time2_3 = datetime.datetime.now()
        part2time += Decimal((time2_3 - time2_2).total_seconds())

    time3 = datetime.datetime.now()
    print("循环池分池测算完成，总用时", (time3 - time1).total_seconds(), "秒")
    print("其中，现金流模拟用时", part1time, "秒, 现金流模拟结果处理用时", part2time, "秒")
    print("part_a_time:", part_a_time)
    print("part_b_time:", part_b_time)
    print("part_c_time:", part_c_time)
    print("part_d_time:", part_d_time)

    # display dict_cashflow_length_irr
    print("循环池长度与XIRR的对应关系:")
    for key, value in dict_cashflow_length_irr.items():
        print(key, ":", value)

    return dict_casflow_details, dict_mob_details, df_grouped_cashflow_details, dict_grouped_mob_details

def calculate_xirr(df_casflow_details, initial_opening_balance, simulation_start_date):
    """
    计算模拟现金流的XIRR（内部收益率）
    
    参数：
    df_casflow_details: 包含现金流明细的DataFrame
    initial_opening_balance: 初始投入金额
    simulation_start_date: 初始投入日期
    
    返回：
    xirr: 年化内部收益率，以小数形式（如0.05表示5%）
    """
    import datetime  # 确保导入datetime模块
    
    # 准备现金流数据
    # 投入金额为负数（表示支出）
    cashflows = [-float(initial_opening_balance)]
    
    # 统一将日期转换为datetime.date类型
    if hasattr(simulation_start_date, 'to_pydatetime'):
        # pandas Timestamp -> datetime.date
        dates = [simulation_start_date.to_pydatetime().date()]
    elif isinstance(simulation_start_date, datetime.datetime):
        # datetime.datetime -> datetime.date
        dates = [simulation_start_date.date()]
    else:
        # 假设已经是datetime.date
        dates = [simulation_start_date]
    
    # 收入现金流为正数
    for _, row in df_casflow_details.iterrows():
        cash_in = float(row["Principal"] + row["Prepayment"] + row["Interest"] + row["InterestPenalty"])
        if cash_in != 0:  # 只考虑非零现金流
            cashflows.append(cash_in)
            
            # 统一将日期转换为datetime.date类型
            pay_date = row["PayDate"]
            if hasattr(pay_date, 'to_pydatetime'):
                # pandas Timestamp -> datetime.date
                dates.append(pay_date.to_pydatetime().date())
            elif isinstance(pay_date, datetime.datetime):
                # datetime.datetime -> datetime.date
                dates.append(pay_date.date())
            else:
                # 假设已经是datetime.date
                dates.append(pay_date)
    
    # 如果只有初始投入，没有后续现金流，无法计算XIRR
    if len(cashflows) <= 1:
        return 0
    
    # 将日期转换为距离初始日期的天数
    days = []
    for date in dates:
        # 日期相减计算天数
        delta = date - dates[0]
        days.append(delta.days)
    
    # 定义净现值(NPV)计算函数
    def npv(rate):
        """计算现金流的净现值"""
        return sum([cf / (1 + rate) ** (d / 365.0) for cf, d in zip(cashflows, days)])
    
    # 定义导数计算函数，用于牛顿法迭代
    def npv_derivative(rate):
        """计算现金流净现值对利率的导数"""
        return sum([-d/365.0 * cf / (1 + rate) ** (d/365.0 + 1) for cf, d in zip(cashflows, days)])
    
    # 使用牛顿迭代法求解XIRR
    # 初始猜测值为10%
    rate = 0.1
    max_iterations = 100
    precision = 1e-6
    
    for _ in range(max_iterations):
        # 计算当前利率下的净现值
        value = npv(rate)
        
        # 如果净现值足够接近0，则找到了解
        if abs(value) < precision:
            return rate
        
        # 计算导数
        derivative = npv_derivative(rate)
        
        # 防止除以0
        if abs(derivative) < precision:
            break
        
        # 牛顿迭代公式
        new_rate = rate - value / derivative
        
        # 检查是否收敛
        if abs(new_rate - rate) < precision:
            return new_rate
        
        # 更新利率
        rate = new_rate
        
        # 避免发散
        if rate <= -1 or rate > 100:
            break
    
    # 如果牛顿法不收敛，使用二分法作为备选
    if abs(npv(rate)) > precision:
        # 初始化搜索区间
        r_low, r_high = -0.99, 1.0
        
        # 确保区间两端的NPV值符号相反
        if npv(r_low) * npv(r_high) > 0:
            # 如果没有根在搜索区间内，返回近似值
            if abs(npv(r_low)) < abs(npv(r_high)):
                return r_low
            else:
                return r_high
        
        # 执行二分法迭代
        for _ in range(max_iterations):
            r_mid = (r_low + r_high) / 2
            npv_mid = npv(r_mid)
            
            if abs(npv_mid) < precision:
                return r_mid
            
            if npv_mid * npv(r_low) < 0:
                r_high = r_mid
            else:
                r_low = r_mid
                
            if r_high - r_low < precision:
                return r_mid
    
    return rate