import sys
import urllib.parse
import traceback
import uuid
from PythonFiles import MysqlAdapter2Java as MysqlAdapter, SQLParamsParser

CONST_STR = "生成于静态池(ID = "


def func(params):
    with_regression = params.get("WithRegression", False)
    default_rate_regression_results = params.get("DefaultRateRegressionResults", None)
    prepayment_rate_regression_results = params.get("PrepaymentRateRegressionResults", None)

    params["GUID"] = str(uuid.uuid1())

    if params["SelectedDates"] == "":
        params["SelectedDates"] = "space"

    sql = """
        select sum(LoanAmount) from (
            select date_format(LoanStartDate, '%Y%m%d') as StartDate, max(LoanAmount) as LoanAmount
            from dbo_StaticPoolRawData
            where StaticPoolID = @StaticPoolID
            group by LoanStartDate
        ) x
        where if('@SelectedDates'='space','','@SelectedDates') = ''
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["sum"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select DATE_FORMAT(max(LoanStartDate), '%Y%m')
        from dbo_StaticPoolRawData
        where StaticPoolID = @StaticPoolID;
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["MaxLoanStartMonth"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    if params["Section"] == "[ArrearsRate1_30]":
        # 违约率曲线
        params["CurveTypeCode"] = "DefaultRate"
        params["vCurveType"] = "违约率曲线"
        params["CurveName"] = (
            "违约_" + str(params["StaticPoolID"]) + "_" + params["Section"]
        )
        params["CurveDesc"] = ""
        params["CurveLength"] = 0

        sql = """
            select count(0)
            from dbo_StaticPoolProcessedData
            where StaticPoolID = @StaticPoolID and Section = '@Section' and StartDate <> ObservationDate and ArrearsRate is not null
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        isexists = MysqlAdapter.commonExecuteGetData(sql)[0][0]

        if isexists > 0:
            params["CurveDesc"] = CONST_STR + str(params["StaticPoolID"]) + ")"

            if with_regression and default_rate_regression_results is not None:
                print("违约率曲线使用宏观因素回归结果")
                '''
                    default_rate_regression_results = {
                        "default_rate_curve": default_rate_curve,
                        "trend_factor": trend_factor,
                        "springfestival_factor": springfestival_factor,
                        "nationalday_factor": nationalday_factor,
                        "base_date": base_date
                    }                
                '''
                default_rate_curve = default_rate_regression_results["default_rate_curve"]

                params["CurveLength"] = len(default_rate_curve)
                # 将曲线转换为逗号连接的字符串，注意不要使用科学计数法
                # 如果保留8位小数后为0（例如0.00000000），则直接用'0'
                params["CurveData"] = ','.join(["0" if x == 0 or x < 1e-8 else f"{x:.8f}" for x in default_rate_curve])

            else:
                sql = """
                    select count(1) from (
                        select  s.PeriodId, sum(s.ArrearsRate * a.LoanAmount) / sum(a.LoanAmount)  as WARate
                        from (
                            select StartDate, ArrearsRate, ROW_NUMBER() over (partition by StartDate order by ObservationDate) as PeriodId
                            from dbo_StaticPoolProcessedData
                            where StaticPoolID = @StaticPoolID and Section = '@Section'
                        ) s
                        inner join (
                            select date_format(LoanStartDate, '%Y%m%d') as StartDate, max(LoanAmount) as LoanAmount
                            from dbo_StaticPoolRawData
                            where StaticPoolID = @StaticPoolID
                            group by LoanStartDate
                        ) a on s.StartDate = a.StartDate where if('@SelectedDates'='space','','@SelectedDates') = ''
                        group by s.PeriodId
                    ) x;
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                params["CurveLength"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]
                sql = """
                    select group_concat(WARate separator ',') from (
                        select  s.PeriodId, sum(s.ArrearsRate * a.LoanAmount) / sum(a.LoanAmount)  as WARate
                        from (
                            select StartDate, ArrearsRate, ROW_NUMBER() over (partition by StartDate order by ObservationDate) as PeriodId
                            from dbo_StaticPoolProcessedData
                            where StaticPoolID = @StaticPoolID and Section = '@Section'
                        ) s
                        inner join (
                            select date_format(LoanStartDate, '%Y%m%d') as StartDate
                            , max(LoanAmount) as LoanAmount
                            from dbo_StaticPoolRawData
                            where StaticPoolID = @StaticPoolID
                            group by LoanStartDate
                        ) a on s.StartDate = a.StartDate where if('@SelectedDates'='space','','@SelectedDates') = ''
                        group by s.PeriodId
                        order by s.PeriodId
                    ) x;
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                params["CurveData"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

            sql = """
                select IFNULL(max(ID),0) from dbo_Curves where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
            """
            sql = MysqlAdapter.prepareSql(sql, params)

            params["CurveID"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]
            if params["CurveID"] == 0:
                sql = """
                    insert into dbo_Curves(CurveTypeCode, CurveType, CurveName, CurveLength, CurveData, Description)
                    values('@CurveTypeCode', '@vCurveType', '@CurveName', '@CurveLength', '@CurveData', '@CurveDesc');

                    select last_Insert_id();
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                params["CurveID"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

            else:
                sql = """
                    update dbo_Curves
                    set CurveData = '@CurveData', CurveLength='@CurveLength'
                    where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

            # 静态池的属性即为曲线的属性
            # 静态池的属性需要包括group_id，因此曲线属性也包括group_id
            # 在分池加压匹配中，分池现金流的group_id与曲线的group_id进行匹配
            sql = """
                delete from dbo_CurveExtension where CurveID = @CurveID$

                insert into dbo_CurveExtension(CurveID, StaticPoolID, Section, Attributes)
                values(@CurveID, @StaticPoolID, '@Section', concat('StaticPoolID:', '@StaticPoolID', ';Section:', '@Section'))$

                delete from dbo_CurveAttributes where CurveID = @CurveID$

                insert into dbo_CurveAttributes(CurveID, AttributeName, AttributeValue)
                select @CurveID, a.AttributeName, a.AttributeValue
                from dbo_CurveExtension e
                inner join dbo_EntityAttributes a on e.StaticPoolID = a.EntityID
                where e.CurveID = @CurveID$

                insert into dbo_CurveAttributes (CurveID, AttributeName, AttributeValue)
                values (@CurveID, 'Section', '@Section')$

            """
            sql = MysqlAdapter.prepareSql(sql, params)
            MysqlAdapter.commonRunsql(sql, separator="$")

            if with_regression and default_rate_regression_results is not None:
                trend_factor = default_rate_regression_results["trend_factor"]
                springfestival_factor = default_rate_regression_results["springfestival_factor"]
                nationalday_factor = default_rate_regression_results["nationalday_factor"]
                base_date = default_rate_regression_results["base_date"]

                sql = f"""
                    insert into dbo_CurveAttributes(CurveID, AttributeName, AttributeValue)
                    values 
                    (@CurveID, 'TrendFactor', '{trend_factor}'),
                    (@CurveID, 'SpringFestivalFactor', '{springfestival_factor}'),
                    (@CurveID, 'NationalDayFactor', '{nationalday_factor}'),
                    (@CurveID, 'BaseDate', '{base_date}');
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                print(sql)
                MysqlAdapter.commonRunsql(sql)

        else:
            sql = """
                delete from dbo_Curves
                where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            MysqlAdapter.commonRunsql(sql)

        # 如果这是[1_30]区间，将违约率曲线加入到super_group_id
        if "SuperGroupID" in params.keys() and params["Section"] == "[ArrearsRate1_30]":
            sql = """
                select count(0) from Analysis_CurveGroupCurves where GroupID = @SuperGroupID and CurveID = @CurveID
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            isexists = MysqlAdapter.commonExecuteGetData(sql)[0][0]
            if isexists == 0:
                sql = """
                    insert into Analysis_CurveGroupCurves(GroupID, CurveID)
                    values(@SuperGroupID, @CurveID)
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

    # 早偿率曲线
    if params["Section"] == "[ArrearsRate1_30]":
        params["CurveTypeCode"] = "PrepaymentRate"
        params["vCurveType"] = "早偿率曲线"
        params["CurveName"] = "早偿_" + str(params["StaticPoolID"])
        params["CurveDesc"] = ""
        sql = """
            insert into data_PrepaymentCurve
            select '@GUID', date_format(LoanStartDate, '%Y%m%d')
            , ROW_NUMBER() over (partition by LoanStartDate order by AsOf) as PeriodId
            , PerformingLoanBalance
            , PrepaymentAmount
            , NULL
            from dbo_StaticPoolRawData
            where StaticPoolID = @StaticPoolID;
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        MysqlAdapter.commonRunsql(sql)

        sql = """
            select count(0) from data_PrepaymentCurve where id = '@GUID' and Prepayment is not null;
        """
        sql = MysqlAdapter.prepareSql(sql, params)
        isexists = MysqlAdapter.commonExecuteGetData(sql)[0][0]

        if isexists > 0:
            params["CurveDesc"] = CONST_STR + str(params["StaticPoolID"]) + ")"

            if with_regression and prepayment_rate_regression_results is not None:
                print("早偿率曲线使用宏观因素回归结果")
                '''
                    prepayment_rate_regression_results = {
                        "prepayment_rate_curve": prepayment_curve,
                        "trend_factor": trend_factor,
                        "springfestival_factor": springfestival_factor,
                        "nationalday_factor": nationalday_factor,
                        "base_date": base_date
                    }                
                '''
                prepayment_curve = prepayment_rate_regression_results["prepayment_rate_curve"]

                params["CurveLength"] = len(prepayment_curve)
                # 将曲线转换为逗号连接的字符串，注意不要使用科学计数法
                # 如果保留8位小数后为0（例如0.00000000），则直接用'0'
                params["CurveData"] = ','.join(["0" if x == 0 or x < 1e-8 else f"{x:.8f}" for x in prepayment_curve])
                
            else:
                # 第一期的早偿率=早偿金额/放款规模
                # 从第二期开始，早偿率=早偿金额/(上一期正常余额)
                sql = """
                    update data_PrepaymentCurve p
                    inner join data_PrepaymentCurve p1
                    on p.id = p1.id and p.StartDate = p1.StartDate and p.PeriodId = p1.PeriodId + 1
                    set p.Balance = p1.Balance
                    where p.id = '@GUID';

                    update data_PrepaymentCurve p
                    inner join (
                        select date_format(LoanStartDate, '%Y%m%d') as StartDate, max(LoanAmount) as LoanAmount
                        from dbo_StaticPoolRawData
                        where StaticPoolID = @StaticPoolID
                        group by LoanStartDate
                    ) a on p.StartDate = a.StartDate set p.Balance = a.LoanAmount
                    where p.id = '@GUID' and p.PeriodId = 1;

                    update data_PrepaymentCurve
                    set Rate = case when Balance = 0 then NULL else Prepayment / Balance end
                    where id = '@GUID';
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

                sql = """
                    delete from data_PrepaymentCurve
                    where id = '@GUID' and Rate is NULL;
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

                sql = '''
                    select count(0) from (
                        select p.PeriodId, cast(sum(cast(p.Rate as decimal(15,4)) * a.LoanAmount) / sum(a.LoanAmount) as decimal(15,4)) as WARate
                        from data_PrepaymentCurve p
                        inner join (
                            select date_format(LoanStartDate, '%Y%m%d') as StartDate, max(LoanAmount) as LoanAmount
                            from dbo_StaticPoolRawData
                            where StaticPoolID = @StaticPoolID
                            group by LoanStartDate
                        ) a on p.StartDate = a.StartDate
                        where p.id = '@GUID' and if('@SelectedDates'='space','','@SelectedDates') = ''
                        group by p.PeriodId
                    ) x;
                '''
                sql = MysqlAdapter.prepareSql(sql, params)
                params["CurveLength"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

                sql = """
                    select group_concat(WARate separator ',')
                    from (
                        select p.PeriodId, cast(sum(cast(p.Rate as decimal(15,4)) * a.LoanAmount) / sum(a.LoanAmount) as decimal(15,4)) as WARate
                        from data_PrepaymentCurve p
                        inner join (
                            select date_format(LoanStartDate, '%Y%m%d') as StartDate, max(LoanAmount) as LoanAmount
                            from dbo_StaticPoolRawData
                            where StaticPoolID = @StaticPoolID
                            group by LoanStartDate
                        ) a on p.StartDate = a.StartDate
                        where p.id = '@GUID' and if('@SelectedDates'='space','','@SelectedDates') = ''
                        group by p.PeriodId
                        order by p.PeriodId
                    ) x;
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                params["CurveData"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

            sql = """
                select IFNULL(max(ID),0) from dbo_Curves where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            params["CurveID"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]
            if params["CurveID"] == 0:
                sql = """
                    insert into dbo_Curves(CurveTypeCode, CurveType, CurveName, CurveLength, CurveData, Description)
                    values('@CurveTypeCode', '@vCurveType', '@CurveName', '@CurveLength', '@CurveData', '@CurveDesc');

                    select last_Insert_id();
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                params["CurveID"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

            else:
                sql = """
                    update dbo_Curves
                    set CurveData = '@CurveData', CurveLength='@CurveLength'
                    where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

            # 静态池的属性即为曲线的属性
            # 静态池的属性需要包括group_id，因此曲线属性也包括group_id
            # 在分池加压匹配中，分池现金流的group_id与曲线的group_id进行匹配
            sql = """
                delete from dbo_CurveExtension where CurveID = @CurveID$

                insert into dbo_CurveExtension(CurveID, StaticPoolID, Section, Attributes)
                values(@CurveID, @StaticPoolID, '', concat('StaticPoolID:', '@StaticPoolID'))$

                delete from dbo_CurveAttributes where CurveID = @CurveID$

                insert into dbo_CurveAttributes(CurveID, AttributeName, AttributeValue)
                select @CurveID, a.AttributeName, a.AttributeValue
                from dbo_CurveExtension e
                inner join dbo_EntityAttributes a on e.StaticPoolID = a.EntityID
                where e.CurveID = @CurveID$

                insert into dbo_CurveAttributes (CurveID, AttributeName, AttributeValue)
                values (@CurveID, 'Section', '@Section')$
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            MysqlAdapter.commonRunsql(sql, separator="$")

            if with_regression and prepayment_rate_regression_results is not None:
                trend_factor = prepayment_rate_regression_results["trend_factor"]
                springfestival_factor = prepayment_rate_regression_results["springfestival_factor"]
                nationalday_factor = prepayment_rate_regression_results["nationalday_factor"]
                base_date = prepayment_rate_regression_results["base_date"]

                sql = f"""
                    insert into dbo_CurveAttributes(CurveID, AttributeName, AttributeValue)
                    values 
                    (@CurveID, 'TrendFactor', '{trend_factor}'),
                    (@CurveID, 'SpringFestivalFactor', '{springfestival_factor}'),
                    (@CurveID, 'NationalDayFactor', '{nationalday_factor}'),
                    (@CurveID, 'BaseDate', '{base_date}');
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                print(sql)
                MysqlAdapter.commonRunsql(sql)

        else:
            sql = """
                delete from dbo_Curves
                where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            MysqlAdapter.commonRunsql(sql)

        # 如果这是[1_30]区间，将早偿率曲线加入到super_group_id
        if "SuperGroupID" in params.keys() and params["Section"] == "[ArrearsRate1_30]":
            sql = """
                select count(0) from Analysis_CurveGroupCurves where GroupID = @SuperGroupID and CurveID = @CurveID
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            isexists = MysqlAdapter.commonExecuteGetData(sql)[0][0]
            if isexists == 0:
                sql = """
                    insert into Analysis_CurveGroupCurves(GroupID, CurveID)
                    values(@SuperGroupID, @CurveID)
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

    ### 迁移率曲线
    if 'Exceed' not in params["Section"]:
        sql = '''
            insert into data_StaticPoolArrears
            select '@GUID',
                case
                    when '@Section' = '[M0]' then ifnull(PerformingLoanBalance, 0)
                    when '@Section' = '[ArrearsRate1_30]' then ifnull(ArrearsAmount1_30, 0)
                    when '@Section' = '[ArrearsRate31_60]' then ifnull(ArrearsAmount31_60, 0)
                    when '@Section' = '[ArrearsRate61_90]' then ifnull(ArrearsAmount61_90, 0)
                    when '@Section' = '[ArrearsRate91_120]' then ifnull(ArrearsAmount91_120, 0)
                    when '@Section' = '[ArrearsRate121_150]' then ifnull(ArrearsAmount121_150, 0)
                    when '@Section' = '[ArrearsRate151_180]' then ifnull(ArrearsAmount151_180, 0)
                end as ArrearsAmount
                , case
                    when '@Section' = '[M0]' then ifnull(ArrearsAmount1_30, 0)
                    when '@Section' = '[ArrearsRate1_30]' then ifnull(ArrearsAmount31_60, 0)
                    when '@Section' = '[ArrearsRate31_60]' then ifnull(ArrearsAmount61_90, 0)
                    when '@Section' = '[ArrearsRate61_90]' then ifnull(ArrearsAmount91_120, 0)
                    when '@Section' = '[ArrearsRate91_120]' then ifnull(ArrearsAmount121_150, 0)
                    when '@Section' = '[ArrearsRate121_150]' then ifnull(ArrearsAmount151_180, 0)
                    when '@Section' = '[ArrearsRate151_180]' then ifnull(ArrearsAmountExceed180, 0)
                end as NewArrearsAmount
                ,date_format(LoanStartDate, '%Y%m%d') as LoanStartDate
                ,ROW_NUMBER() over (partition by LoanStartDate order by date_format(AsOf, '%Y%m%d')) as PeriodId
            from dbo_StaticPoolRawData
            where StaticPoolID = @StaticPoolID
            order by LoanStartDate;

            insert into data_StaticPoolTransition
            select '@GUID', c.LoanStartDate, c.PeriodId
            ,case when p.PeriodId is null then 0
                when p.ArrearsAmount = 0 then NULL
                when '@Section' = '[ArrearsRate151_180]' then (c.NewArrearsAmount - ifnull(p.NewArrearsAmount, 0)) / p.ArrearsAmount
                else c.NewArrearsAmount / p.ArrearsAmount
            end as TransitionRate
            from data_StaticPoolArrears c
            left join data_StaticPoolArrears p
            on c.id = p.id and c.LoanStartDate = p.LoanStartDate and c.PeriodId = p.PeriodId + 1
            where c.id = '@GUID';

            update data_StaticPoolTransition
            set TransitionRate = 0
            where id = '@GUID' and TransitionRate < 0;
            
            insert into data_StaticPoolWATransition
            select '@GUID', p.PeriodId, ifnull(x.WARate, 0) as WARate
            from (
                select distinct PeriodId
                from data_StaticPoolTransition
                where id = '@GUID'
            ) p
            left join (
                select r.PeriodId
                , case when sum(case when r.TransitionRate is not null then a.LoanAmount else 0 end) = 0 then NULL
                else cast(sum(case when r.TransitionRate is not null then r.TransitionRate * a.LoanAmount else 0 end) / 
                    sum(case when r.TransitionRate is not null then a.LoanAmount else 0 end) as decimal(15,4)) 
                end as WARate
                from data_StaticPoolTransition r
                inner join (
                    select date_format(LoanStartDate, '%Y%m%d') as StartDate, max(LoanAmount) as LoanAmount
                    from dbo_StaticPoolRawData
                    where StaticPoolID = @StaticPoolID
                    group by LoanStartDate
                ) a on r.StartDate = a.StartDate
                where r.id = '@GUID' and if('@SelectedDates'='space','','@SelectedDates') = ''
                group by r.PeriodId
            ) x
            on p.PeriodId = x.PeriodId
        '''
        sql = MysqlAdapter.prepareSql(sql,params)
        MysqlAdapter.commonRunsql(sql)

        params['CurveTypeCode'] = 'TransitionRate'
        params['vCurveType'] = '迁移率曲线'
        params['CurveName'] = '迁移_' + str(params['StaticPoolID']) + '_' + params['Section']
        params['CurveDesc']='生成于静态池(ID = ' + str(params['StaticPoolID']) + ')'

        sql = '''
            select count(0) from data_StaticPoolWATransition where id = '@GUID';
        '''
        sql = MysqlAdapter.prepareSql(sql,params)
        params['CurveLength']  = MysqlAdapter.commonExecuteGetData(sql)[0][0]

        sql = '''
            select group_concat(ifnull(WARate,0) separator ',') from data_StaticPoolWATransition where id = '@GUID' order by PeriodId;
        '''
        sql = MysqlAdapter.prepareSql(sql,params)
        params['CurveData']  = MysqlAdapter.commonExecuteGetData(sql)[0][0]

        sql = '''
            select IFNULL(max(ID),0) from dbo_Curves where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
        '''
        sql = MysqlAdapter.prepareSql(sql,params)
        params['CurveID']  = MysqlAdapter.commonExecuteGetData(sql)[0][0]

        if params['CurveID'] ==0:
            sql ='''
                insert into dbo_Curves(CurveTypeCode, CurveType, CurveName, CurveLength, CurveData, Description)
                values('@CurveTypeCode', '@vCurveType', '@CurveName', '@CurveLength', '@CurveData', '@CurveDesc');

                select last_Insert_id();
                '''
            sql = MysqlAdapter.prepareSql(sql,params)
            params['CurveID'] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

        else:
            sql  ='''
                update dbo_Curves
                set CurveData = '@CurveData', CurveLength='@CurveLength'
                where CurveTypeCode = '@CurveTypeCode' and CurveName = '@CurveName'
            '''
            sql = MysqlAdapter.prepareSql(sql,params)
            MysqlAdapter.commonRunsql(sql)

        # 静态池的属性即为曲线的属性
        # 静态池的属性需要包括group_id，因此曲线属性也包括group_id
        # 在分池加压匹配中，分池现金流的group_id与曲线的group_id进行匹配
        sql = '''
            delete from dbo_CurveExtension where CurveID = @CurveID$

            insert into dbo_CurveExtension(CurveID, StaticPoolID, Section, Attributes)
            values(@CurveID, @StaticPoolID, '@Section', concat('StaticPoolID:', '@StaticPoolID', ';Section:', '@Section'))$

            delete from dbo_CurveAttributes where CurveID = @CurveID$

            insert into dbo_CurveAttributes (CurveID, AttributeName, AttributeValue)
            select @CurveID, a.AttributeName, a.AttributeValue
            from dbo_CurveExtension e
            inner join dbo_EntityAttributes a on e.StaticPoolID = a.EntityID
            where e.CurveID = @CurveID$

            insert into dbo_CurveAttributes (CurveID, AttributeName, AttributeValue)
            values (@CurveID, 'Section', '@Section')$
        '''
        sql = MysqlAdapter.prepareSql(sql,params)
        MysqlAdapter.commonRunsql(sql,separator = '$')

        # 将迁移率率曲线加入到super_group_id
        if "SuperGroupID" in params.keys():
            sql = """
                select count(0) from Analysis_CurveGroupCurves where GroupID = @SuperGroupID and CurveID = @CurveID
            """
            sql = MysqlAdapter.prepareSql(sql, params)
            isexists = MysqlAdapter.commonExecuteGetData(sql)[0][0]
            if isexists == 0:
                sql = """
                    insert into Analysis_CurveGroupCurves(GroupID, CurveID)
                    values(@SuperGroupID, @CurveID)
                """
                sql = MysqlAdapter.prepareSql(sql, params)
                MysqlAdapter.commonRunsql(sql)

    sql = '''
        delete from data_PrepaymentCurve where id = '@GUID';
        delete from data_StaticPoolArrears where id = '@GUID';
        delete from data_StaticPoolTransition where id = '@GUID';
        delete from data_StaticPoolWATransition where id = '@GUID';
    '''
    sql = MysqlAdapter.prepareSql(sql,params)
    MysqlAdapter.commonRunsql(sql)

    return ""


if __name__ == "__main__":
    try:

        arg = urllib.parse.unquote_plus(sys.argv[1])
        params = SQLParamsParser.getParmas(arg)
        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 InvestSuite_usp_StaticPool_GenerateCurves.py '{"StaticPoolID": "1500", "Section":"[ArrearsRate1_30]","SelectedDates":""}
        result = func(params)

        print("$OUTPUT" + result)
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
