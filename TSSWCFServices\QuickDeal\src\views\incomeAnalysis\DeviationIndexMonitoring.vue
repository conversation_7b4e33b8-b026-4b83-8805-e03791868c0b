<template>
    <div class="monitoring">
        <div class="search-form">
            <el-form inline>
                <el-form-item label="专项计划" required>
                    <el-select v-model="form.trustId" filterable @change="getDateOptions" style="width: 480px;"
                        popper-class="max-height-select-dropdown">
                        <el-option v-for="item in trustOptions" :key="item.TrustId"
                            :label="item.TrustCode + '_' + item.TrustName + '( ID: ' + item.TrustId + ' )'"
                            :value="item.TrustId"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="测算日期" required>
                    <el-select v-model="form.calcDate" filterable @change="getScenarioOptions">
                        <el-option v-for="item in dateOptions" :key="item.archive_date" :label="item.archive_date"
                            :value="item.archive_date"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item v-if="scenarioId" label="数据日期">
                    <el-input :value="scenarioId.data_date" disabled></el-input>
                </el-form-item>
                <el-form-item label="当前日期">
                    <el-input v-model="form.currentDate" disabled></el-input>
                </el-form-item>
                <el-form-item label="测算情景" required>
                    <el-select v-model="form.scenarioId" filterable style="width: 760px;"
                        popper-class="max-height-select-dropdown">
                        <el-option v-for="item in scenarioOptions" :key="item.task_session_id" :label="item.label"
                            :value="item.task_session_id"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="">
                    <el-button type="primary" @click="getData" :disabled="loading1 || loading2 || loading">检索</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="result-panel">
            <el-tabs>
                <el-tab-pane label="券端_By兑付周期">
                    <base-box title="图表" :hideFold="false">
                        <template v-slot:header>
                            <el-select v-model="range.min" @change="updateRange($refs.chart1, range)" style="width: 230px">
                                <el-option v-for="(item, index) in bondsData" :label="item.dateRange"
                                    :value="index"></el-option>
                            </el-select>
                            <span class="ml-5 mr-5">-</span>
                            <el-select v-model="range.max" @change="updateRange($refs.chart1, range)" style="width: 230px">
                                <el-option v-for="(item, index) in bondsData" :label="item.dateRange" :value="index"
                                    :disabled="index === range.min"></el-option>
                            </el-select>
                        </template>
                        <HChart v-loading="loading1" :options="chartOption1" ref="chart1" style="height: 600px"></HChart>
                    </base-box>
                    <base-box title="数据" :hideFold="false">
                        <el-table v-loading="loading1" element-loading-text="数据获取中..." :data="bondsData" height="620px">
                            <el-table-column label="期数" width="70px" prop="periodId">
                                <template v-slot="{ row, $index }">
                                    <div>{{ $index + 1 }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="开始时间" width="120px" prop="startDate"></el-table-column>
                            <el-table-column label="结束时间" width="120px" prop="endDate"></el-table-column>
                            <el-table-column label="预实兑付区间" min-width="220px" prop="dateRange"></el-table-column>
                            <el-table-column label="预测分配总额（元）" min-width="180px" prop="cashflowDue"
                                :formatter="formatMoney">
                            </el-table-column>
                            <el-table-column label="实际分配总额（元）" min-width="180px" prop="cashflowPaid"
                                :formatter="formatMoney">
                            </el-table-column>
                            <el-table-column label="预实分配总额偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.cashflowDue, row.cashflowPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实分配总额偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.cashflowDue, row.cashflowPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测_当期分配本金" min-width="140px" prop="principalDue"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际_当期分配本金" min-width="140px" prop="principalPaid"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="预实分配本金偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.principalDue, row.principalPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实分配本金偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.principalDue, row.principalPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测_当期分配利息" min-width="140px" prop="interestDue"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际_当期分配利息" min-width="140px" prop="interestPaid"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="预实分配利息偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.interestDue, row.interestPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实分配利息偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.interestDue, row.interestPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测_当期分配总费用" min-width="160px" prop="feeDue"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际_当期分配总费用" min-width="160px" prop="feePaid"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="预实分配费用偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.feeDue, row.feePaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实分配费用偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.feeDue, row.feePaid) }}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </base-box>

                </el-tab-pane>
                <el-tab-pane label="资产端_By收款区间">
                    <base-box title="图表" :hideFold="false">
                        <template v-slot:header>
                            <el-select v-model="range2.min" @change="updateRange($refs.chart, range2)" style="width: 230px">
                                <el-option v-for="(item, index) in assetPoolData" :label="item.dateRange"
                                    :value="index"></el-option>
                            </el-select>
                            <span class="ml-5 mr-5">-</span>
                            <el-select v-model="range2.max" @change="updateRange($refs.chart, range2)" style="width: 230px">
                                <el-option v-for="(item, index) in assetPoolData" :label="item.dateRange" :value="index"
                                    :disabled="index === range2.min"></el-option>
                            </el-select>
                        </template>
                        <HChart v-loading="loading" :options="chartOption" ref="chart" style="height: 600px"></HChart>
                    </base-box>
                    <base-box title="数据" :hideFold="false">
                        <el-table v-loading="loading" element-loading-text="数据获取中..." :data="assetPoolData" height="620px">
                            <el-table-column label="期数" width="70px" prop="periodId"></el-table-column>
                            <el-table-column label="开始时间" width="110px" prop="startDate"></el-table-column>
                            <el-table-column label="结束时间" width="110px" prop="endDate"></el-table-column>
                            <el-table-column label="预实收款区间" prop="dateRange" min-width="220px"></el-table-column>
                            <el-table-column label="预测总额" prop="cashflowDue" min-width="160px"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际总额" prop="cashflowPaid" min-width="160px" :formatter="formatMoney">
                                <template v-slot="{ row }">
                                    <div
                                        :class="['in-range cashflow', isDateInRange(row.dateRange) && row.cashflowPaid ? 'highlight' : '']">
                                        {{ toMoney(row.cashflowPaid) }}
                                        <el-tooltip v-if="isDateInRange(row.dateRange) && row.cashflowPaid" effect="light"
                                            placement="bottom">
                                            <div slot="content">
                                                <p>此数值统计区间为: {{ newDateRange(row.dateRange) }}</p>
                                            </div>
                                            <i class="el-icon-info help"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实总额偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.cashflowDue, row.cashflowPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实总额偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.cashflowDue, row.cashflowPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测本金" prop="principalDue" min-width="160px"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际本金" prop="principalPaid" min-width="160px" :formatter="formatMoney">
                                <template v-slot="{ row }">
                                    <div
                                        :class="['in-range principal', isDateInRange(row.dateRange) && row.principalPaid ? 'highlight' : '']">
                                        {{ toMoney(row.principalPaid) }}
                                        <el-tooltip v-if="isDateInRange(row.dateRange) && row.principalPaid" effect="light"
                                            placement="bottom">
                                            <div slot="content">
                                                <p>此数值统计区间为: {{ newDateRange(row.dateRange) }}</p>
                                            </div>
                                            <i class="el-icon-info help"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实本金偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.principalDue, row.principalPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实本金偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.principalDue, row.principalPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测利息" prop="interestDue" min-width="160px"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际利息" prop="interestPaid" min-width="160px" :formatter="formatMoney">
                                <template v-slot="{ row }">
                                    <div
                                        :class="['in-range interest', isDateInRange(row.dateRange) && row.interestPaid ? 'highlight' : '']">
                                        {{ toMoney(row.interestPaid) }}
                                        <el-tooltip v-if="isDateInRange(row.dateRange) && row.interestPaid" effect="light"
                                            placement="bottom">
                                            <div slot="content">
                                                <p>此数值统计区间为: {{ newDateRange(row.dateRange) }}</p>
                                            </div>
                                            <i class="el-icon-info help"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实利息偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.interestDue, row.interestPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实利息偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.interestDue, row.interestPaid) }}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </base-box>
                </el-tab-pane>
                <el-tab-pane label="资产端_ByMob">
                    <base-box title="图表" :hideFold="false">
                        <template v-slot:header>
                            <el-select v-model="range3.min" @change="updateRange($refs.chart2, range3)"
                                style="width: 230px">
                                <el-option v-for="(item, index) in mobData" :label="item.dateRange"
                                    :value="index"></el-option>
                            </el-select>
                            <span class="ml-5 mr-5">-</span>
                            <el-select v-model="range3.max" @change="updateRange($refs.chart2, range3)"
                                style="width: 230px">
                                <el-option v-for="(item, index) in mobData" :label="item.dateRange" :value="index"
                                    :disabled="index === range3.min"></el-option>
                            </el-select>
                        </template>
                        <HChart v-loading="loading2" :options="chartOption2" ref="chart2" style="height: 600px"></HChart>
                    </base-box>
                    <base-box title="数据" :hideFold="false">
                        <el-table v-loading="loading2" element-loading-text="数据获取中..." :data="mobData" height="620px">
                            <el-table-column label="归集阶段" width="90px" prop="PeriodId">
                                <template v-slot="{ row, $index }">
                                    <div>{{ $index + 1 }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="开始时间" width="110px" prop="startDate"></el-table-column>
                            <el-table-column label="结束时间" width="110px" prop="endDate"></el-table-column>
                            <el-table-column label="预实收款区间" prop="dateRange" min-width="220px"></el-table-column>
                            <el-table-column label="预测总额" prop="cashflowDue" min-width="160px"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际总额" prop="cashflowPaid" min-width="160px" :formatter="formatMoney">
                                <template v-slot="{ row }">
                                    <div
                                        :class="['in-range cashflow', isDateInRange(row.dateRange) && row.cashflowPaid ? 'highlight' : '']">
                                        {{ toMoney(row.cashflowPaid) }}
                                        <el-tooltip v-if="isDateInRange(row.dateRange) && row.cashflowPaid" effect="light"
                                            placement="bottom">
                                            <div slot="content">
                                                <p>此数值统计区间为: {{ newDateRange(row.dateRange) }}</p>
                                            </div>
                                            <i class="el-icon-info help"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实总额偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.cashflowDue, row.cashflowPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实总额偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.cashflowDue, row.cashflowPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测本金" prop="principalDue" min-width="160px"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际本金" prop="principalPaid" min-width="160px" :formatter="formatMoney">
                                <template v-slot="{ row }">
                                    <div
                                        :class="['in-range principal', isDateInRange(row.dateRange) && row.principalPaid ? 'highlight' : '']">
                                        {{ toMoney(row.principalPaid) }}
                                        <el-tooltip v-if="isDateInRange(row.dateRange) && row.principalPaid" effect="light"
                                            placement="bottom">
                                            <div slot="content">
                                                <p>此数值统计区间为: {{ newDateRange(row.dateRange) }}</p>
                                            </div>
                                            <i class="el-icon-info help"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实本金偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.principalDue, row.principalPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实本金偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.principalDue, row.principalPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预测利息" prop="interestDue" min-width="160px"
                                :formatter="formatMoney"></el-table-column>
                            <el-table-column label="实际利息" prop="interestPaid" min-width="160px" :formatter="formatMoney">
                                <template v-slot="{ row }">
                                    <div
                                        :class="['in-range interest', isDateInRange(row.dateRange) && row.interestPaid ? 'highlight' : '']">
                                        {{ toMoney(row.interestPaid) }}
                                        <el-tooltip v-if="isDateInRange(row.dateRange) && row.interestPaid" effect="light"
                                            placement="bottom">
                                            <div slot="content">
                                                <p>此数值统计区间为: {{ newDateRange(row.dateRange) }}</p>
                                            </div>
                                            <i class="el-icon-info help"></i>
                                        </el-tooltip>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实利息偏差（元）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffValue(row.interestDue, row.interestPaid) }}</div>
                                </template>
                            </el-table-column>
                            <el-table-column label="预实利息偏差（%）" min-width="180px">
                                <template v-slot="{ row }">
                                    <div> {{ diffPercent(row.interestDue, row.interestPaid) }}</div>
                                </template>
                            </el-table-column>
                        </el-table>
                    </base-box>
                </el-tab-pane>

            </el-tabs>
        </div>
    </div>
</template>


<script>
import HChart from '@/components/common/Highcharts.vue'
import utils from '@/utils/';
import dynamicTab from '@/mixins/dynamicTab.js';
import BaseBox from '../../components/base/BaseBox.vue';
import api from '@/api/Api'

export default {
    components: {
        HChart,
        BaseBox
    },
    mixins: [dynamicTab],
    data() {
        return {
            trustOptions: [],
            form: {
                trustId: '',
                calcDate: '',
                currentDate: (new Date()).Format('yyyy-MM-dd'),
                scenarioId: ''
            },
            assetPoolData: [],
            bondsData: [],
            highchartColors: [
                "#007BFF", // 经典蓝色，传达专业、可靠的感觉
                "#007BFF", // 经典蓝色，传达专业、可靠的感觉
                "#28A745", // 绿色，代表生机、成长，常用于商务展示中的积极数据
                "#28A745", // 绿色，代表生机、成长，常用于商务展示中的积极数据
                "#FFC107", // 明亮的黄色，突出重点，吸引注意力
                "#FFC107", // 明亮的黄色，突出重点，吸引注意力
                "#6F42C1", // 紫色，展现独特与创意
                "#6F42C1", // 紫色，展现独特与创意
            ],
            chartOption: null,
            chartOption1: null,
            chartOption2: null,

            loading: false,
            loading1: false,
            loading2: false,

            dateOptions: [],
            scenarioOptions: [],

            mobData: [],

            range: {
                min: '',
                max: ''
            },
            range2: {
                min: '',
                max: ''
            },
            range3: {
                min: '',
                max: ''
            },
            // 最新的数据日期
            newDataDate: '',
        }
    },
    computed: {
        scenarioId() {
            return this.scenarioOptions.find(v => v.task_session_id == this.form.scenarioId);
        }
    },
    mounted() {
        this.newTab({
            id: 'DeviationIndexMonitoring',
            name: 'DeviationIndexMonitoring',
            title: '偏离度指标监控'
        })
        this.getTrustOptions();
    },
    methods: {
        isDateInRange(dateRangeStr) {
            if (dateRangeStr == null) return false;
            const currentDate = this.newDataDate;
            const regex = /【(\d{4}-\d{2}-\d{2})，(\d{4}-\d{2}-\d{2})】/;
            const match = dateRangeStr.match(regex);
            if (match) {
                const startDate = match[1];
                const endDate = match[2];
                return currentDate >= startDate && currentDate <= endDate;
            }
            return false;
        },
        newDateRange(dateRangeStr) {
            if (dateRangeStr == null) return '';
            const currentDate = this.newDataDate;
            const regex = /【(\d{4}-\d{2}-\d{2})，(\d{4}-\d{2}-\d{2})】/;
            const match = dateRangeStr.match(regex);
            if (match) {
                const startDate = match[1];
                return `[${startDate}, ${currentDate}]`;
            }
            return '';
        },
        // 计算两个值的差额
        diffValue(val1, val2) {
            if (val1 == null || val2 == null) return '';
            return this.toMoney(utils.floatSub(val1, val2));
        },
        // 计算两个值的差额百分比
        diffPercent(val1, val2) {
            if (val1 == null || val2 == null) return '';
            return Number(utils.floatMul(utils.floatDiv(utils.floatSub(val1, val2), val2), 100)).toFixed(2);
        },
        formatMoney(row, column, value) {
            return this.toMoney(value);
        },
        toMoney(value) {
            // null 要显示出来，不能为0
            if (value == null || value === '') return '';
            return utils.formatCurrency(Number(value).toFixed(2));
        },
        getTrustOptions() {
            let svc = this.$http('FixedIncome', 'TrustManagement.usp_GetTrustIdAndName');
            svc.ExecTable().then(res => {
                if (res) {
                    this.trustOptions = res;
                }
            });
        },
        getDateOptions(val) {
            this.form.calcDate = '';
            this.form.scenarioId = '';
            api.getArchiveDate(val).then(res => {
                this.dateOptions = res;
            })
            api.getLatestDataDate(val).then(res => {
                this.newDataDate = res;
            })
        },
        getScenarioOptions() {
            this.form.scenarioId = '';
            api.getArchiveRecords(this.form.trustId, this.form.calcDate).then(res => {
                if (res) {
                    this.scenarioOptions = res.map((item, index) => {
                        const durationRate = item.duration_rate === 0 ? '-' : item.duration_rate;
                        const serviceFeeRate = item.service_fee_rate === 0 ? '-' : item.service_fee_rate;
                        const coupon = item.coupon === 0 ? '-' : item.coupon;
                        const premium = item.premium === 0 ? '-' : item.premium;
                        return {
                            plan_id: item.plan_id,
                            task_session_id: item.task_session_id,
                            id: item.id,
                            data_date: item.data_date,
                            label: `循环方案:${item.plan_id}；覆盖率(%):${durationRate}；服务费率(%):${serviceFeeRate}；票面利率(%):${coupon}；次级溢价率(%):${premium}；违约倍数:${item.cdr}；早偿倍数:${item.cpr}；`
                        }
                    });
                }
            });
        },
        getData() {
            // 校验必选字段
            if (!this.form.trustId) {
                this.$message.error('请选择专项计划');
                return;
            }
            if (!this.form.calcDate) {
                this.$message.error('请选择测算日期');
                return;
            }
            if (!this.form.scenarioId) {
                this.$message.error('请选择测算情景');
                return;
            }
            this.loading = true;
            this.range = {
                min: '',
                max: ''
            }
            this.range2 = {
                min: '',
                max: ''
            }
            this.range3 = {
                min: '',
                max: ''
            }
            api.getAssetDeviation(this.form.trustId, this.scenarioId.data_date, this.scenarioId.id).then(res => {
                this.assetPoolData = res;

                const xCate = [];
                const series = [
                    {
                        name: '实际本金',
                        data: [],
                        color: '#007BFF',
                        dashStyle: 'Solid',
                    },
                    {
                        name: '预测本金',
                        data: [],
                        color: '#007BFF',
                        dashStyle: 'shortdash',
                    },
                    {
                        name: '实际利息',
                        data: [],
                        color: '#28A745',
                        dashStyle: 'Solid',
                    },
                    {
                        name: '预测利息',
                        data: [],
                        color: '#28A745',
                        dashStyle: 'shortdash',
                    },
                    {
                        name: '实际总额',
                        data: [],
                        color: '#FFC107',
                        dashStyle: 'Solid',
                    },
                    {
                        name: '预测总额',
                        data: [],
                        color: '#FFC107',
                        dashStyle: 'shortdash',
                    },
                ];

                for (let i = 0; i < res.length; i++) {
                    xCate.push(res[i].dateRange);
                    series[1].data.push(res[i].principalDue);
                    series[0].data.push(res[i].principalPaid);
                    series[3].data.push(res[i].interestDue);
                    series[2].data.push(res[i].interestPaid);
                    series[5].data.push(res[i].cashflowDue);
                    series[4].data.push(res[i].cashflowPaid);
                }
                let virtualNode = '';
                for (let i = 0; i < res.length; i++) {
                    if (res[i].principalDue !== null && i > 0) {
                        if (res[i - 1].principalPaid !== null) {
                            series[1].data.splice(i - 1, 1, res[i - 1].principalPaid)
                            virtualNode = xCate[i - 1];
                        }
                        break;
                    }
                }
                for (let i = 0; i < res.length; i++) {
                    if (res[i].interestDue !== null && i > 0) {
                        if (res[i - 1].interestPaid !== null) series[3].data.splice(i - 1, 1, res[i - 1].interestPaid)
                        break;
                    }
                }
                for (let i = 0; i < res.length; i++) {
                    if (res[i].cashflowDue !== null && i > 0) {
                        if (res[i - 1].cashflowPaid !== null) series[5].data.splice(i - 1, 1, res[i - 1].cashflowPaid)
                        break;
                    }
                }

                this.chartOption = this.createCombinedChartOptions('资产端_by收款区间预实偏差', xCate, series, virtualNode);
                this.loading = false;
            });

            this.loading1 = true;
            api.getTicketsEndDeviation(this.form.trustId, this.form.scenarioId, this.form.calcDate, this.form.currentDate).then(res => {
                this.bondsData = res;

                const xCate = [];
                const data = {
                    "预测_当期分配本金": [],
                    "实际_当期分配本金": [],
                    "预测_当期分配利息": [],
                    "实际_当期分配利息": [],
                    "预测_当期分配总费用": [],
                    "实际_当期分配总费用": [],
                };
                for (let i = 0; i < res.length; i++) {
                    xCate.push(res[i].dateRange);
                    data["预测_当期分配本金"].push(res[i].principalDue);
                    data["实际_当期分配本金"].push(res[i].principalPaid);
                    data["预测_当期分配利息"].push(res[i].interestDue);
                    data["实际_当期分配利息"].push(res[i].interestPaid);
                    data["预测_当期分配总费用"].push(res[i].feeDue);
                    data["实际_当期分配总费用"].push(res[i].feePaid);
                }
                this.chartOption1 = this.createChart('债券端成本预实偏差', res.map(v => v.dateRange), data);

                this.loading1 = false;
            });
            this.loading2 = true;
            api.getAssetDeviationByMob(this.form.trustId, this.scenarioId.data_date, this.scenarioId.id).then(res => {
                this.mobData = res;
                const xCate = [];
                const series = [
                    {
                        name: '实际本金',
                        data: [],
                        color: '#007BFF',
                        dashStyle: 'Solid',
                    },
                    {
                        name: '预测本金',
                        data: [],
                        color: '#007BFF',
                        dashStyle: 'shortdash',
                    },
                    {
                        name: '实际利息',
                        data: [],
                        color: '#28A745',
                        dashStyle: 'Solid',
                    },
                    {
                        name: '预测利息',
                        data: [],
                        color: '#28A745',
                        dashStyle: 'shortdash',
                    },
                    {
                        name: '实际总额',
                        data: [],
                        color: '#FFC107',
                        dashStyle: 'Solid',
                    },
                    {
                        name: '预测总额',
                        data: [],
                        color: '#FFC107',
                        dashStyle: 'shortdash',
                    },
                ];

                for (let i = 0; i < res.length; i++) {
                    xCate.push(res[i].dateRange);
                    series[1].data.push(res[i].principalDue);
                    series[0].data.push(res[i].principalPaid);
                    series[3].data.push(res[i].interestDue);
                    series[2].data.push(res[i].interestPaid);
                    series[5].data.push(res[i].cashflowDue);
                    series[4].data.push(res[i].cashflowPaid);
                }

                let virtualNode = '';
                for (let i = 0; i < res.length; i++) {
                    if (res[i].principalDue !== null && i > 0) {
                        if (res[i - 1].principalPaid !== null) {
                            series[1].data.splice(i - 1, 1, res[i - 1].principalPaid)
                            virtualNode = xCate[i - 1];
                        }
                        break;
                    }
                }
                for (let i = 0; i < res.length; i++) {
                    if (res[i].interestDue !== null && i > 0) {
                        if (res[i - 1].interestPaid !== null) series[3].data.splice(i - 1, 1, res[i - 1].interestPaid)
                        break;
                    }
                }
                for (let i = 0; i < res.length; i++) {
                    if (res[i].cashflowDue !== null && i > 0) {
                        if (res[i - 1].cashflowPaid !== null) series[5].data.splice(i - 1, 1, res[i - 1].cashflowPaid)
                        break;
                    }
                }

                this.chartOption2 = this.createCombinedChartOptions('资产端_byMob预实偏差', xCate, series, virtualNode);
                this.loading2 = false;
            });
        },
        createChart(title, categories, data, unit = '(元)') {
            const self = this
            const keys = Object.keys(data);
            const series = Object.values(data).map((v, i) => {
                return {
                    name: keys[i],
                    data: v,
                    color: self.highchartColors[i],
                    dashStyle: keys[i].includes('预测') ? 'shortdash' : 'Solid',
                    marker: {
                        fillColor: self.highchartColors[i],
                        radius: 1,
                        symbol: 'circle'
                    }
                }
            })

            return {
                chart: {
                    type: 'spline',
                    spacingTop: 25,
                },
                title: {
                    text: title,
                },
                subtitle: {
                    text: ''
                },
                xAxis: {
                    categories,
                    //gridLineWidth: 1,
                    title: {
                        text: ''
                    },
                    labels: {
                        rotation: -45 // 标签斜着的角度，正值向右倾斜，负值向左倾斜
                    },
                    min: 0,
                    max: categories.length - 1
                },
                plotOptions: {},
                legend: {
                    align: 'center', // 图例水平居中对齐
                    verticalAlign: 'top', // 图例垂直方向顶部对齐
                    layout: 'horizontal' // 图例项水平排列
                },
                yAxis: {
                    title: {
                        text: unit
                    },
                    lineWidth: 1,
                    labels: {
                        formatter: function () {
                            return this.value.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }
                    },
                    minPadding: 0,
                    maxPadding: 0,
                    min: -1000,
                },
                tooltip: {
                    valueDecimals: 2,
                    shared: true,
                    crosshairs: true
                    //valueSuffix: '%'
                },
                series: series
            }
        },
        createCombinedChartOptions(title, categories, series, virtualNode = '') {

            return {
                chart: {
                    type: 'spline',
                    spacingTop: 25,
                },
                title: {
                    text: title
                },
                legend: {
                    align: 'center', // 图例水平居中对齐
                    verticalAlign: 'top', // 图例垂直方向顶部对齐
                    layout: 'horizontal' // 图例项水平排列
                },
                xAxis: {
                    categories,
                    title: {
                        text: ''
                    },
                    labels: {
                        rotation: -45 // 标签斜着的角度，正值向右倾斜，负值向左倾斜
                    },
                    min: 0,
                    max: categories.length - 1
                },
                yAxis: {
                    title: {
                        text: '(元)'
                    },
                    labels: {
                        formatter: function () {
                            return this.value.toLocaleString(undefined, {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2
                            });
                        }
                    },
                    lineWidth: 1
                },
                plotOptions: {
                    series: {
                        marker: {
                            enabled: false
                        }
                    }
                },
                tooltip: {
                    shared: true,
                    crosshairs: true,
                    valueDecimals: 2,
                    formatter: function () {
                        let showData = [];
                        if (this.x === virtualNode) {
                            showData = this.points.filter(v => !v.series.name.includes('预测'))
                        } else {
                            showData = this.points;
                        }
                        let tooltipContent = '<span style="font-size:10px">' + this.x + '</span><br/>';
                        showData.forEach(point => {
                            const color = point.series.color;
                            const shape = `<span style="color:${color}">\u25CF</span>`; // 圆形形状
                            tooltipContent += `${shape} ${point.series.name}: <b>${Number(point.y).toFixed(2)}</b><br/>`;
                        });
                        return tooltipContent;
                    }
                },
                // 系列数据配置
                series
            }
        },
        updateRange(chartInstance, range) {
            const { min, max } = range;
            if (min === '' || max === '') return;
            if (min == max) return this.$message.error('区间选择有误')
            if (min > max) return this.$message.error('最小区间不能大于最大区间');
            if (chartInstance.chart) {
                const xAxis = chartInstance.chart.xAxis[0];
                xAxis.setExtremes(min, max);

                // 用于存储新 x 轴范围内数据点的 y 值
                const yValues = [];

                // 遍历所有数据系列
                chartInstance.chart.series.forEach(series => {
                    series.data.forEach(dataPoint => {
                        const x = dataPoint.x;
                        const y = dataPoint.y;
                        console.log(dataPoint)
                        if (x >= min && x <= max) {
                            yValues.push(y);
                        }
                    });
                });

                // 计算 Y 轴的最小值和最大值
                const yMin = Math.min(...yValues) - 1000;
                const yMax = Math.max(...yValues);

                // 设置 Y 轴范围
                const yAxis = chartInstance.chart.yAxis[0];
                yAxis.setExtremes(yMin, yMax);
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.monitoring {
    position: relative;
    border-radius: 4px;
}

.search-form {
    background-color: #fff;
    margin-left: -15px;
    margin-right: -15px;
    margin-top: -15px;
    padding: 10px 15px 0 15px;
}

.result-panel {
    background-color: #fff;
    padding: 0 10px 10px 10px;
    border-radius: 5px;
    margin-top: 10px;
}

/deep/ .el-tabs__nav-wrap::after {
    height: 1px;
}

.in-range {
    &.highlight.principal {
        background-color: cornsilk;
    }

    &.highlight.interest {
        background-color: cornsilk;
    }

    &.highlight.cashflow {
        background-color: cornsilk;
    }
}
</style>

<style lang="scss">
.max-height-select-dropdown {
    .el-scrollbar__wrap {
        max-height: 400px !important;
    }
}
</style>