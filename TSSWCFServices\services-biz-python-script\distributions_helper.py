#!/usr/bin/env python
# -*- coding: utf-8 -*-
import pandas as pd
import random
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from distributions_utils import match_asset_group, generate_cashflows_for_single_loan, random_date
random.seed(42)  # 使用固定的种子值

LOAN_ID_SEGMENT = 1000000
CONST_NUM_OF_LOANS = 4000

# 函数：使用分层配额抽样方法生成资产
def generate_assets_from_distribution_stratified(
    asset_category,
    asset_subcategory_buckets,
    loan_term_buckets,
    remaining_term_buckets,
    credit_rating_buckets,
    available_cash,
    pool_date,
    average_loan_amount,
    df_asset_groups_interest_rates,
    grouping_configs,
    discount_method,
    asset_group_cache,
    num_of_pre_existing_loans = 0
):
    """
    使用分层配额抽样方法生成资产，确保生成的资产分布与目标分布更加接近。
    
    Args:
        asset_category: 资产类别
        asset_subcategory_buckets: 子类别分布
        loan_term_buckets: 贷款期限分布
        remaining_term_buckets: 剩余期限分布
        credit_rating_buckets: 信用评级分布
        available_cash: 可用资金
        pool_date: 资产池日期
        average_loan_amount: 平均贷款余额
        df_asset_groups_interest_rates: 资产组利率数据
        grouping_configs: 分组配置
        discount_method: 折现方法
        asset_group_cache: 资产组缓存
        num_of_pre_existing_loans: 调用此方法时, 该asset_category已生成的贷款数量，新的loan_id从这里开始
        
    Returns:
        (loans_df, cashflows_df): 贷款DataFrame和现金流DataFrame
    """
    # 参数检查和边界处理
    if not isinstance(pool_date, datetime):
        raise TypeError("pool_date必须是datetime类型")
        
    if not isinstance(asset_subcategory_buckets, dict) or not asset_subcategory_buckets:
        raise ValueError("asset_subcategory_buckets必须是非空字典")
        
    if not isinstance(loan_term_buckets, dict) or not loan_term_buckets:
        raise ValueError("loan_term_buckets必须是非空字典")
        
    if not isinstance(remaining_term_buckets, dict) or not remaining_term_buckets:
        raise ValueError("remaining_term_buckets必须是非空字典")
        
    if not isinstance(credit_rating_buckets, dict) or not credit_rating_buckets:
        raise ValueError("credit_rating_buckets必须是非空字典")
        
    if available_cash <= 0:
        raise ValueError(f"available_cash必须大于0，当前值: {available_cash}")
        
    if average_loan_amount <= 0:
        raise ValueError(f"average_loan_amount必须大于0，当前值: {average_loan_amount}")
        
    if asset_category not in grouping_configs:
        raise ValueError(f"未找到资产类别 {asset_category} 的分组配置")
    
    # 生成资产的剩余本金为指定的值
    remaining_balance = average_loan_amount

    # 令资产“上一起息日”在过去一个月内均匀分布，以产生连续均匀的按日归集现金流
    # 但注意资产的起始日应基于seasoning确定
    start_date = pool_date - relativedelta(months=1)
    end_date = pool_date - relativedelta(days=1)
    
    # 计算贷款ID起始值
    loan_id_start = asset_category * LOAN_ID_SEGMENT + num_of_pre_existing_loans
    index = loan_id_start
    counter = 0

    # 检查分布权重之和是否接近1
    for name, buckets in [
        ("资产子类别", asset_subcategory_buckets),
        ("贷款期限", loan_term_buckets),
        ("剩余期限", remaining_term_buckets),
        ("信用评级", credit_rating_buckets)
    ]:
        weight_sum = sum(buckets.values())
        if not 0.99 <= weight_sum <= 1.01:  # 允许1%的误差
            print(f"警告: {name}分布的权重之和为{weight_sum:.4f}，期望值为1.0")
    
    # 打印输入参数以便于分析
    print(f"\n=== 开始分层抽样生成资产 ===")
    print(f"资产类别: {asset_category}")
    print(f"可用资金: {available_cash:.2f}")
    print(f"资产池日期: {pool_date}")
    print(f"平均贷款余额: {average_loan_amount:.2f}")
    print(f"折现方法: {discount_method}")
    
    print(f"\n--- 资产子类别分布 ---")
    for bucket, pct in sorted(asset_subcategory_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 贷款期限分布 ---")  
    for bucket, pct in sorted(loan_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 剩余期限分布 ---")
    for bucket, pct in sorted(remaining_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 信用评级分布 ---")
    for bucket, pct in sorted(credit_rating_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
    
    # 动态计算贷款数量而非使用固定值
    # 估算的贷款数量，目标约4000笔
    estimated_total_loans = int(available_cash / average_loan_amount)
    # 确保数量在合理范围，且不会导致单笔贷款余额过小
    total_loans = min(max(3000, estimated_total_loans), 5000)
    print(f"\n估算贷款数量: {estimated_total_loans}，采用目标数量: {total_loans}")
    
    # 计算固定贷款余额（平均分配总预算）
    fixed_loan_balance = available_cash / total_loans
    print(f"固定贷款余额: {fixed_loan_balance:.2f}")
    
    # 步骤1: 计算每个维度组合的目标贷款数量
    loan_quotas = {}
    valid_combinations = 0
    
    # 遍历所有可能的组合
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            # 贷款期限必须是区间右值（离散固定值），例如3个月、6个月、12个月等
            loan_term = int(loan_term_bucket[1])  # 使用右边界，不生成区间内随机值
            
            for credit_rating_bucket, credit_rating_pct in credit_rating_buckets.items():
                credit_rating = credit_rating_bucket[1]  # 使用右边界
                
                # 寻找与当前贷款期限兼容的剩余期限
                compatible_remaining_terms = {}
                total_compatible_pct = 0
                
                # 为短期贷款特殊处理
                if loan_term <= 3:  # 对于3个月及以下的贷款
                    print(f"处理短期贷款期限: {loan_term}个月，放宽匹配条件")
                    # 从剩余期限区间中找出左边界小于loan_term的所有区间
                    for remaining_term_bucket, remaining_term_pct in remaining_term_buckets.items():
                        if remaining_term_bucket[0] < loan_term:  # 只要左边界小于贷款期限
                            compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                            total_compatible_pct += remaining_term_pct
                            print(f"  短期贷款特例: 匹配剩余期限区间 {remaining_term_bucket}, 权重={remaining_term_pct:.4f}")
                else:
                    # 原有逻辑：只考虑右边界小于等于贷款期限的剩余期限
                    for remaining_term_bucket, remaining_term_pct in remaining_term_buckets.items():
                        # 确保RemainingTerm <= LoanTerm
                        if remaining_term_bucket[1] <= loan_term:
                            compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                            total_compatible_pct += remaining_term_pct
                
                # 如果没有兼容的RemainingTerm，跳过此组合
                if not compatible_remaining_terms:
                    continue
                
                # 重新归一化兼容的RemainingTerm概率
                if total_compatible_pct > 0:
                    for bucket in compatible_remaining_terms:
                        compatible_remaining_terms[bucket] /= total_compatible_pct
                
                # 为每个兼容的RemainingTerm计算配额
                for remaining_term_bucket, normalized_pct in compatible_remaining_terms.items():
                    # 获取此剩余期限区间的金额系数
                    rt_factor = 1  # 不使用金额系数
                    
                    # 计算权重时，考虑金额系数的反比 - 金额系数越大，需要更少的贷款笔数
                    # 反向调整：金额系数越大，配额越少；金额系数越小，配额越多
                    adjusted_pct = normalized_pct / rt_factor
                    
                    # 计算此组合的贷款配额
                    # 计算此组合应该有多少贷款
                    loan_quota = int(total_loans * sub_category_pct * loan_term_pct * credit_rating_pct * adjusted_pct)
                    
                    if loan_quota > 0:
                        # 配额上取整，确保每个组合至少有一个贷款
                        loan_quota = max(1, loan_quota)
                        loan_quotas[(asset_sub_category, loan_term, remaining_term_bucket, credit_rating)] = loan_quota
                        valid_combinations += 1
    
    print(f"计算得到{valid_combinations}个有效组合，总配额: {sum(loan_quotas.values())}")
    
    # 调整配额以匹配总数
    total_quota = sum(loan_quotas.values())
    if total_quota != total_loans:
        scale_factor = total_loans / total_quota
        for key in loan_quotas:
            loan_quotas[key] = max(1, int(loan_quotas[key] * scale_factor))
        
        # 微调以确保总数正好等于total_loans
        current_total = sum(loan_quotas.values())
        if current_total != total_loans:
            diff = total_loans - current_total
            # 按配额大小排序的键
            sorted_keys = sorted(loan_quotas.keys(), key=lambda k: loan_quotas[k], reverse=(diff > 0))
            
            # 分配差额
            for key in sorted_keys[:abs(diff)]:
                loan_quotas[key] += 1 if diff > 0 else -1
                if loan_quotas[key] < 1:
                    loan_quotas[key] = 1  # 确保每个组合至少有一个贷款
    
    # 步骤2: 按配额生成贷款
    loans = []
    cashflow_dfs = []
    spent = 0
    target_amount = available_cash
    
    # 为不同剩余期限区间设置贷款余额系数
    # 剩余期限越长，贷款余额系数越大；剩余期限越短，系数越小
    remaining_term_amount_factors = {}
    
    # 提取所有剩余期限区间并排序
    all_remaining_term_buckets = sorted(set([combo[2] for combo in loan_quotas.keys()]), key=lambda b: b[1])
    
    # 设置所有剩余期限区间系数为1.0
    print("\n--- 使用统一固定贷款余额 ---")    
    for rt_bucket in all_remaining_term_buckets:
        remaining_term_amount_factors[rt_bucket] = 1.0
    
    # 按剩余期限区间上限从大到小排序，优先处理长期剩余期限的贷款
    print("\n--- 按剩余期限从长到短的优先级排序配额 ---")
    sorted_combinations = sorted(
        loan_quotas.keys(), 
        key=lambda k: k[2][1] if isinstance(k[2], tuple) else 0, 
        reverse=True
    )
    
    for rt_bucket in sorted(set([combo[2] for combo in sorted_combinations]), key=lambda b: b[1], reverse=True):
        print(f"剩余期限区间 {rt_bucket} 优先级较高")
    
    # 按优先级顺序处理贷款生成
    for combination in sorted_combinations:
        quota = loan_quotas[combination]
        asset_sub_category, loan_term, remaining_term_bucket, credit_rating = combination
        
        # 获取当前剩余期限区间的金额系数
        amount_factor = remaining_term_amount_factors[remaining_term_bucket]
        
        # 生成剩余期限
        # 在剩余期限区间内进行随机抽样（左开右闭区间）
        # 剩余期限应该是区间内的随机值，而不是固定的右边界
        # 例如，对于区间(9.0, 10.0]，可以是9.1-10.0之间的任意值
        # 为避免浮点数问题，先转换为整数区间再随机选择
        left = int(remaining_term_bucket[0])  # 左边界（不含）
        right = int(remaining_term_bucket[1]) # 右边界（含）
        # 确保剩余期限不大于贷款期限
        right = min(right, loan_term)
        # 在区间内随机选择（左边界+1到右边界之间）
        remaining_term = random.randint(left + 1, right)
        seasoning = int(loan_term - remaining_term)
        
        # 记录生成的组合详细信息
        if counter % 100 == 0:  # 每100笔贷款记录一次，避免日志过多
            print(f"贷款#{counter}: 子类别={asset_sub_category}, 贷款期限={loan_term}, " +
                  f"剩余期限桶={remaining_term_bucket}, 实际剩余期限={remaining_term}, " +
                  f"账龄={loan_term - remaining_term}, 信用评级={credit_rating}")
        
        # START OF ALERT: 不可修改的代码块
        # 至此，已确定4个维度的取值
        # 根据 Analysis_AssetGroups 表中的分组利率，确定此笔资产的利率
        # dimension1 = AssetSubCategory, dimension2 = CreditRating, dimension3 = LoanTerm, dimension4 = Seasoning
        #time_x = datetime.now()
        group_id, interest_rate = match_asset_group(
            df_asset_groups_interest_rates,
            grouping_configs,
            asset_category,
            asset_sub_category,
            loan_term,
            seasoning,
            credit_rating,
            asset_group_cache
        )
        # END OF ALERT: 不可修改的代码块
        
        # 为此组合生成指定数量的贷款
        for _ in range(quota):
            # 检查是否已达到或接近目标金额
            if spent >= target_amount:
                break
                
            # 使用统一贷款余额
            remaining_balance = fixed_loan_balance
            
            # 如果是最后一笔贷款，调整金额以精确匹配目标总额
            remaining_target = target_amount - spent
            if remaining_target < remaining_balance:
                remaining_balance = remaining_target
            
            # START OF ALERT: 不可修改的代码块
            # 生成贷款ID和其他属性
            loan_start_date = random_date(start_date, end_date)

            # 资产的放款日区间应为 pool_date - (seasoning+1)月 至 pool_date - seasoning月 之间
            issue_date_start = pool_date - relativedelta(months=loan_term - remaining_term + 1)
            issue_date_end = pool_date - relativedelta(months=loan_term - remaining_term)
            issue_date = random_date(issue_date_start, issue_date_end)

            seasoning_days = (pool_date - issue_date).days

            # loan_interest是上一兑付日（即资产起始日）到当前日期之间的利息
            cf, loan_interest, remaining_term_days = generate_cashflows_for_single_loan(
                index,
                group_id,
                remaining_balance,
                remaining_term,
                interest_rate,
                loan_start_date,
                pool_date,
            )

            # 默认资产价格为其本金
            discount_factor = 1
            loan_value = remaining_balance

            if discount_method >= 1:
                # 若基于本金折价
                if discount_method == 1:
                    loan_value = remaining_balance / discount_factor
                # 若基于本息折价，但利息不折价
                elif discount_method == 2:
                    loan_value = remaining_balance / discount_factor + loan_interest
                # 若基于本息折价，且利息折价
                elif discount_method == 3:
                    loan_value = (
                        remaining_balance / discount_factor + loan_interest / discount_factor
                    )

            price = loan_value

            # 创建贷款记录
            loans.append(
                {
                    "loan_id": index,
                    "remaining_balance": remaining_balance,
                    "asset_category": asset_category,
                    "asset_sub_category": asset_sub_category,
                    "loan_term": loan_term,
                    "seasoning": loan_term - remaining_term,
                    "seasoning_days": seasoning_days,
                    "remaining_term": remaining_term,
                    "remaining_term_days": remaining_term_days,
                    "credit_rating": credit_rating,
                    "interest_rate": interest_rate,
                    "wal": None,
                    "discount_factor": discount_factor,
                    "group_id": group_id,
                }
            )            

            cashflow_dfs.append(cf)
            
            spent += price

            index += 1
            counter += 1
            # END OF ALERT
            
            # 如果已达到目标金额，提前退出
            if spent >= target_amount:
                break
    
    # 转换为DataFrame并计算每个维度的实际分布
    loans_df = pd.DataFrame(loans)
    
    # 记录最终统计信息
    if not loans_df.empty:
        print("\n=== 贷款生成完成，最终统计 ===")
        print(f"生成贷款总数: {len(loans_df)}")
        print(f"总金额: {spent:.2f} / 目标金额: {target_amount:.2f}")
        
        # 计算和显示实际分布
        total_balance = loans_df["remaining_balance"].sum()
        
        print("\n--- 实际资产子类别分布 ---")
        for sub_cat in sorted(loans_df["asset_sub_category"].unique()):
            balance = loans_df[loans_df["asset_sub_category"] == sub_cat]["remaining_balance"].sum()
            pct = balance / total_balance
            print(f"子类别 {sub_cat}: {pct:.4f}")
            
        print("\n--- 实际贷款期限分布 ---")
        for term in sorted(loans_df["loan_term"].unique()):
            balance = loans_df[loans_df["loan_term"] == term]["remaining_balance"].sum()
            pct = balance / total_balance
            print(f"期限 {term}: {pct:.4f}")
            
        print("\n--- 实际剩余期限分布 ---")
        for term in sorted(loans_df["remaining_term"].unique()):
            balance = loans_df[loans_df["remaining_term"] == term]["remaining_balance"].sum()
            pct = balance / total_balance
            print(f"剩余期限 {term}: {pct:.4f}")
            
        print("\n--- 账龄分布 ---")
        for seasoning in sorted(loans_df["seasoning"].unique()):
            balance = loans_df[loans_df["seasoning"] == seasoning]["remaining_balance"].sum()
            pct = balance / total_balance
            print(f"账龄 {seasoning}: {pct:.4f}")
    
    # 转换现金流DataFrame
    cashflows_df = pd.concat(cashflow_dfs) if cashflow_dfs else pd.DataFrame()
    
    return loans_df, cashflows_df

'''
# 函数：使用二阶段生成方案的分层配额抽样方法生成资产
def generate_assets_from_distribution_stratified_2stage(
    asset_category,
    asset_subcategory_buckets,
    loan_term_buckets,
    remaining_term_buckets,
    credit_rating_buckets,
    available_cash,
    pool_date,
    average_loan_amount,
    df_asset_groups_interest_rates,
    grouping_configs,
    discount_method,
    asset_group_cache
):
    """
    使用二阶段生成方案的分层抽样方法
    第一阶段：使用当前方法生成约80%的贷款
    第二阶段：分析第一阶段结果，针对性地生成剩余20%以补偿偏差
    
    Args:
        asset_category: 资产类别
        asset_subcategory_buckets: 子类别分布
        loan_term_buckets: 贷款期限分布
        remaining_term_buckets: 剩余期限分布
        credit_rating_buckets: 信用评级分布
        available_cash: 可用资金
        pool_date: 资产池日期
        average_loan_amount: 平均贷款余额
        df_asset_groups_interest_rates: 资产组利率数据
        grouping_configs: 分组配置
        discount_method: 折现方法
        asset_group_cache: 资产组缓存
        
    Returns:
        (loans_df, cashflows_df): 贷款DataFrame和现金流DataFrame
    """
    # 参数检查和边界处理
    if not isinstance(pool_date, datetime):
        raise TypeError("pool_date必须是datetime类型")
        
    if not isinstance(asset_subcategory_buckets, dict) or not asset_subcategory_buckets:
        raise ValueError("asset_subcategory_buckets必须是非空字典")
        
    if not isinstance(loan_term_buckets, dict) or not loan_term_buckets:
        raise ValueError("loan_term_buckets必须是非空字典")
        
    if not isinstance(remaining_term_buckets, dict) or not remaining_term_buckets:
        raise ValueError("remaining_term_buckets必须是非空字典")
        
    if not isinstance(credit_rating_buckets, dict) or not credit_rating_buckets:
        raise ValueError("credit_rating_buckets必须是非空字典")
        
    if available_cash <= 0:
        raise ValueError(f"available_cash必须大于0，当前值: {available_cash}")
        
    if average_loan_amount <= 0:
        raise ValueError(f"average_loan_amount必须大于0，当前值: {average_loan_amount}")
        
    if asset_category not in grouping_configs:
        raise ValueError(f"未找到资产类别 {asset_category} 的分组配置")
    
    print(f"\n=== 开始二阶段分层抽样生成资产 ===")
    print(f"资产类别: {asset_category}")
    print(f"可用资金: {available_cash:.2f}")
    print(f"资产池日期: {pool_date}")
    print(f"平均贷款余额: {average_loan_amount:.2f}")
    
    # 打印输入的分布信息
    print(f"\n--- 目标资产子类别分布 ---")
    for bucket, pct in sorted(asset_subcategory_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 目标贷款期限分布 ---")  
    for bucket, pct in sorted(loan_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 目标剩余期限分布 ---")
    for bucket, pct in sorted(remaining_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 目标信用评级分布 ---")
    for bucket, pct in sorted(credit_rating_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
    
    # 设置一阶段生成的贷款比例（80%）
    stage_1_ratio = 0.8
    
    # 第一阶段：生成80%的贷款
    print(f"\n=== 第一阶段：生成{stage_1_ratio*100:.0f}%的贷款 ===")
    stage_1_cash = available_cash * stage_1_ratio
    stage_1_loans_df, stage_1_cashflow_df, stage_1_index = generate_assets_from_distribution_stratified(
        asset_category,
        asset_subcategory_buckets,
        loan_term_buckets,
        remaining_term_buckets,
        credit_rating_buckets,
        stage_1_cash,
        pool_date,
        average_loan_amount,
        df_asset_groups_interest_rates,
        grouping_configs,
        discount_method,
        asset_group_cache,
        0
    )
    
    # 如果第一阶段没有生成任何贷款，则返回空结果
    if stage_1_loans_df.empty:
        print("第一阶段未生成任何贷款，请检查参数")
        return pd.DataFrame(), pd.DataFrame()
    
    # 计算第一阶段的实际分布
    print(f"\n=== 分析第一阶段结果 ===")
    stage_1_total_balance = stage_1_loans_df["remaining_balance"].sum()
    
    # 计算实际剩余期限分布
    stage_1_remaining_term_dist = {}
    for bucket in remaining_term_buckets.keys():
        bucket_mask = (stage_1_loans_df["remaining_term"] > bucket[0]) & (stage_1_loans_df["remaining_term"] <= bucket[1])
        bucket_balance = stage_1_loans_df[bucket_mask]["remaining_balance"].sum()
        stage_1_remaining_term_dist[bucket] = bucket_balance / stage_1_total_balance if stage_1_total_balance > 0 else 0
    
    print("\n--- 第一阶段实际剩余期限分布 ---")
    for bucket, pct in sorted(stage_1_remaining_term_dist.items()):
        target_pct = remaining_term_buckets[bucket]
        deviation = (pct - target_pct) * 100  # 转换为百分比
        print(f"区间 {bucket}: 当前={pct:.4f}, 目标={target_pct:.4f}, 偏差={deviation:.2f}%")
    
    # 计算第二阶段需要的调整分布
    # 如果某个区间在第一阶段低于目标，则在第二阶段增加权重；反之则降低权重
    stage_2_remaining_term_buckets = {}
    
    # 计算总偏差，用于归一化
    total_positive_deviation = 0
    total_negative_deviation = 0
    
    for bucket, target_pct in remaining_term_buckets.items():
        actual_pct = stage_1_remaining_term_dist.get(bucket, 0)
        deviation = target_pct - actual_pct
        
        if deviation > 0:  # 低于目标，需要增加
            total_positive_deviation += deviation
        else:  # 高于目标，需要减少
            total_negative_deviation += abs(deviation)
    
    # 防止除零错误
    if total_positive_deviation == 0:
        total_positive_deviation = 1
    if total_negative_deviation == 0:
        total_negative_deviation = 1
    
    # 计算调整后的第二阶段分布
    for bucket, target_pct in remaining_term_buckets.items():
        actual_pct = stage_1_remaining_term_dist.get(bucket, 0)
        deviation = target_pct - actual_pct
        
        if deviation > 0:  # 低于目标，在第二阶段增加比例
            # 增加的比例与偏差大小成正比
            adjustment_factor = 2.0  # 可调整的放大因子
            weight = (deviation / total_positive_deviation) * adjustment_factor
        else:  # 高于目标，在第二阶段降低比例
            weight = 0.01  # 给一个很小的权重，但不是零
        
        stage_2_remaining_term_buckets[bucket] = max(0.01, weight)  # 确保权重不为零
    
    # 归一化第二阶段分布
    total_weight = sum(stage_2_remaining_term_buckets.values())
    for bucket in stage_2_remaining_term_buckets:
        stage_2_remaining_term_buckets[bucket] /= total_weight
    
    print("\n--- 第二阶段调整后的剩余期限分布 ---")
    for bucket, pct in sorted(stage_2_remaining_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
    
    # 第二阶段：使用调整后的分布生成剩余20%的贷款
    print(f"\n=== 第二阶段：生成剩余{(1-stage_1_ratio)*100:.0f}%的贷款 ===")
    stage_2_cash = available_cash - stage_1_cash
    
    stage_2_loans_df, stage_2_cashflow_df, stage_2_index = generate_assets_from_distribution_stratified(
        asset_category,
        asset_subcategory_buckets,
        loan_term_buckets,
        stage_2_remaining_term_buckets,  # 使用调整后的剩余期限分布
        credit_rating_buckets,
        stage_2_cash,
        pool_date,
        average_loan_amount,
        df_asset_groups_interest_rates,
        grouping_configs,
        discount_method,
        asset_group_cache,
        stage_1_index,
    )
    
    # 合并两个阶段的结果
    combined_loans_df = pd.concat([stage_1_loans_df, stage_2_loans_df]).reset_index(drop=True)
    combined_cashflow_df = pd.concat([stage_1_cashflow_df, stage_2_cashflow_df]).reset_index(drop=True)
    
    # 计算最终的分布情况
    if not combined_loans_df.empty:
        print("\n=== 最终分布情况（两阶段合并后）===")
        
        # 计算总金额
        total_balance = combined_loans_df["remaining_balance"].sum()
        
        # 计算最终剩余期限分布
        final_remaining_term_dist = {}
        for bucket in remaining_term_buckets.keys():
            bucket_mask = (combined_loans_df["remaining_term"] > bucket[0]) & (combined_loans_df["remaining_term"] <= bucket[1])
            bucket_balance = combined_loans_df[bucket_mask]["remaining_balance"].sum()
            final_remaining_term_dist[bucket] = bucket_balance / total_balance if total_balance > 0 else 0
        
        print("\n--- 最终剩余期限分布 ---")
        for bucket, pct in sorted(final_remaining_term_dist.items()):
            target_pct = remaining_term_buckets[bucket]
            deviation = (pct - target_pct) * 100  # 转换为百分比
            print(f"区间 {bucket}: 最终={pct:.4f}, 目标={target_pct:.4f}, 偏差={deviation:.2f}%")
        
        # 计算最终的贷款期限分布
        final_loan_term_dist = {}
        for bucket in loan_term_buckets.keys():
            bucket_mask = (combined_loans_df["loan_term"] > bucket[0]) & (combined_loans_df["loan_term"] <= bucket[1])
            bucket_balance = combined_loans_df[bucket_mask]["remaining_balance"].sum()
            final_loan_term_dist[bucket] = bucket_balance / total_balance if total_balance > 0 else 0
        
        print("\n--- 最终贷款期限分布 ---")
        for bucket, pct in sorted(final_loan_term_dist.items()):
            target_pct = loan_term_buckets[bucket]
            deviation = (pct - target_pct) * 100  # 转换为百分比
            print(f"区间 {bucket}: 最终={pct:.4f}, 目标={target_pct:.4f}, 偏差={deviation:.2f}%")
        
        # 计算资产子类别分布
        print("\n--- 最终资产子类别分布 ---")
        for sub_cat in sorted(combined_loans_df["asset_sub_category"].unique()):
            balance = combined_loans_df[combined_loans_df["asset_sub_category"] == sub_cat]["remaining_balance"].sum()
            pct = balance / total_balance
            print(f"子类别 {sub_cat}: {pct:.4f}")
            
        # 计算信用评级分布
        print("\n--- 最终信用评级分布 ---")
        for rating in sorted(credit_rating_buckets.keys()):
            bucket_mask = (combined_loans_df["credit_rating"] > rating[0]) & (combined_loans_df["credit_rating"] <= rating[1])
            bucket_balance = combined_loans_df[bucket_mask]["remaining_balance"].sum()
            final_rating_dist = bucket_balance / total_balance if total_balance > 0 else 0
            target_pct = credit_rating_buckets[rating]
            deviation = (final_rating_dist - target_pct) * 100
            print(f"区间 {rating}: 最终={final_rating_dist:.4f}, 目标={target_pct:.4f}, 偏差={deviation:.2f}%")
        
        # 账龄分布
        print("\n--- 账龄分布 ---")
        for seasoning in sorted(combined_loans_df["seasoning"].unique()):
            balance = combined_loans_df[combined_loans_df["seasoning"] == seasoning]["remaining_balance"].sum()
            pct = balance / total_balance
            print(f"账龄 {seasoning}: {pct:.4f}")
    
    print(f"\n=== 贷款生成完成 ===")
    print(f"生成贷款总数: {len(combined_loans_df)}")
    print(f"总金额: {combined_loans_df['remaining_balance'].sum():.2f} / 目标金额: {available_cash:.2f}")
    
    return combined_loans_df, combined_cashflow_df
'''

# 函数：使用预计算配额调整法+分层配额抽样方法生成资产
# ! 此函数不允许改动
def generate_assets_from_distribution_stratified_adjusted(
    asset_category,
    asset_subcategory_buckets,
    loan_term_buckets,
    remaining_term_buckets,
    credit_rating_buckets,
    available_cash,
    pool_date,
    average_loan_amount,
    df_asset_groups_interest_rates,
    grouping_configs,
    discount_method,
    asset_group_cache,
    num_of_pre_existing_loans = 0
):
    """
    使用分层配额抽样方法生成资产，确保生成的资产分布与目标分布更加接近。
    
    Args:
        asset_category: 资产类别
        asset_subcategory_buckets: 子类别分布
        loan_term_buckets: 贷款期限分布
        remaining_term_buckets: 剩余期限分布
        credit_rating_buckets: 信用评级分布
        available_cash: 可用资金
        pool_date: 资产池日期
        average_loan_amount: 平均贷款余额
        df_asset_groups_interest_rates: 资产组利率数据
        grouping_configs: 分组配置
        discount_method: 折现方法
        asset_group_cache: 资产组缓存
        num_of_pre_existing_loans: 调用此方法时, 该asset_category已生成的贷款数量，新的loan_id从这里开始
        
    Returns:
        (loans_df, cashflows_df): 贷款DataFrame和现金流DataFrame
    """
    # 输出函数名
    print(f"\n=== {generate_assets_from_distribution_stratified_adjusted.__name__} ===")
    # 参数检查和边界处理
    if not isinstance(pool_date, datetime):
        raise TypeError("pool_date必须是datetime类型")
        
    if not isinstance(asset_subcategory_buckets, dict) or not asset_subcategory_buckets:
        raise ValueError("asset_subcategory_buckets必须是非空字典")
        
    if not isinstance(loan_term_buckets, dict) or not loan_term_buckets:
        raise ValueError("loan_term_buckets必须是非空字典")
        
    if not isinstance(remaining_term_buckets, dict) or not remaining_term_buckets:
        raise ValueError("remaining_term_buckets必须是非空字典")
        
    if not isinstance(credit_rating_buckets, dict) or not credit_rating_buckets:
        raise ValueError("credit_rating_buckets必须是非空字典")
        
    if available_cash <= 0:
        raise ValueError(f"available_cash必须大于0，当前值: {available_cash}")
        
    if average_loan_amount <= 0:
        raise ValueError(f"average_loan_amount必须大于0，当前值: {average_loan_amount}")
        
    if asset_category not in grouping_configs:
        raise ValueError(f"未找到资产类别 {asset_category} 的分组配置")
    
    # 预计算配额调整 - 先调整剩余期限分布权重
    adjusted_remaining_term_buckets = remaining_term_buckets
    
    # 生成资产的剩余本金为指定的值
    remaining_balance = average_loan_amount

    # 令资产“上一起息日”在过去一个月内均匀分布，以产生连续均匀的按日归集现金流
    # 但注意资产的起始日应基于seasoning确定
    start_date = pool_date - relativedelta(months=1)
    end_date = pool_date - relativedelta(days=1)
    
    # 计算贷款ID起始值
    loan_id_start = asset_category * LOAN_ID_SEGMENT + num_of_pre_existing_loans
    index = loan_id_start
    counter = 0

    # 检查分布权重之和是否接近1
    for name, buckets in [
        ("资产子类别", asset_subcategory_buckets),
        ("贷款期限", loan_term_buckets),
        ("剩余期限", adjusted_remaining_term_buckets),  # 使用调整后的分布
        ("信用评级", credit_rating_buckets)
    ]:
        weight_sum = sum(buckets.values())
        if not 0.99 <= weight_sum <= 1.01:  # 允许1%的误差
            print(f"警告: {name}分布的权重之和为{weight_sum:.4f}，期望值为1.0")
    
    # 打印输入参数以便于分析
    print(f"\n=== 开始分层抽样生成资产（使用预计算配额调整法）===")
    print(f"资产类别: {asset_category}")
    print(f"可用资金: {available_cash:.2f}")
    print(f"资产池日期: {pool_date}")
    print(f"平均贷款余额: {average_loan_amount:.2f}")
    print(f"折现方法: {discount_method}")
    
    print(f"\n--- 资产子类别分布 ---")
    for bucket, pct in sorted(asset_subcategory_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 贷款期限分布 ---")  
    for bucket, pct in sorted(loan_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 剩余期限分布（调整后）---")
    for bucket, pct in sorted(adjusted_remaining_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 信用评级分布 ---")
    for bucket, pct in sorted(credit_rating_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
    
    # 估算的贷款数量，目标约4000笔
    estimated_total_loans = int(available_cash / average_loan_amount)
    # 确保数量在合理范围，且不会导致单笔贷款余额过小
    total_loans = min(max(3000, estimated_total_loans), 5000)
    print(f"\n估算贷款数量: {estimated_total_loans}，采用目标数量: {total_loans}")
    
    # 计算固定贷款余额（平均分配总预算）
    fixed_loan_balance = available_cash / total_loans
    print(f"固定贷款余额: {fixed_loan_balance:.2f}")
    
    # 预处理信用评级值和权重，用于后续随机抽样
    credit_rating_values = []
    credit_rating_weights = []
    
    for credit_rating_bucket, weight in credit_rating_buckets.items():
        left, right = credit_rating_bucket
        # 使用区间的右边界值作为信用评级值
        credit_rating_values.append(right)
        credit_rating_weights.append(weight)
    
    print(f"\n--- 信用评级抽样准备 ---")
    print(f"评级值: {credit_rating_values}")
    print(f"权重: {credit_rating_weights}")
    
    # 为不同剩余期限区间设置贷款余额系数
    remaining_term_amount_factors = {}

    # 提取所有剩余期限区间并排序
    all_remaining_term_buckets = sorted(set(adjusted_remaining_term_buckets.keys()), key=lambda b: b[1])

    # 设置所有剩余期限区间系数为1.0
    print("\n--- 使用统一固定贷款余额 ---")    
    for rt_bucket in all_remaining_term_buckets:
        remaining_term_amount_factors[rt_bucket] = 1.0    
    
    # 第0步: 自适应计算剩余期限区间的校正系数
    print("\n--- 开始计算剩余期限校正系数 ---")
    
    # 进行初始配额模拟计算，不实际生成贷款
    remaining_term_quotas = {bucket: 0 for bucket in adjusted_remaining_term_buckets.keys()}
    total_quota_allocated = 0
    
    # 模拟计算每个维度组合的初始配额
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            loan_term = int(loan_term_bucket[1])  # 使用右边界
            
            # 找出与当前贷款期限兼容的剩余期限
            compatible_remaining_terms = {}
            total_compatible_pct = 0
            
            if loan_term <= 3:  # 特殊处理短期贷款
                for rt_bucket, rt_pct in adjusted_remaining_term_buckets.items():
                    if rt_bucket[0] < loan_term:  # 放宽匹配条件
                        compatible_remaining_terms[rt_bucket] = rt_pct
                        total_compatible_pct += rt_pct
            else:
                # 标准匹配：剩余期限 <= 贷款期限
                for rt_bucket, rt_pct in adjusted_remaining_term_buckets.items():
                    if rt_bucket[1] <= loan_term:
                        compatible_remaining_terms[rt_bucket] = rt_pct
                        total_compatible_pct += rt_pct
            
            # 若无兼容剩余期限，跳过此组合
            if not compatible_remaining_terms:
                continue
            
            # 重新归一化兼容的剩余期限概率
            if total_compatible_pct > 0:
                for bucket in compatible_remaining_terms:
                    compatible_remaining_terms[bucket] /= total_compatible_pct
            
            # 计算每个兼容剩余期限区间的初始配额
            for rt_bucket, normalized_pct in compatible_remaining_terms.items():
                # 简单配额计算，不应用任何校正因子
                quota = int(total_loans * sub_category_pct * loan_term_pct * normalized_pct)
                if quota > 0:
                    remaining_term_quotas[rt_bucket] += quota
                    total_quota_allocated += quota
    
    # 计算初始配额下各剩余期限区间的实际分布比例
    initial_rt_distribution = {}
    if total_quota_allocated > 0:
        for bucket, quota in remaining_term_quotas.items():
            initial_rt_distribution[bucket] = quota / total_quota_allocated
    
    # 计算剩余期限区间的校正系数
    rt_correction_factors = {}
    print("\n剩余期限区间校正系数计算:")
    for bucket in adjusted_remaining_term_buckets:
        target_pct = adjusted_remaining_term_buckets[bucket]
        initial_pct = initial_rt_distribution.get(bucket, 0)
        
        if initial_pct > 0:
            # 校正系数 = 目标分布比例 / 初始分布比例
            correction = target_pct / initial_pct
            # 限制校正系数在合理范围内
            correction = max(0.5, min(2.0, correction))
        else:
            correction = 1.0  # 默认不变
            
        rt_correction_factors[bucket] = correction
        print(f"区间 {bucket}: 目标比例={target_pct:.4f}, 初始比例={initial_pct:.4f}, 校正系数={correction:.4f}")
    
    print("--- 剩余期限校正系数计算完成 ---\n")
    
    # 步骤1: 预先计算贷款配额
    loan_quotas = {}
    valid_combinations = 0
    
    # 遍历所有可能的组合 (不包括信用评级)
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            # 贷款期限必须是区间右值（离散固定值），例如3个月、6个月、12个月等
            loan_term = int(loan_term_bucket[1])  # 使用右边界，不生成区间内随机值
            
            # 寻找与当前贷款期限兼容的剩余期限
            compatible_remaining_terms = {}
            total_compatible_pct = 0
            
            if loan_term <= 3:  # 对于3个月及以下的贷款
                print(f"处理短期贷款期限: {loan_term}个月，放宽匹配条件")
                # 从剩余期限区间中找出左边界小于loan_term的所有区间
                for remaining_term_bucket, remaining_term_pct in adjusted_remaining_term_buckets.items():
                    if remaining_term_bucket[0] < loan_term:  # 只要左边界小于贷款期限
                        compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                        total_compatible_pct += remaining_term_pct
                        print(f"  短期贷款特例: 匹配剩余期限区间 {remaining_term_bucket}, 权重={remaining_term_pct:.4f}")
            else:
                # 原有逻辑：只考虑右边界小于等于贷款期限的剩余期限
                for remaining_term_bucket, remaining_term_pct in adjusted_remaining_term_buckets.items():
                    # 确保RemainingTerm <= LoanTerm
                    if remaining_term_bucket[1] <= loan_term:
                        compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                        total_compatible_pct += remaining_term_pct
            
            # 如果没有兼容的RemainingTerm，跳过此组合
            if not compatible_remaining_terms:
                continue
            
            # 重新归一化兼容的RemainingTerm概率
            if total_compatible_pct > 0:
                for bucket in compatible_remaining_terms:
                    compatible_remaining_terms[bucket] /= total_compatible_pct
            
            # 为每个兼容的RemainingTerm计算配额 (不包括信用评级维度)
            for remaining_term_bucket, normalized_pct in compatible_remaining_terms.items():
                # 获取此剩余期限区间的金额系数
                rt_factor = remaining_term_amount_factors[remaining_term_bucket]
                
                # 计算权重时，考虑金额系数的反比 - 金额系数越大，需要更少的贷款笔数
                # 反向调整：金额系数越大，配额越少；金额系数越小，配额越多
                adjusted_pct = normalized_pct / rt_factor
                
                # 计算此组合的贷款配额
                # 计算此组合应该有多少贷款
                loan_quota = int(total_loans * sub_category_pct * loan_term_pct * adjusted_pct)
                
                if loan_quota > 0:
                    # 配额上取整，确保每个组合至少有一个贷款
                    loan_quota = max(1, loan_quota)
                    loan_quotas[(asset_sub_category, loan_term, remaining_term_bucket)] = loan_quota
                    valid_combinations += 1
    
    print(f"计算得到{valid_combinations}个有效组合，总配额: {sum(loan_quotas.values())}")
    
    # 调整配额以匹配总数
    total_quota = sum(loan_quotas.values())
    if total_quota != total_loans:
        scale_factor = total_loans / total_quota
        for key in loan_quotas:
            loan_quotas[key] = max(1, int(loan_quotas[key] * scale_factor))
            
        # 微调以确保总数正好等于total_loans
        total_quota = sum(loan_quotas.values())
        if total_quota != total_loans:
            diff = total_loans - total_quota
            # 按配额从大到小排序，调整最大的几个组合的配额
            sorted_keys = sorted(loan_quotas.keys(), key=lambda k: loan_quotas[k], reverse=True)
            
            # 分配差额
            for i in range(abs(diff)):
                key = sorted_keys[i % len(sorted_keys)]
                loan_quotas[key] += 1 if diff > 0 else -1 if loan_quotas[key] > 1 else 0
    
    # 步骤2: 按配额生成贷款
    loans = []
    cashflow_dfs = []
    spent = 0
    target_amount = available_cash
    
    # 按剩余期限区间上限从大到小排序，优先处理长期剩余期限的贷款
    print("\n--- 按剩余期限从长到短的优先级排序配额 ---")
    sorted_combinations = sorted(
        loan_quotas.keys(), 
        key=lambda k: k[2][1] if isinstance(k[2], tuple) else 0, 
        reverse=True
    )
    
    for rt_bucket in sorted(set([combo[2] for combo in sorted_combinations]), key=lambda b: b[1], reverse=True):
        print(f"剩余期限区间 {rt_bucket} 优先级较高")
    
    # 按优先级顺序处理贷款生成
    for combination in sorted_combinations:
        quota = loan_quotas[combination]
        asset_sub_category, loan_term, remaining_term_bucket = combination
        
        # 获取当前剩余期限区间的金额系数
        amount_factor = remaining_term_amount_factors[remaining_term_bucket]
        
        # 生成剩余期限
        # 在剩余期限区间内进行随机抽样（左开右闭区间）
        # 剩余期限应该是区间内的随机值，而不是固定的右边界
        # 例如，对于区间(9.0, 10.0]，可以是9.1-10.0之间的任意值
        # 为避免浮点数问题，先转换为整数区间再随机选择
        left = int(remaining_term_bucket[0])  # 左边界（不含）
        right = int(remaining_term_bucket[1]) # 右边界（含）
        # 确保剩余期限不大于贷款期限
        right = min(right, loan_term)
        # 在区间内随机选择（左边界+1到右边界之间）
        remaining_term = random.randint(left + 1, right)
        
        # 生成quota个贷款
        for _ in range(quota):
            # 检查是否达到预算上限
            if spent >= target_amount:
                print(f"已达到预算上限 {target_amount:.2f}，停止生成贷款")
                break
            
            # 随机抽样一个信用评级值，基于权重分布
            credit_rating = random.choices(credit_rating_values, weights=credit_rating_weights)[0]
            
            # 获取该组合的利率，增加容错机制
            interest_rate = None
            group_id = None
            
            # 计算已经过期限(seasoning)
            seasoning = loan_term - remaining_term
            
            try:
                # START OF ALERT: 不可修改的代码块
                # 至此，已确定4个维度的取值
                # 根据 Analysis_AssetGroups 表中的分组利率，确定此笔资产的利率
                # dimension1 = AssetSubCategory, dimension2 = CreditRating, dimension3 = LoanTerm, dimension4 = Seasoning
                #time_x = datetime.now()
                group_id, interest_rate = match_asset_group(
                    df_asset_groups_interest_rates,
                    grouping_configs,
                    asset_category,
                    asset_sub_category,
                    loan_term,
                    seasoning,
                    credit_rating,
                    asset_group_cache
                )
                # END OF ALERT
            except Exception as e:
                print(f"警告: 匹配资产组失败: Category={asset_category}, "
                      f"SubCategory={asset_sub_category}, "
                      f"LoanTerm={loan_term}, "
                      f"RemainingTerm={remaining_term}, "
                      f"Seasoning={seasoning}, "
                      f"CreditRating={credit_rating}")
                print(f"错误详情: {str(e)}")
                continue
            
            if group_id is None or interest_rate is None:
                print(f"警告: 未找到匹配的利率，跳过贷款生成")
                continue
            
            # 使用统一贷款余额
            remaining_balance = fixed_loan_balance
            
            # 如果是最后一笔贷款，调整金额以精确匹配目标总额
            remaining_target = target_amount - spent
            if remaining_target < remaining_balance:
                remaining_balance = remaining_target
                        
            # START OF ALERT: 以下这部分代码请不要做任何改动
            loan_start_date = random_date(start_date, end_date)

            # 资产的放款日区间应为 pool_date - (seasoning+1)月 至 pool_date - seasoning月 之间
            issue_date_start = pool_date - relativedelta(months=seasoning + 1)
            issue_date_end = pool_date - relativedelta(months=seasoning)
            issue_date = random_date(issue_date_start, issue_date_end)

            seasoning_days = (pool_date - issue_date).days

            # loan_interest是上一兑付日（即资产起始日）到当前日期之间的利息
            cf, loan_interest, remaining_term_days = generate_cashflows_for_single_loan(
                index,
                group_id,
                remaining_balance,
                remaining_term,
                interest_rate,
                loan_start_date,
                pool_date,
            )

            # 默认资产价格为其本金
            discount_factor = 1
            loan_value = remaining_balance

            if discount_method >= 1:
                # 若基于本金折价
                if discount_method == 1:
                    loan_value = remaining_balance / discount_factor
                # 若基于本息折价，但利息不折价
                elif discount_method == 2:
                    loan_value = remaining_balance / discount_factor + loan_interest
                # 若基于本息折价，且利息折价
                elif discount_method == 3:
                    loan_value = (
                        remaining_balance / discount_factor + loan_interest / discount_factor
                    )

            price = loan_value

            loans.append(
                {
                    "loan_id": index,
                    "remaining_balance": remaining_balance,
                    "asset_category": asset_category,
                    "asset_sub_category": asset_sub_category,
                    "loan_term": loan_term,
                    "seasoning": loan_term - remaining_term,
                    "seasoning_days": seasoning_days,
                    "remaining_term": remaining_term,
                    "remaining_term_days": remaining_term_days,
                    "credit_rating": credit_rating,
                    "interest_rate": interest_rate,
                    "wal": None,
                    "discount_factor": discount_factor,
                    "group_id": group_id,
                }
            )

            cashflow_dfs.append(cf)
            
            # 更新计数器和已使用金额
            index += 1
            counter += 1

            # END OF ALERT: 以上代码请不要做任何改动

            spent += price
            
            # 每生成100个贷款，显示进度
            if counter % 100 == 0:
                print(f"已生成 {counter} 个贷款，已使用金额 {spent:.2f} / {target_amount:.2f}")
            
            # 检查是否达到预算上限
            if spent >= target_amount:
                print(f"已达到预算上限 {target_amount:.2f}，停止生成贷款")
                break
    
    # 合并现金流DataFrame
    combined_cashflows_df = pd.concat(cashflow_dfs) if cashflow_dfs else pd.DataFrame()
    
    # 将贷款列表转换为DataFrame
    loans_df = pd.DataFrame(loans)
    
    # 打印生成结果总结
    avg_spent = spent / counter if counter > 0 else 0
    
    print(f"\n=== 生成资产总结 ===")
    print(f"生成的贷款总数: {counter}")
    print(f"总额: {spent:.2f} / {available_cash:.2f} ({100 * spent / available_cash:.2f}%)")
    print(f"平均贷款余额: {avg_spent:.2f}")
    
    # 返回生成的贷款和现金流DataFrame
    return loans_df, combined_cashflows_df, counter

def adjust_remaining_term_weights(loan_term_buckets, remaining_term_buckets):
    """
    预先调整剩余期限分布权重以适应贷款期限分布约束
    
    Args:
        loan_term_buckets: 贷款期限分布
        remaining_term_buckets: 剩余期限分布
        
    Returns:
        调整后的剩余期限分布
    """
    adjusted_remaining_term_buckets = remaining_term_buckets.copy()
    
    print("\n--- 预计算配额调整开始 ---")
    print("原始剩余期限分布:")
    for bucket, weight in sorted(remaining_term_buckets.items()):
        print(f"区间 {bucket}: {weight:.4f}")
    
    # 对每个贷款期限，计算compatible的剩余期限的权重总和
    for loan_term_bucket, loan_term_weight in loan_term_buckets.items():
        max_term = int(loan_term_bucket[1])
        
        # 找出兼容的剩余期限区间
        compatible_buckets = {}
        total_compatible_weight = 0
        
        # 特殊处理短期贷款
        if max_term <= 3 and total_compatible_weight < loan_term_weight:
            print(f"短期贷款期限 {max_term} 月，寻找其他兼容区间")
            # 对于短贷款期限，考虑左边界小于贷款期限的区间
            for rem_bucket, rem_weight in remaining_term_buckets.items():
                if rem_bucket[0] < max_term and rem_bucket not in compatible_buckets:
                    compatible_buckets[rem_bucket] = rem_weight
                    total_compatible_weight += rem_weight
                    print(f"  添加额外兼容区间 {rem_bucket}, 权重={rem_weight:.4f}")
        else:
            # 原有逻辑：只考虑右边界小于等于贷款期限的剩余期限
            for rem_bucket, rem_weight in remaining_term_buckets.items():
                # 确保RemainingTerm <= LoanTerm
                if rem_bucket[1] <= max_term:
                    compatible_buckets[rem_bucket] = rem_weight
                    total_compatible_weight += rem_weight
        
        print(f"贷款期限 {loan_term_bucket} (权重={loan_term_weight:.4f}) 兼容剩余期限总权重: {total_compatible_weight:.4f}")
        
        # 如果兼容权重小于贷款期限权重，需要调整
        if total_compatible_weight < loan_term_weight and total_compatible_weight > 0:
            # 计算需要放大的比例
            scale_factor = loan_term_weight / total_compatible_weight
            print(f"  权重不足，应用放大因子: {scale_factor:.4f}")
            
            # 按比例放大兼容区间权重
            for bucket in compatible_buckets:
                adjusted_remaining_term_buckets[bucket] *= scale_factor
                print(f"  调整区间 {bucket}: {remaining_term_buckets[bucket]:.4f} -> {adjusted_remaining_term_buckets[bucket]:.4f}")
    
    # 重新归一化
    total = sum(adjusted_remaining_term_buckets.values())
    if total > 0:
        for bucket in adjusted_remaining_term_buckets:
            adjusted_remaining_term_buckets[bucket] /= total
    
    print("\n调整后剩余期限分布:")
    for bucket, weight in sorted(adjusted_remaining_term_buckets.items()):
        print(f"区间 {bucket}: {weight:.4f}")
    print("--- 预计算配额调整结束 ---\n")
    
    return adjusted_remaining_term_buckets


# 函数：使用预计算配额调整法+分层配额抽样方法生成资产
def generate_assets_from_distribution_stratified_adjusted_v1(
    asset_category,
    asset_subcategory_buckets,
    loan_term_buckets,
    remaining_term_buckets,
    credit_rating_buckets,
    available_cash,
    pool_date,
    average_loan_amount,
    df_asset_groups_interest_rates,
    grouping_configs,
    discount_method,
    asset_group_cache,
    num_of_pre_existing_loans = 0
):
    """
    使用固定4000笔资产
    
    Args:
        asset_category: 资产类别
        asset_subcategory_buckets: 子类别分布
        loan_term_buckets: 贷款期限分布
        remaining_term_buckets: 剩余期限分布
        credit_rating_buckets: 信用评级分布
        available_cash: 可用资金
        pool_date: 资产池日期
        average_loan_amount: 平均贷款余额
        df_asset_groups_interest_rates: 资产组利率数据
        grouping_configs: 分组配置
        discount_method: 折现方法
        asset_group_cache: 资产组缓存
        num_of_pre_existing_loans: 调用此方法时, 该asset_category已生成的贷款数量，新的loan_id从这里开始
        
    Returns:
        (loans_df, cashflows_df): 贷款DataFrame和现金流DataFrame
    """
    # 输出函数名
    print(f"\n=== {generate_assets_from_distribution_stratified_adjusted_v1.__name__} ===")
    print(f"使用固定{CONST_NUM_OF_LOANS}笔资产")

    # 参数检查和边界处理
    if not isinstance(pool_date, datetime):
        raise TypeError("pool_date必须是datetime类型")
        
    if not isinstance(asset_subcategory_buckets, dict) or not asset_subcategory_buckets:
        raise ValueError("asset_subcategory_buckets必须是非空字典")
        
    if not isinstance(loan_term_buckets, dict) or not loan_term_buckets:
        raise ValueError("loan_term_buckets必须是非空字典")
        
    if not isinstance(remaining_term_buckets, dict) or not remaining_term_buckets:
        raise ValueError("remaining_term_buckets必须是非空字典")
        
    if not isinstance(credit_rating_buckets, dict) or not credit_rating_buckets:
        raise ValueError("credit_rating_buckets必须是非空字典")
        
    if available_cash <= 0:
        raise ValueError(f"available_cash必须大于0，当前值: {available_cash}")
        
    if average_loan_amount <= 0:
        raise ValueError(f"average_loan_amount必须大于0，当前值: {average_loan_amount}")
        
    if asset_category not in grouping_configs:
        raise ValueError(f"未找到资产类别 {asset_category} 的分组配置")
    
    # 预计算配额调整 - 先调整剩余期限分布权重
    adjusted_remaining_term_buckets = remaining_term_buckets
    
    # 生成资产的剩余本金为指定的值
    fixed_loan_balance = available_cash / CONST_NUM_OF_LOANS

    # 打印输入参数以便于分析
    print(f"\n=== 开始分层抽样生成资产（使用固定数量和金额）===")
    print(f"资产类别: {asset_category}")
    print(f"可用资金: {available_cash:.2f}")
    print(f"贷款数量: {CONST_NUM_OF_LOANS}")
    print(f"固定贷款余额: {fixed_loan_balance:.2f}")
    print(f"资产池日期: {pool_date}")
    print(f"折现方法: {discount_method}")

    # 令资产“上一起息日”在过去一个月内均匀分布，以产生连续均匀的按日归集现金流
    # 但注意资产的起始日应基于seasoning确定
    start_date = pool_date - relativedelta(months=1)
    end_date = pool_date - relativedelta(days=1)
    
    # 计算贷款ID起始值
    loan_id_start = asset_category * LOAN_ID_SEGMENT + num_of_pre_existing_loans
    index = loan_id_start
    counter = 0

    # 检查分布权重之和是否接近1
    for name, buckets in [
        ("资产子类别", asset_subcategory_buckets),
        ("贷款期限", loan_term_buckets),
        ("剩余期限", remaining_term_buckets), 
        ("信用评级", credit_rating_buckets)
    ]:
        weight_sum = sum(buckets.values())
        if not 0.99 <= weight_sum <= 1.01:  # 允许1%的误差
            print(f"警告: {name}分布的权重之和为{weight_sum:.4f}，期望值为1.0")
    
    print(f"\n--- 资产子类别分布 ---")
    for bucket, pct in sorted(asset_subcategory_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 贷款期限分布 ---")  
    for bucket, pct in sorted(loan_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 剩余期限分布 ---")
    for bucket, pct in sorted(adjusted_remaining_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 信用评级分布 ---")
    for bucket, pct in sorted(credit_rating_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
    
    # 预处理信用评级值和权重，用于后续随机抽样
    credit_rating_values = []
    credit_rating_weights = []
    
    for credit_rating_bucket, weight in credit_rating_buckets.items():
        left, right = credit_rating_bucket
        # 使用区间的右边界值作为信用评级值
        credit_rating_values.append(right)
        credit_rating_weights.append(weight)
    
    print(f"\n--- 信用评级抽样准备 ---")
    print(f"评级值: {credit_rating_values}")
    print(f"权重: {credit_rating_weights}")
    
    # 提取所有剩余期限区间并排序
    all_remaining_term_buckets = sorted(set(remaining_term_buckets.keys()), key=lambda b: b[1])
    
    # 第0步: 自适应计算剩余期限区间的校正系数
    print("\n--- 开始计算剩余期限校正系数 ---")
    
    # 进行初始配额模拟计算，不实际生成贷款
    remaining_term_quotas = {bucket: 0 for bucket in remaining_term_buckets.keys()}
    total_quota_allocated = 0
    
    # 模拟计算每个维度组合的初始配额
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            loan_term = int(loan_term_bucket[1])  # 使用右边界
            
            # 找出与当前贷款期限兼容的剩余期限
            compatible_remaining_terms = {}
            total_compatible_pct = 0
            
            if loan_term <= 3:  # 特殊处理短期贷款
                for rt_bucket, rt_pct in remaining_term_buckets.items():
                    if rt_bucket[0] < loan_term:  # 放宽匹配条件
                        compatible_remaining_terms[rt_bucket] = rt_pct
                        total_compatible_pct += rt_pct
            else:
                # 标准匹配：剩余期限 <= 贷款期限
                for rt_bucket, rt_pct in remaining_term_buckets.items():
                    if rt_bucket[1] <= loan_term:
                        compatible_remaining_terms[rt_bucket] = rt_pct
                        total_compatible_pct += rt_pct
            
            # 若无兼容剩余期限，跳过此组合
            if not compatible_remaining_terms:
                continue
            
            # 重新归一化兼容的剩余期限概率
            if total_compatible_pct > 0:
                for bucket in compatible_remaining_terms:
                    compatible_remaining_terms[bucket] /= total_compatible_pct
            
            # 计算每个兼容剩余期限区间的初始配额
            for rt_bucket, normalized_pct in compatible_remaining_terms.items():
                # 简单配额计算，不应用任何校正因子
                quota = int(CONST_NUM_OF_LOANS * sub_category_pct * loan_term_pct * normalized_pct)
                if quota > 0:
                    remaining_term_quotas[rt_bucket] += quota
                    total_quota_allocated += quota
    
    # 计算初始配额下各剩余期限区间的实际分布比例
    initial_rt_distribution = {}
    if total_quota_allocated > 0:
        for bucket, quota in remaining_term_quotas.items():
            initial_rt_distribution[bucket] = quota / total_quota_allocated
    
    # 计算剩余期限区间的校正系数
    rt_correction_factors = {}
    print("\n剩余期限区间校正系数计算:")
    for bucket in remaining_term_buckets:
        target_pct = remaining_term_buckets[bucket]
        initial_pct = initial_rt_distribution.get(bucket, 0)
        
        if initial_pct > 0:
            # 校正系数 = 目标分布比例 / 初始分布比例
            correction = target_pct / initial_pct
            # 限制校正系数在合理范围内
            correction = max(0.3, min(3.0, correction))
        else:
            correction = 1.0  # 默认不变
            
        rt_correction_factors[bucket] = correction
        print(f"区间 {bucket}: 目标比例={target_pct:.4f}, 初始比例={initial_pct:.4f}, 校正系数={correction:.4f}")
    
    print("--- 剩余期限校正系数计算完成 ---\n")
    
    # 步骤1: 预先计算贷款配额
    loan_quotas = {}
    valid_combinations = 0
    
    # 遍历所有可能的组合 (不包括信用评级)
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            # 贷款期限必须是区间右值（离散固定值），例如3个月、6个月、12个月等
            loan_term = int(loan_term_bucket[1])  # 使用右边界，不生成区间内随机值
            
            # 寻找与当前贷款期限兼容的剩余期限
            compatible_remaining_terms = {}
            total_compatible_pct = 0
            
            if loan_term <= 3:  # 对于3个月及以下的贷款
                print(f"处理短期贷款期限: {loan_term}个月，放宽匹配条件")
                # 从剩余期限区间中找出左边界小于loan_term的所有区间
                for remaining_term_bucket, remaining_term_pct in adjusted_remaining_term_buckets.items():
                    if remaining_term_bucket[0] < loan_term:  # 只要左边界小于贷款期限
                        compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                        total_compatible_pct += remaining_term_pct
                        print(f"  短期贷款特例: 匹配剩余期限区间 {remaining_term_bucket}, 权重={remaining_term_pct:.4f}")
            else:
                # 原有逻辑：只考虑右边界小于等于贷款期限的剩余期限
                for remaining_term_bucket, remaining_term_pct in adjusted_remaining_term_buckets.items():
                    # 确保RemainingTerm <= LoanTerm
                    if remaining_term_bucket[1] <= loan_term:
                        compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                        total_compatible_pct += remaining_term_pct
            
            # 如果没有兼容的RemainingTerm，跳过此组合
            if not compatible_remaining_terms:
                continue
            
            # 重新归一化兼容的RemainingTerm概率
            if total_compatible_pct > 0:
                for bucket in compatible_remaining_terms:
                    compatible_remaining_terms[bucket] /= total_compatible_pct
            
            # 为每个兼容的RemainingTerm计算配额 (不包括信用评级维度)
            for remaining_term_bucket, normalized_pct in compatible_remaining_terms.items():
                # 使用校正系数而非金额系数
                rt_factor = rt_correction_factors[remaining_term_bucket]
                
                # 使用校正系数进行正向调整：校正系数越大，配额越多；校正系数越小，配额越少
                adjusted_pct = normalized_pct * rt_factor
                
                # 计算此组合的贷款配额
                # 计算此组合应该有多少贷款
                loan_quota = int(CONST_NUM_OF_LOANS * sub_category_pct * loan_term_pct * adjusted_pct)
                
                if loan_quota > 0:
                    # 配额上取整，确保每个组合至少有一个贷款
                    loan_quota = max(1, loan_quota)
                    loan_quotas[(asset_sub_category, loan_term, remaining_term_bucket)] = loan_quota
                    valid_combinations += 1
    
    print(f"计算得到{valid_combinations}个有效组合，总配额: {sum(loan_quotas.values())}")
    
    # 调整配额以匹配总数
    total_quota = sum(loan_quotas.values())
    if total_quota != CONST_NUM_OF_LOANS:
        scale_factor = CONST_NUM_OF_LOANS / total_quota
        for key in loan_quotas:
            loan_quotas[key] = max(1, int(loan_quotas[key] * scale_factor))
            
        # 微调以确保总数正好等于total_loans
        total_quota = sum(loan_quotas.values())
        if total_quota != CONST_NUM_OF_LOANS:
            diff = CONST_NUM_OF_LOANS - total_quota
            # 按配额从大到小排序，调整最大的几个组合的配额
            sorted_keys = sorted(loan_quotas.keys(), key=lambda k: loan_quotas[k], reverse=True)
            
            # 分配差额
            for i in range(abs(diff)):
                key = sorted_keys[i % len(sorted_keys)]
                loan_quotas[key] += 1 if diff > 0 else -1 if loan_quotas[key] > 1 else 0
    
    # 步骤2: 按配额生成贷款
    loans = []
    all_cashflows = []
    spent = 0
    target_amount = available_cash
    
    # 按剩余期限区间上限从大到小排序，优先处理长期剩余期限的贷款
    print("\n--- 按剩余期限从长到短的优先级排序配额 ---")
    sorted_combinations = sorted(
        loan_quotas.keys(), 
        key=lambda k: k[2][1] if isinstance(k[2], tuple) else 0, 
        reverse=True
    )
    
    for rt_bucket in sorted(set([combo[2] for combo in sorted_combinations]), key=lambda b: b[1], reverse=True):
        print(f"剩余期限区间 {rt_bucket} 优先级较高")
    
    # 按优先级顺序处理贷款生成
    for combination in sorted_combinations:
        quota = loan_quotas[combination]
        asset_sub_category, loan_term, remaining_term_bucket = combination
         
        # 生成剩余期限
        # 在剩余期限区间内进行随机抽样（左开右闭区间）
        # 剩余期限应该是区间内的随机值，而不是固定的右边界
        # 例如，对于区间(9.0, 10.0]，可以是9.1-10.0之间的任意值
        # 为避免浮点数问题，先转换为整数区间再随机选择
        left = int(remaining_term_bucket[0])  # 左边界（不含）
        right = int(remaining_term_bucket[1]) # 右边界（含）
        # 确保剩余期限不大于贷款期限
        right = min(right, loan_term)
        # 在区间内随机选择（左边界+1到右边界之间）
        remaining_term = random.randint(left + 1, right)
        
        # 生成quota个贷款
        for _ in range(quota):
            # 检查是否达到预算上限
            if spent >= target_amount:
                print(f"已达到预算上限 {target_amount:.2f}，停止生成贷款")
                break
            
            # 随机抽样一个信用评级值，基于权重分布
            credit_rating = random.choices(credit_rating_values, weights=credit_rating_weights)[0]
            
            # 获取该组合的利率，增加容错机制
            interest_rate = None
            group_id = None
            
            # 计算已经过期限(seasoning)
            seasoning = loan_term - remaining_term
            
            try:
                # START OF ALERT: 不可修改的代码块
                # 至此，已确定4个维度的取值
                # 根据 Analysis_AssetGroups 表中的分组利率，确定此笔资产的利率
                # dimension1 = AssetSubCategory, dimension2 = CreditRating, dimension3 = LoanTerm, dimension4 = Seasoning
                group_id, interest_rate = match_asset_group(
                    df_asset_groups_interest_rates,
                    grouping_configs,
                    asset_category,
                    asset_sub_category,
                    loan_term,
                    seasoning,
                    credit_rating,
                    asset_group_cache
                )
                # END OF ALERT
            except Exception as e:
                print(f"警告: 匹配资产组失败: Category={asset_category}, "
                      f"SubCategory={asset_sub_category}, "
                      f"LoanTerm={loan_term}, "
                      f"RemainingTerm={remaining_term}, "
                      f"Seasoning={seasoning}, "
                      f"CreditRating={credit_rating}")
                print(f"错误详情: {str(e)}")
                continue
            
            if group_id is None or interest_rate is None:
                print(f"警告: 未找到匹配的利率，跳过贷款生成")
                continue
            
            # 使用统一贷款余额
            remaining_balance = fixed_loan_balance
            
            # 如果是最后一笔贷款，调整金额以精确匹配目标总额
            remaining_target = target_amount - spent
            if remaining_target < remaining_balance:
                remaining_balance = remaining_target
                        
            # START OF ALERT: 以下这部分代码请不要做任何改动
            loan_start_date = random_date(start_date, end_date)

            # 资产的放款日区间应为 pool_date - (seasoning+1)月 至 pool_date - seasoning月 之间
            issue_date_start = pool_date - relativedelta(months=seasoning + 1)
            issue_date_end = pool_date - relativedelta(months=seasoning)
            issue_date = random_date(issue_date_start, issue_date_end)

            seasoning_days = (pool_date - issue_date).days

            # loan_interest是上一兑付日（即资产起始日）到当前日期之间的利息
            # 注意返回的cf改为list，而不是DataFrame
            cf, loan_interest, remaining_term_days = generate_cashflows_for_single_loan(
                index,
                group_id,
                remaining_balance,
                remaining_term,
                interest_rate,
                loan_start_date,
                pool_date,
            )

            # 默认资产价格为其本金
            discount_factor = 1
            loan_value = remaining_balance

            if discount_method >= 1:
                # 若基于本金折价
                if discount_method == 1:
                    loan_value = remaining_balance / discount_factor
                # 若基于本息折价，但利息不折价
                elif discount_method == 2:
                    loan_value = remaining_balance / discount_factor + loan_interest
                # 若基于本息折价，且利息折价
                elif discount_method == 3:
                    loan_value = (
                        remaining_balance / discount_factor + loan_interest / discount_factor
                    )

            price = loan_value

            loans.append(
                {
                    "loan_id": index,
                    "remaining_balance": remaining_balance,
                    "asset_category": asset_category,
                    "asset_sub_category": asset_sub_category,
                    "loan_term": loan_term,
                    "seasoning": loan_term - remaining_term,
                    "seasoning_days": seasoning_days,
                    "remaining_term": remaining_term,
                    "remaining_term_days": remaining_term_days,
                    "credit_rating": credit_rating,
                    "interest_rate": interest_rate,
                    "wal": None,
                    "discount_factor": discount_factor,
                    "group_id": group_id,
                }
            )
            all_cashflows.extend(cf)
            
            # 更新计数器和已使用金额
            index += 1
            counter += 1

            # END OF ALERT: 以上代码请不要做任何改动

            spent += price
            
            # 每生成100个贷款，显示进度
            # if counter % 100 == 0:
            #     print(f"已生成 {counter} 个贷款，已使用金额 {spent:.2f} / {target_amount:.2f}")
            
            # 检查是否达到预算上限
            if spent >= target_amount:
                print(f"已达到预算上限 {target_amount:.2f}，停止生成贷款")
                break
    
    # 合并现金流DataFrame
    combined_cashflows_df = pd.DataFrame(all_cashflows)
    
    # 将贷款列表转换为DataFrame
    loans_df = pd.DataFrame(loans)
    
    # 打印生成结果总结
    avg_spent = spent / counter if counter > 0 else 0
    
    print(f"\n=== 生成资产总结 ===")
    print(f"生成的贷款总数: {counter}")
    print(f"总额: {spent:.2f} / {target_amount:.2f} ({100 * spent / target_amount:.2f}%)")
    print(f"平均贷款余额: {avg_spent:.2f}")
    
    # 返回生成的贷款和现金流DataFrame
    return loans_df, combined_cashflows_df, counter

# 分组校正：在每个贷款期限组内分别应用剩余期限校正，确保总体贷款期限比例不变
def generate_assets_from_distribution_stratified_adjusted_v2(
    asset_category,
    asset_subcategory_buckets,
    loan_term_buckets,
    remaining_term_buckets,
    credit_rating_buckets,
    available_cash,
    pool_date,
    average_loan_amount,
    df_asset_groups_interest_rates,
    grouping_configs,
    discount_method,
    asset_group_cache,
    num_of_pre_existing_loans = 0
):
    """
    使用固定4000笔资产
    
    Args:
        asset_category: 资产类别
        asset_subcategory_buckets: 子类别分布
        loan_term_buckets: 贷款期限分布
        remaining_term_buckets: 剩余期限分布
        credit_rating_buckets: 信用评级分布
        available_cash: 可用资金
        pool_date: 资产池日期
        average_loan_amount: 平均贷款余额
        df_asset_groups_interest_rates: 资产组利率数据
        grouping_configs: 分组配置
        discount_method: 折现方法
        asset_group_cache: 资产组缓存
        num_of_pre_existing_loans: 调用此方法时, 该asset_category已生成的贷款数量，新的loan_id从这里开始
        
    Returns:
        (loans_df, cashflows_df): 贷款DataFrame和现金流DataFrame
    """
    # 输出函数名
    print(f"\n=== {generate_assets_from_distribution_stratified_adjusted_v2.__name__} ===")
    CONST_NUM_OF_LOANS = 4000
    print(f"使用固定{CONST_NUM_OF_LOANS}笔资产")

    # 参数检查和边界处理
    if not isinstance(pool_date, datetime):
        raise TypeError("pool_date必须是datetime类型")
        
    if not isinstance(asset_subcategory_buckets, dict) or not asset_subcategory_buckets:
        raise ValueError("asset_subcategory_buckets必须是非空字典")
        
    if not isinstance(loan_term_buckets, dict) or not loan_term_buckets:
        raise ValueError("loan_term_buckets必须是非空字典")
        
    if not isinstance(remaining_term_buckets, dict) or not remaining_term_buckets:
        raise ValueError("remaining_term_buckets必须是非空字典")
        
    if not isinstance(credit_rating_buckets, dict) or not credit_rating_buckets:
        raise ValueError("credit_rating_buckets必须是非空字典")
        
    if available_cash <= 0:
        raise ValueError(f"available_cash必须大于0，当前值: {available_cash}")
        
    if average_loan_amount <= 0:
        raise ValueError(f"average_loan_amount必须大于0，当前值: {average_loan_amount}")
        
    if asset_category not in grouping_configs:
        raise ValueError(f"未找到资产类别 {asset_category} 的分组配置")
    
    # 预计算配额调整 - 先调整剩余期限分布权重
    adjusted_remaining_term_buckets = remaining_term_buckets
    
    # 生成资产的剩余本金为指定的值
    fixed_loan_balance = available_cash / CONST_NUM_OF_LOANS

    # 打印输入参数以便于分析
    print(f"\n=== 开始分层抽样生成资产（使用固定数量和金额）===")
    print(f"资产类别: {asset_category}")
    print(f"可用资金: {available_cash:.2f}")
    print(f"贷款数量: {CONST_NUM_OF_LOANS}")
    print(f"固定贷款余额: {fixed_loan_balance:.2f}")
    print(f"资产池日期: {pool_date}")
    print(f"折现方法: {discount_method}")

    # 令资产“上一起息日”在过去一个月内均匀分布，以产生连续均匀的按日归集现金流
    # 但注意资产的起始日应基于seasoning确定
    start_date = pool_date - relativedelta(months=1)
    end_date = pool_date - relativedelta(days=1)
    
    # 计算贷款ID起始值
    loan_id_start = asset_category * LOAN_ID_SEGMENT + num_of_pre_existing_loans
    index = loan_id_start
    counter = 0

    # 检查分布权重之和是否接近1
    for name, buckets in [
        ("资产子类别", asset_subcategory_buckets),
        ("贷款期限", loan_term_buckets),
        ("剩余期限", remaining_term_buckets), 
        ("信用评级", credit_rating_buckets)
    ]:
        weight_sum = sum(buckets.values())
        if not 0.99 <= weight_sum <= 1.01:  # 允许1%的误差
            print(f"警告: {name}分布的权重之和为{weight_sum:.4f}，期望值为1.0")
    
    print(f"\n--- 资产子类别分布 ---")
    for bucket, pct in sorted(asset_subcategory_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 贷款期限分布 ---")  
    for bucket, pct in sorted(loan_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 剩余期限分布 ---")
    for bucket, pct in sorted(adjusted_remaining_term_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
        
    print(f"\n--- 信用评级分布 ---")
    for bucket, pct in sorted(credit_rating_buckets.items()):
        print(f"区间 {bucket}: {pct:.4f}")
    
    # 预处理信用评级值和权重，用于后续随机抽样
    credit_rating_values = []
    credit_rating_weights = []
    
    for credit_rating_bucket, weight in credit_rating_buckets.items():
        left, right = credit_rating_bucket
        # 使用区间的右边界值作为信用评级值
        credit_rating_values.append(right)
        credit_rating_weights.append(weight)
    
    print(f"\n--- 信用评级抽样准备 ---")
    print(f"评级值: {credit_rating_values}")
    print(f"权重: {credit_rating_weights}")
    
    # 提取所有剩余期限区间并排序
    all_remaining_term_buckets = sorted(set(remaining_term_buckets.keys()), key=lambda b: b[1])
    
    # 第0步: 自适应计算剩余期限区间的校正系数
    print("\n--- 开始计算剩余期限校正系数 ---")
    
    # 进行初始配额模拟计算，不实际生成贷款
    remaining_term_quotas = {bucket: 0 for bucket in remaining_term_buckets.keys()}
    total_quota_allocated = 0
    
    # 模拟计算每个维度组合的初始配额
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            loan_term = int(loan_term_bucket[1])  # 使用右边界
            
            # 找出与当前贷款期限兼容的剩余期限
            compatible_remaining_terms = {}
            total_compatible_pct = 0
            
            if loan_term <= 3:  # 特殊处理短期贷款
                for rt_bucket, rt_pct in remaining_term_buckets.items():
                    if rt_bucket[0] < loan_term:  # 放宽匹配条件
                        compatible_remaining_terms[rt_bucket] = rt_pct
                        total_compatible_pct += rt_pct
            else:
                # 标准匹配：剩余期限 <= 贷款期限
                for rt_bucket, rt_pct in remaining_term_buckets.items():
                    if rt_bucket[1] <= loan_term:
                        compatible_remaining_terms[rt_bucket] = rt_pct
                        total_compatible_pct += rt_pct
            
            # 若无兼容剩余期限，跳过此组合
            if not compatible_remaining_terms:
                continue
            
            # 重新归一化兼容的剩余期限概率
            if total_compatible_pct > 0:
                for bucket in compatible_remaining_terms:
                    compatible_remaining_terms[bucket] /= total_compatible_pct
            
            # 计算每个兼容剩余期限区间的初始配额
            for rt_bucket, normalized_pct in compatible_remaining_terms.items():
                # 简单配额计算，不应用任何校正因子
                quota = int(CONST_NUM_OF_LOANS * sub_category_pct * loan_term_pct * normalized_pct)
                if quota > 0:
                    remaining_term_quotas[rt_bucket] += quota
                    total_quota_allocated += quota
    
    # 计算初始配额下各剩余期限区间的实际分布比例
    initial_rt_distribution = {}
    if total_quota_allocated > 0:
        for bucket, quota in remaining_term_quotas.items():
            initial_rt_distribution[bucket] = quota / total_quota_allocated
    
    # 计算剩余期限区间的校正系数
    rt_correction_factors = {}
    print("\n剩余期限区间校正系数计算:")
    for bucket in remaining_term_buckets:
        target_pct = remaining_term_buckets[bucket]
        initial_pct = initial_rt_distribution.get(bucket, 0)
        
        if initial_pct > 0:
            # 校正系数 = 目标分布比例 / 初始分布比例
            correction = target_pct / initial_pct
            # 限制校正系数在合理范围内
            correction = max(0.3, min(3.0, correction))
        else:
            correction = 1.0  # 默认不变
            
        rt_correction_factors[bucket] = correction
        print(f"区间 {bucket}: 目标比例={target_pct:.4f}, 初始比例={initial_pct:.4f}, 校正系数={correction:.4f}")
    
    print("--- 剩余期限校正系数计算完成 ---\n")
    
    # 步骤1: 预先计算贷款配额
    loan_quotas = {}
    valid_combinations = 0
    
    # 遍历所有可能的组合 (不包括信用评级)
    for sub_category_bucket, sub_category_pct in asset_subcategory_buckets.items():
        asset_sub_category = int(sub_category_bucket[1])  # 使用右边界
        
        for loan_term_bucket, loan_term_pct in loan_term_buckets.items():
            # 贷款期限必须是区间右值（离散固定值），例如3个月、6个月、12个月等
            loan_term = int(loan_term_bucket[1])  # 使用右边界，不生成区间内随机值
            
            # 寻找与当前贷款期限兼容的剩余期限
            compatible_remaining_terms = {}
            total_compatible_pct = 0
            
            if loan_term <= 3:  # 对于3个月及以下的贷款
                print(f"处理短期贷款期限: {loan_term}个月，放宽匹配条件")
                # 从剩余期限区间中找出左边界小于loan_term的所有区间
                for remaining_term_bucket, remaining_term_pct in adjusted_remaining_term_buckets.items():
                    if remaining_term_bucket[0] < loan_term:  # 只要左边界小于贷款期限
                        compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                        total_compatible_pct += remaining_term_pct
                        print(f"  短期贷款特例: 匹配剩余期限区间 {remaining_term_bucket}, 权重={remaining_term_pct:.4f}")
            else:
                # 原有逻辑：只考虑右边界小于等于贷款期限的剩余期限
                for remaining_term_bucket, remaining_term_pct in adjusted_remaining_term_buckets.items():
                    # 确保RemainingTerm <= LoanTerm
                    if remaining_term_bucket[1] <= loan_term:
                        compatible_remaining_terms[remaining_term_bucket] = remaining_term_pct
                        total_compatible_pct += remaining_term_pct
            
            # 如果没有兼容的RemainingTerm，跳过此组合
            if not compatible_remaining_terms:
                continue
            
            # 重新归一化兼容的RemainingTerm概率
            if total_compatible_pct > 0:
                for bucket in compatible_remaining_terms:
                    compatible_remaining_terms[bucket] /= total_compatible_pct
            
            # 为每个兼容的RemainingTerm计算配额 (不包括信用评级维度)
            for remaining_term_bucket, normalized_pct in compatible_remaining_terms.items():
                # 使用校正系数而非金额系数
                rt_factor = rt_correction_factors[remaining_term_bucket]
                
                # 使用校正系数进行正向调整：校正系数越大，配额越多；校正系数越小，配额越少
                adjusted_pct = normalized_pct * rt_factor
                
                # 计算此组合的贷款配额
                # 计算此组合应该有多少贷款
                loan_quota = int(CONST_NUM_OF_LOANS * sub_category_pct * loan_term_pct * adjusted_pct)
                
                if loan_quota > 0:
                    # 配额上取整，确保每个组合至少有一个贷款
                    loan_quota = max(1, loan_quota)
                    loan_quotas[(asset_sub_category, loan_term, remaining_term_bucket)] = loan_quota
                    valid_combinations += 1
    
    print(f"计算得到{valid_combinations}个有效组合，总配额: {sum(loan_quotas.values())}")
    
    # 调整配额以匹配总数
    total_quota = sum(loan_quotas.values())
    if total_quota != CONST_NUM_OF_LOANS:
        # 先调整总体贷款数量
        scale_factor = CONST_NUM_OF_LOANS / total_quota if total_quota > 0 else 1
        
        # 按贷款期限分组调整，确保每个贷款期限组内的贷款比例符合目标
        adjusted_loan_quotas = {}
        for loan_term_bucket, target_quota in total_loan_term_quotas.items():
            # 找出所有该贷款期限的组合
            term_combinations = [k for k in loan_quotas.keys() if k[1] == int(loan_term_bucket[1])]
            # 计算当前该期限的总配额
            current_term_quota = sum(loan_quotas[k] for k in term_combinations)
            
            if current_term_quota > 0:
                # 计算该贷款期限内部的调整因子
                term_scale_factor = target_quota / current_term_quota
                
                # 应用调整
                for key in term_combinations:
                    adjusted_quota = max(1, int(loan_quotas[key] * term_scale_factor))
                    adjusted_loan_quotas[key] = adjusted_quota
            else:
                # 如果当前该期限没有配额，则跳过
                for key in term_combinations:
                    adjusted_loan_quotas[key] = loan_quotas[key]
        
        # 更新配额
        loan_quotas = adjusted_loan_quotas
            
        # 微调以确保总数正好等于 CONST_NUM_OF_LOANS
        total_quota = sum(loan_quotas.values())
        if total_quota != CONST_NUM_OF_LOANS:
            diff = CONST_NUM_OF_LOANS - total_quota
            # 按贷款期限分组进行调整，保持各贷款期限的比例
            for loan_term_bucket in sorted(total_loan_term_quotas.keys(), key=lambda b: total_loan_term_quotas[b], reverse=True):
                if diff == 0:
                    break
                    
                # 找出所有该贷款期限的组合
                term_combinations = [k for k in loan_quotas.keys() if k[1] == int(loan_term_bucket[1])]
                if not term_combinations:
                    continue
                    
                # 按配额从大到小排序
                term_combinations = sorted(term_combinations, key=lambda k: loan_quotas[k], reverse=True)
            
            # 分配差额
                for key in term_combinations:
                    if diff > 0:
                        loan_quotas[key] += 1
                        diff -= 1
                    elif diff < 0 and loan_quotas[key] > 1:
                        loan_quotas[key] -= 1
                        diff += 1
                        
                    if diff == 0:
                        break
    
    # 最终检查各贷款期限组的总配额
    final_term_quotas = {}
    for loan_term_bucket in loan_term_buckets.keys():
        term_val = int(loan_term_bucket[1])
        term_combinations = [k for k in loan_quotas.keys() if k[1] == term_val]
        final_term_quotas[loan_term_bucket] = sum(loan_quotas[k] for k in term_combinations)
    
    print("\n--- 最终贷款期限配额 ---")
    for lt_bucket, quota in final_term_quotas.items():
        target_quota = total_loan_term_quotas[lt_bucket]
        print(f"贷款期限区间 {lt_bucket}: 配额 {quota} ({quota/CONST_NUM_OF_LOANS:.4f}), 目标: {target_quota} ({target_quota/CONST_NUM_OF_LOANS:.4f})")
    
    # 步骤2: 按配额生成贷款
    loans = []
    cashflow_dfs = []
    spent = 0
    target_amount = available_cash
    
    # 按剩余期限区间上限从大到小排序，优先处理长期剩余期限的贷款
    print("\n--- 按剩余期限从长到短的优先级排序配额 ---")
    sorted_combinations = sorted(
        loan_quotas.keys(), 
        key=lambda k: k[2][1] if isinstance(k[2], tuple) else 0, 
        reverse=True
    )
    
    for rt_bucket in sorted(set([combo[2] for combo in sorted_combinations]), key=lambda b: b[1], reverse=True):
        print(f"剩余期限区间 {rt_bucket} 优先级较高")
    
    # 按优先级顺序处理贷款生成
    for combination in sorted_combinations:
        quota = loan_quotas[combination]
        asset_sub_category, loan_term, remaining_term_bucket = combination
         
        # 生成剩余期限
        # 在剩余期限区间内进行随机抽样（左开右闭区间）
        # 剩余期限应该是区间内的随机值，而不是固定的右边界
        # 例如，对于区间(9.0, 10.0]，可以是9.1-10.0之间的任意值
        # 为避免浮点数问题，先转换为整数区间再随机选择
        left = int(remaining_term_bucket[0])  # 左边界（不含）
        right = int(remaining_term_bucket[1]) # 右边界（含）
        # 确保剩余期限不大于贷款期限
        right = min(right, loan_term)
        # 在区间内随机选择（左边界+1到右边界之间）
        remaining_term = random.randint(left + 1, right)
        
        # 生成quota个贷款
        for _ in range(quota):
            # 检查是否达到预算上限
            if spent >= target_amount:
                print(f"已达到预算上限 {target_amount:.2f}，停止生成贷款")
                break
            
            # 随机抽样一个信用评级值，基于权重分布
            credit_rating = random.choices(credit_rating_values, weights=credit_rating_weights)[0]
            
            # 获取该组合的利率，增加容错机制
            interest_rate = None
            group_id = None
            
            # 计算已经过期限(seasoning)
            seasoning = loan_term - remaining_term
            
            try:
                # START OF ALERT: 不可修改的代码块
                # 至此，已确定4个维度的取值
                # 根据 Analysis_AssetGroups 表中的分组利率，确定此笔资产的利率
                # dimension1 = AssetSubCategory, dimension2 = CreditRating, dimension3 = LoanTerm, dimension4 = Seasoning
                #time_x = datetime.now()
                group_id, interest_rate = match_asset_group(
                    df_asset_groups_interest_rates,
                    grouping_configs,
                    asset_category,
                    asset_sub_category,
                    loan_term,
                    seasoning,
                    credit_rating,
                    asset_group_cache
                )
                # END OF ALERT
            except Exception as e:
                print(f"警告: 匹配资产组失败: Category={asset_category}, "
                      f"SubCategory={asset_sub_category}, "
                      f"LoanTerm={loan_term}, "
                      f"RemainingTerm={remaining_term}, "
                      f"Seasoning={seasoning}, "
                      f"CreditRating={credit_rating}")
                print(f"错误详情: {str(e)}")
                continue
            
            if group_id is None or interest_rate is None:
                print(f"警告: 未找到匹配的利率，跳过贷款生成")
                continue
            
            # 使用统一贷款余额
            remaining_balance = fixed_loan_balance
            
            # 如果是最后一笔贷款，调整金额以精确匹配目标总额
            remaining_target = target_amount - spent
            if remaining_target < remaining_balance:
                remaining_balance = remaining_target
                        
            # START OF ALERT: 以下这部分代码请不要做任何改动
            loan_start_date = random_date(start_date, end_date)

            # 资产的放款日区间应为 pool_date - (seasoning+1)月 至 pool_date - seasoning月 之间
            issue_date_start = pool_date - relativedelta(months=seasoning + 1)
            issue_date_end = pool_date - relativedelta(months=seasoning)
            issue_date = random_date(issue_date_start, issue_date_end)

            seasoning_days = (pool_date - issue_date).days

            # loan_interest是上一兑付日（即资产起始日）到当前日期之间的利息
            cf, loan_interest, remaining_term_days = generate_cashflows_for_single_loan(
                index,
                group_id,
                remaining_balance,
                remaining_term,
                interest_rate,
                loan_start_date,
                pool_date,
            )

            # 默认资产价格为其本金
            discount_factor = 1
            loan_value = remaining_balance

            if discount_method >= 1:
                # 若基于本金折价
                if discount_method == 1:
                    loan_value = remaining_balance / discount_factor
                # 若基于本息折价，但利息不折价
                elif discount_method == 2:
                    loan_value = remaining_balance / discount_factor + loan_interest
                # 若基于本息折价，且利息折价
                elif discount_method == 3:
                    loan_value = (
                        remaining_balance / discount_factor + loan_interest / discount_factor
                    )

            price = loan_value

            loans.append(
                {
                    "loan_id": index,
                    "remaining_balance": remaining_balance,
                    "asset_category": asset_category,
                    "asset_sub_category": asset_sub_category,
                    "loan_term": loan_term,
                    "seasoning": loan_term - remaining_term,
                    "seasoning_days": seasoning_days,
                    "remaining_term": remaining_term,
                    "remaining_term_days": remaining_term_days,
                    "credit_rating": credit_rating,
                    "interest_rate": interest_rate,
                    "wal": None,
                    "discount_factor": discount_factor,
                    "group_id": group_id,
                }
            )

            cashflow_dfs.append(cf)
            
            # 更新计数器和已使用金额
            index += 1
            counter += 1

            # END OF ALERT: 以上代码请不要做任何改动

            spent += price
            
            # 每生成100个贷款，显示进度
            # if counter % 100 == 0:
            #     print(f"已生成 {counter} 个贷款，已使用金额 {spent:.2f} / {target_amount:.2f}")
            
            # 检查是否达到预算上限
            if spent >= target_amount:
                print(f"已达到预算上限 {target_amount:.2f}，停止生成贷款")
                break
    
    # 合并现金流DataFrame
    combined_cashflows_df = pd.concat(cashflow_dfs) if cashflow_dfs else pd.DataFrame()
    
    # 将贷款列表转换为DataFrame
    loans_df = pd.DataFrame(loans)
    
    # 打印生成结果总结
    avg_spent = spent / counter if counter > 0 else 0
    
    print(f"\n=== 生成资产总结 ===")
    print(f"生成的贷款总数: {counter}")
    print(f"总额: {spent:.2f} / {target_amount:.2f} ({100 * spent / target_amount:.2f}%)")
    print(f"平均贷款余额: {avg_spent:.2f}")
    
    # 返回生成的贷款和现金流DataFrame
    return loans_df, combined_cashflows_df, counter    