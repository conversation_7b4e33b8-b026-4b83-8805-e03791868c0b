<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.goldenstand.services.mapper.TrustMapper">
    <select id="getSolutionTrustInfo" resultType="java.util.Map">
        select t.*, tc.CautionType
        from TrustManagement_Trust t
                 left join Analysis_TrustCaution tc on t.TrustId = tc.TrustId
        where t.TrustId = #{trustId}
    </select>
    <select id="getCashflowTaskByTrustId" resultType="java.util.Map">
        select TrustCode
             , concat(TrustCode, '_CashFlow')         as CashflowTaskCode
             , concat(TrustCode, '_CashFlow_STBatch') as StressTestTaskCode
             , TrustCode                              as WaterfallTaskCode
        from TrustManagement_Trust
        where TrustId = #{trustId}
    </select>

    <select id="getTrustIdByTrustCode" parameterType="java.util.Set" resultType="java.util.Map">
        select TrustId,TrustCode from Analysis_Trust
        <where>
            TrustCode in
            <foreach item="item" collection="list" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
        </where>
    </select>

    <select id="findOne" resultType="java.util.Map">
        select * from Analysis_Trust limit 1
    </select>

    <select id="getTrustIdAndName" resultType="java.util.Map">
        select TrustId, TrustName from TrustManagement_Trust where TrustId != 0
    </select>

    <select id="getTrustByProductType" resultType="java.util.Map">
        select t.TrustId, t.TrustCode, t.TrustName 
        from TrustManagement_Trust as t
        inner join Analysis_Trust as a on t.TrustId = a.TrustId
        inner join TrustManagement_TrustInfoExtension as te on t.TrustId = te.TrustId and te.ItemCode = 'ProductNo'
        <where>
            t.TrustId != 0 and t.IsMarketProduct = 1
            <if test="productType != null and productType != ''">
                <choose>
                    <when test="productType == 'yuefu'">
                        and te.ItemValue = '00010'
                    </when>
                    <when test="productType == 'weidai'">
                        and te.ItemValue != '00010'
                    </when>
                </choose>
            </if>
        </where>
    </select>

    <select id="getTrustDistInfo" resultType="cn.goldenstand.services.entity.drb.TrustDistInfoDTO">
        select t.TrustId as trustId, t.TrustCode as trustCode, t.TrustName as trustName, t2.ItemValue as poolCloseDate,
        	replace(replace(replace(replace(replace(t1.ItemValue, '00010', '月付'), '00001', '生活费小额')
                , '00007', '生意贷小额'), '00017', '生意贷中额'), '00027', '生活费中额') as productType,
            case when t3.ItemValue = 'true' then '循环' else '静态' end as structure,
            case t.TrustCalculationStatus 
            	when '1' then '发行前'
                when '2' then '循环期'
                when '3' then '摊还期'
                when '4' then '已结束'
                else '' 
            end as status
        from Analysis_Trust as t
        left join TrustManagement_TrustInfoExtension as t1 on t.TrustId = t1.TrustId
        	and t1.ItemCode = 'ProductNo'
    	left join TrustManagement_TrustExtension as t2 on t.TrustId = t2.TrustId
    		and t2.ItemCode = 'PoolCloseDate'
    	left join TrustManagement_TrustExtension as t3 on t.TrustId = t3.TrustId
    		and t3.ItemCode = 'IsTopUpAvailable'
        where t.TrustId = #{trustId}
    </select>

    <select id="getTrustBondIDAndName" resultType="java.util.Map">
        select TrustBondId, ItemValue as BondName from TrustManagement_TrustBond where TrustId = #{trustId} and ItemCode = 'ShortName' order by TrustBondId
    </select>

    <select id="findTrustIdByTrustName" resultType="java.util.Map">
        select TrustId as trust_id from TrustManagement_Trust where TrustName=#{trustName}
    </select>
</mapper>
