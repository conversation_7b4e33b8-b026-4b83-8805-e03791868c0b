import importlib
import logging
import os
import sys
import traceback
import urllib.parse

from PythonFiles import SQLParamsParser

current_dir = os.path.dirname(os.path.abspath(__file__))
package_path = os.path.join(current_dir, '..', 'Automation', 'meituan')
sys.path.append(package_path)
fin = importlib.import_module('fin_dashboard_trust_metric')


def main(params):
    # 临时禁用日志输出
    original_log_level = None
    if hasattr(fin, 'log') and fin.log:
        original_log_level = fin.log.level
        fin.log.setLevel(logging.CRITICAL)  # 设置为最高级别，只有严重错误才会输出

    try:
        # 调用原始处理逻辑
        fin.main(report_date=params['reporting_date'], trust_id=params['trust_id'])
    finally:
        # 恢复原始日志级别
        if hasattr(fin, 'log') and fin.log and original_log_level is not None:
            fin.log.setLevel(original_log_level)

if __name__ == '__main__':
    try:
        str_arg = ''''''
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]
        else:
            raise Exception("参数错误")

        arg = urllib.parse.unquote_plus(str_arg)
        params = SQLParamsParser.getParmas(arg)
        main(params)
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())