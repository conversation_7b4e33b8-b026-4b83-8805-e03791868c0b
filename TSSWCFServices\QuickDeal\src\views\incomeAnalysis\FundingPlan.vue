<template>
    <div>
        <base-box title="融资端和资产端">
            <h3>
                <el-button type="text" class="fr" icon="el-icon-circle-plus" @click="addData('tableForm1')"></el-button>
                融资端
            </h3>
            <el-table :data="tableForm1">
                <el-table-column label="操作" width="80px">
                    <template v-slot="{ row }">
                        <el-button type="text" icon="el-icon-delete" @click="deleteData('tableForm1', row)"></el-button>
                    </template>
                </el-table-column>
                <el-table-column label="融资渠道名称">
                    <template v-slot="{ row }">
                        <el-input v-model="row.SourceName"  :class="{ 'fp-red-border': isSubmitting && isEmpty(row.SourceName) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="资金成本（年化%）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.FundingCost"  :class="{ 'fp-red-border': isSubmitting && isEmpty(row.FundingCost) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="融资金额上限（万元）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.UpperLimit"  :class="{ 'fp-red-border': isSubmitting && isEmpty(row.UpperLimit) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="期限（月）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.Term"  :class="{ 'fp-red-border': isSubmitting && isEmpty(row.Term) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="税费成本（年化%）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.Fees"  :class="{ 'fp-red-border': isSubmitting && isEmpty(row.Fees) }"></el-input>
                    </template>
                </el-table-column>
            </el-table>
            <h3>
                <el-button type="text" class="fr" icon="el-icon-circle-plus" @click="addData('tableForm2')"></el-button>
                资产端
            </h3>
            <el-table :data="tableForm2">
                <el-table-column label="操作" width="80px">
                    <template v-slot="{ row }">
                        <el-button type="text" icon="el-icon-delete" @click="deleteData('tableForm2', row)"></el-button>
                    </template>
                </el-table-column>
                <el-table-column label="资产名称">
                    <template v-slot="{ row }">
                        <el-input v-model="row.AssetName"
                            :class="{ 'fp-red-border': isSubmitting && isEmpty(row.AssetName) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="件均（万元）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.AvgAmount" :class="{ 'fp-red-border': isSubmitting && isEmpty(row.AvgAmount) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="平均利率（年化%）" min-width="110px">
                    <template v-slot="{ row }">
                        <el-input v-model="row.InterestRate" :class="{ 'fp-red-border': isSubmitting && isEmpty(row.InterestRate) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="流量上限（笔）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.AssetCount" :class="{ 'fp-red-border': isSubmitting && isEmpty(row.AssetCount) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="资产期限">
                    <template v-slot="{ row }">
                        <el-input v-model="row.LoanTerm" :class="{ 'fp-red-border': isSubmitting && isEmpty(row.LoanTerm) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="损失率（年化%）">
                    <template v-slot="{ row }">
                        <el-input v-model="row.Loss" :class="{ 'fp-red-border': isSubmitting && isEmpty(row.Loss) }"></el-input>
                    </template>
                </el-table-column>
                <el-table-column label="资产总额上限（万元）" prop="TotalAmount" :formatter="formatMoney"
                    min-width="110px"></el-table-column>
            </el-table>
            <div class="mt-10 text-center">
                <el-button type="primary" @click="save">保存</el-button>
                <el-button @click="calcResult">测算</el-button>
            </div>
        </base-box>

        <base-box title="明细">
            <template v-slot:header>
                <label for="">排序条件</label>
                <el-select v-model="orderby" class="ml-10" @change="getResult">
                    <el-option label="融资总额" value="TotalFundingAmount"></el-option>
                    <el-option label="融资成本" value="FundingCost"></el-option>
                    <el-option label="净利润" value="NetProfit"></el-option>
                    <el-option label="利润率" value="ProfitMargin"></el-option>
                </el-select>
            </template>
            <el-table :data="table" :height="480">
                <el-table-column label="方案序号" prop="PlanID" min-width="110px">
                    <template v-slot="{ row }">
                        <div>{{ row.PlanID }} <el-button icon="el-icon-data-line" type="text"
                                @click="openCashflow(row)"></el-button></div>
                    </template>
                </el-table-column>
                <el-table-column label="融资总额(亿元)" prop="TotalFundingAmount" :formatter="formatMoney"
                    min-width="140px"></el-table-column>
                <el-table-column label="融资成本(万元)" prop="FundingCost" :formatter="formatMoney"
                    min-width="140px"></el-table-column>
                <el-table-column label="净利润(万元)" prop="NetProfit" :formatter="formatMoney"
                    min-width="140px"></el-table-column>
                <el-table-column label="利润率(%)" prop="ProfitMargin" min-width="140px"></el-table-column>
                <el-table-column label="融资明细" v-if="tableFundingDetails.length" class-name="fp-mx-bg">
                    <template v-for="(item, index) in tableFundingDetails">
                        <el-table-column :label="'渠道名称'" min-width="140px" class-name="fp-mx-bg">
                            <template v-slot="{ row }">
                                <div>{{ row.FundingDetails[index].SourceName }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column :label="'金额'" min-width="140px" class-name="fp-mx-bg">
                            <template v-slot="{ row }">
                                <div>{{ formatMoney(0, 0, row.FundingDetails[index].Amount) }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column :label="'成本'" min-width="140px" class-name="fp-mx-bg">
                            <template v-slot="{ row }">
                                <div>{{ formatMoney(0, 0, row.FundingDetails[index].Cost) }}</div>
                            </template>
                        </el-table-column>
                    </template>
                </el-table-column>
                <el-table-column label="资产总额" prop="TotalAssetAmount" min-width="140px"
                    :formatter="formatMoney"></el-table-column>
                <el-table-column label="资产明细" class-name="fp-mx-bg2">
                    <template v-for="(item, index) in tableAssetDetail">
                        <el-table-column :label="'资产名称'" min-width="140px" class-name="fp-mx-bg2">
                            <template v-slot="{ row }">
                                <div>{{ row.AssetDetails[index].AssetName }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column :label="'金额'" min-width="140px" class-name="fp-mx-bg2">
                            <template v-slot="{ row }">
                                <div>{{ formatMoney(0, 0, row.AssetDetails[index].Amount) }}</div>
                            </template>
                        </el-table-column>
                    </template>
                </el-table-column>
            </el-table>

            <el-dialog title="现金流弹窗" :visible.sync="showCashflow">
                <el-table :data="cashflowData">
                    <el-table-column label="天数" prop="Days" min-width="70px"></el-table-column>
                    <el-table-column label="本金（元）" prop="Principal" min-width="110px"
                        :formatter="formatMoney"></el-table-column>
                    <el-table-column label="利息（元）" prop="Interest" min-width="110px"
                        :formatter="formatMoney"></el-table-column>
                    <el-table-column label="循环购买金额（元）" prop="RevolvingPurchase" min-width="110px"
                        :formatter="formatMoney"></el-table-column>
                </el-table>
            </el-dialog>
        </base-box>

    </div>
</template>

<script>
import BaseBox from '@/components/base/BaseBox.vue'
import Highcharts from '@/components/common/Highcharts.vue'
import util from '@/utils/';
import TaskProcess from '@/api/TaskProcess.js';
import dynamicTab from '@/mixins/dynamicTab.js';
export default {
    components: { BaseBox, Highcharts },
    mixins: [dynamicTab],
    data() {
        return {
            isSubmitting: false,
            table: [],
            tableAssetDetail: [],
            tableFundingDetails: [],
            tableForm1: [],
            tableForm2: [],
            TrustStatus: '',
            orderby: '',

            showCashflow: false,
            cashflowData: []
        }
    },
    mounted() {
        this.newTab({
            id: 'FundingPlan',
            name: 'FundingPlan',
            title: '融资规划报表'
        })
        this.getFundingPlan();
        this.getResult()
    },
    methods: {
        isEmpty(value) {
            if (typeof value === 'string') return !value.trim(); // 字符串去空格校验
            if (typeof value === 'number') return isNaN(value) || value === null || value === undefined; // 数字允许 0
            return value === null || value === undefined || value === ''; // 其他类型
        },
        openCashflow(row) {
            this.showCashflow = true;
            this.getCashflow(row)
        },
        validateForm() {
            let isValid = true;
            // 校验融资端
            this.tableForm1.forEach(item => {
                Object.keys(item).forEach(field => {
                    if (this.isEmpty(item[field])) {
                        isValid = false;
                    }
                });
            });
            // 校验资产端
            this.tableForm2.forEach(item => {
                Object.keys(item).forEach(field => {
                    if (this.isEmpty(item[field])) {
                        isValid = false;
                    }
                });
            });
            return isValid;
        },
        save() {
            this.isSubmitting = true;
            this.$nextTick(() => {
                const isValid = this.validateForm();
                if (!isValid) {
                    this.$message.error('请填写所有必填字段');
                    return;
                }
                let svc = this.$http('FixedIncome', 'Analysis_Trust_FundingPlan_Save');
                svc.AddParam({
                    Name: 'items', Value: JSON.stringify([
                        this.tableForm1,
                        this.tableForm2
                    ]), DBType: 'string'
                });
                svc.ExecTable().then(res => {
                    this.$message.success('保存成功')
                    this.isSubmitting = false;
                });
            });
        },
        getCashflow(row) {
            this.cashflowData = []
            /// Analysis_Trust_FundingPlan_Cashflows
            let svc = this.$http('FixedIncome', 'Analysis_Trust_FundingPlan_Cashflows');
            svc.AddParam({ Name: 'PlanID', Value: row.PlanID, DBType: 'string' });
            svc.ExecTable().then(res => {
                this.cashflowData = res;
            });
        },
        getFundingPlan() {
            let svc = this.$http('FixedIncome', 'Analysis_Trust_FundingPlan');
            svc.ExecTable().then(res => {
                this.tableForm1 = res[0]
                this.tableForm2 = res[1]
                //this.table = res;
            });
        },
        getResult() {
            let svc = this.$http('FixedIncome', 'Analysis_Trust_FundingPlan_Results');
            svc.AddParam({ Name: 'orderby', Value: this.orderby, DBType: 'string' });
            svc.ExecTable().then(res => {
                this.table = res.map(v => {
                    try {
                        v.FundingDetails = JSON.parse(v.FundingDetails)
                    } catch (e) {
                        v.FundingDetails = []
                    }

                    try {
                        v.AssetDetails = JSON.parse(v.AssetDetails)
                    } catch (e) {
                        v.AssetDetails = []
                    }
                    return v;
                });
                if (res.length) {
                    console.log(this.table)
                    this.tableAssetDetail = this.table[0].AssetDetails;
                    this.tableFundingDetails = this.table[0].FundingDetails;
                    console.log(this.tableFundingDetails)
                }
            });
        },
        deleteData(data, row) {
            const index = this[data].indexOf(row);
            this[data].splice(index, 1);
        },
        addData(data) {
            if (data === 'tableForm1') {
                this[data].push({
                    SourceName: '',
                    FundingCost: '',
                    UpperLimit: '',
                    Term: '',
                    Fees: ''
                })
            }
            if (data === 'tableForm2') {
                this[data].push({
                    AssetName: '',
                    AvgAmount: '',
                    InterestRate: '',
                    AssetCount: '',
                    Loss: '',
                    LoanTerm: ''
                })
            }
        },
        formatMoney(row, column, value) {
            return util.formatCurrency(value);
        },
        async calcResult() {
            let ok = true;
            if (this.tableForm1.length === 0 || this.tableForm2.length === 0) {
                ok = await this.$confirm('请确认融资端和资产端均已配置完成', '提示');
            }
            if (ok) {
                let self = this;
                let tpi = new TaskProcess("Task", 'FixedIncomeSuite_RunFundingPlan', "TaskProcess");
                tpi.ShowIndicator(() => {
                    self.getResult();
                })
            }
        },
    }
}
</script>

<style lang="scss">
.fp-mx-bg {
    background: #f4f7ff !important;
    color: #6c80bd;
}

.fp-mx-bg2 {
    background: #f4fffe !important;
    color: #75a791;
}
.fp-red-border {
    .el-input__inner{
        border: 1px solid #f56c6c !important;
        box-shadow: 0 0 3px rgba(245, 108, 108, 0.3);
    }
}
</style>