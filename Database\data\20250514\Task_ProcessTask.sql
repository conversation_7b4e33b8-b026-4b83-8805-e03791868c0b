update `Task_ProcessTask` set `XMLProcessTask` = '<Task>
   <Action ActionCode="DataReady" ActionDisplayName="准备运行脚本" FunctionName="RunPython" SequenceNo="1">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="Analysis_usp_PrepareProductDesignTaskScript.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="TrustID" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="SessionID" SessionParameterName="SessionId" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="AnalysisMode" SessionParameterName="AnalysisMode" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="AnalysisType" SessionParameterName="AnalysisType" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="SensitivityMode" SessionParameterName="SensitivityMode" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="ExecutionMode" SessionParameterName="ExecutionMode" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="StressTestCategory" SessionParameterName="StressTestCategory" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="IsRegion" SessionParameterName="IsRegion" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="RunDate" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="运行压力情景" FunctionName="RunPython" SequenceNo="2">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="FixedIncomeSuite_ProductDesign_PyEngine.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="TrustID" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="SessionID" SessionParameterName="SessionId" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="AnalysisMode" SessionParameterName="AnalysisMode" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="AnalysisType" SessionParameterName="AnalysisType" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="SensitivityMode" SessionParameterName="SensitivityMode" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="ExecutionMode" SessionParameterName="ExecutionMode" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="StressTestCategory" SessionParameterName="StressTestCategory" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="IsRegion" SessionParameterName="IsRegion" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfCDR" SessionParameterName="NumOfCDR" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfCPR" SessionParameterName="NumOfCPR" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfCRR" SessionParameterName="NumOfCRR" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfBondExtraBP" SessionParameterName="NumOfBondExtraBP" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfBondPremium" SessionParameterName="NumOfBondPremium" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfAssetPoolRate" SessionParameterName="NumOfAssetPoolRate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfServiceFeeRate" SessionParameterName="NumOfServiceFeeRate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfDisposalFee" SessionParameterName="NumOfDisposalFee" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="Period" SessionParameterName="Period" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="NumOfDiscountRate" SessionParameterName="NumOfDiscountRate" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="DiscountMethod" SessionParameterName="DiscountMethod" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="UseTermDiscountFactor" SessionParameterName="UseTermDiscountFactor" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="UseDaily" SessionParameterName="UseDaily" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="NumOfDaily" SessionParameterName="NumOfDaily" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
     <Parameter Name="RunDate" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="批量写入报表数据" FunctionName="RunPython" SequenceNo="3">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="Analysis_ProductDesign_WriteToReportData.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="TrustID" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="RunDate" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="计算风险报酬转移数据" FunctionName="RunPython" SequenceNo="4">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="Analysis_ProductDesign_CalculateRiskTransfer.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="TrustID" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="RunDate" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="汇总测算结果-偏离度指标" FunctionName="RunPython" SequenceNo="5">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="Analysis_usp_AssetDeviationTaskScript.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="TrustID" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="5" />
     <Parameter Name="RunDate" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" IsConfigurable="false" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="ABS指标测算" FunctionName="RunPython" SequenceNo="6">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="abs_metrics_calculation.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="TrustID" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="Category" SessionParameterName="AnalysisMode" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="RunDate" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="融资仪表盘统计" FunctionName="RunPython" SequenceNo="7">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="fin_dashboard_bond_balance_task.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="trust_id" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="reporting_date" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" />
   </Action>
   <Action ActionCode="DataReady" ActionDisplayName="融资仪表盘指标统计" FunctionName="RunPython" SequenceNo="7">
     <Parameter Name="UseAbsolutePath" SessionParameterName="" Value="0" DataType="String" Usage="Task" IsConfigurable="false"/>
     <Parameter Name="PythonFilePath" SessionParameterName="" Value="fin_dashboard_trust_metric_task.py" DataType="String" Usage="Task" IsConfigurable="5" IsHidden="false"/>
     <Parameter Name="PythonRuntime" SessionParameterName="" Value="python3" DataType="String" Usage="Task" IsConfigurable="0" IsHidden="false"/>
     <Parameter Name="trust_id" SessionParameterName="TrustID" Value="" DataType="NVarChar" Usage="SQLParameter"/>
     <Parameter Name="reporting_date" SessionParameterName="RunDate" Value="" DataType="NVarChar" Usage="SQLParameter" />
   </Action>
 </Task>'
where `ProcessTaskDescription` = 'FixedIncomeSuite_ProductDesign_PyEngine';