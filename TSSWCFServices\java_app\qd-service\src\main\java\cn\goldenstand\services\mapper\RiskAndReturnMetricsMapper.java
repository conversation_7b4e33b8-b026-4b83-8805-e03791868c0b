/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.mapper;

import java.util.List;
import java.util.Map;

public interface RiskAndReturnMetricsMapper {

    List<Map<String, Object>> getTrustBaseInfo();

    Map<String, Object> getTrustDateInfo(Integer trustId);

    List<Map<String, String>> getAPRMetricsResult(Integer trustId, String trustStartDate, String trustEndDate);

    List<Map<String, String>> getIRRMetricsResult(Integer trustId, String trustStartDate, String trustEndDate);

    List<Map<String, String>> getRIRMetricsResult(Integer trustId, String trustStartDate, String trustEndDate);

    Map<String, String> getActualIRROrRIR(Integer trustId, String repurchaseDate, String metricsType);

}
