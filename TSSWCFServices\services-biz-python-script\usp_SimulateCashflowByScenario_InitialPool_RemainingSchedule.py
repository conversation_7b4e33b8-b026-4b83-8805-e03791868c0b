#coding=utf-8
from usp_SimulateCashflowByScenario_RevolvingPool_Daily_Group import func as usp_SimulateCashflowByScenario_RevolvingPool_Daily_Group
from usp_InitialPool_RemainingSchedule_Daily_Group import func as usp_InitialPool_RemainingSchedule_Daily_Group
from supporting_functions import calculate_initial_pool_stress_effect, calculate_revolving_pool_grouped_stress_effect, save_stressed_results, save_revolving_stressed_results, calculate_revolving_pool_stress_effect
from supporting_functions import save_grouped_revolving_stressed_results

def func(params):
    log_text = "=== 常规压力测试存续池压测入口 ==="
    print(log_text)

    is_revolving = params["IsTopUpAvailable"]

    if "RevolvingPeriodPassed" in params.keys() and params["RevolvingPeriodPassed"]:
        is_revolving = False

    # 无论是循环还是非循环产品，都使用“按日模拟”分池测算
    df_cashflow_details, df_mob_details, df_grouped_mob_details = usp_InitialPool_RemainingSchedule_Daily_Group(params={
        "TrustID":int(params["TrustID"]),
        "SessionID":params["SessionID"],
        "ScenarioID":params["ScenarioID"],
        "SimulationStartDate":params["SimulationStartDate"],
        "AllStressCurves":params["AllStressCurves"],
        "MaxCollectionDate":params["MaxCollectionDate"],
        "TotalRemainingBalance":params["TotalRemainingBalance"],
        "AmortisationCurves":params["AmortisationCurves"],
        "AssetGroups":params["AssetGroups"],
        "SpecifiedGroupID":params["SpecifiedGroupID"],
        "ExistingMOB":params["ExistingMOB"],
        "WAPurchasedDays":params["WAPurchasedDays"],
        "PredictionFinalDate":params["PredictionFinalDate"]
    })

    # 统计存续池的分池加压效果
    calculate_initial_pool_stress_effect(params["TrustID"], params["SessionID"], params["ScenarioID"], params["SimulationStartDate"])

    # 基于当前备选池的方案
    dict_revolving_pool_casflow_details = {}
    dict_revolving_pool_mob_details = {}
    dict_grouped_revolving_pool_mob_details = {}

    # 基于平均备选池的方案
    dict_average_revolving_pool_casflow_details = {}
    dict_average_revolving_pool_mob_details = {}
    dict_average_grouped_revolving_pool_mob_details = {}

    pool_cashflow_history = params["PoolCashflowHistory"]

    # 将已发生的现金流拼接在df_cashflow_details之前
    # df_cashflow_details 的字段：["SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "AccountNo", "PeriodID", "PayDate", "Principal", "Interest", "DefaultPrincipal", "Prepayment", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "InterestPenalty", "CumulativePrepayment", "DefaultRateBase", "PrepaymentRateBase", "RecoveryRateBase", "LoanAmount"]
    # from pool_cashflow_history
    if pool_cashflow_history is not None and len(pool_cashflow_history) > 0:
        pool_cashflow_history["SessionID"] = params["SessionID"]
        pool_cashflow_history["TrustID"] = params["TrustID"]
        pool_cashflow_history["ScenarioID"] = params["ScenarioID"]
        pool_cashflow_history["RevolvingPurchaseID"] = 0 if not is_revolving else -1
        pool_cashflow_history["AccountNo"] = 'AllAssets'

        # reorder columns
        pool_cashflow_history = pool_cashflow_history[["SessionID", "TrustID", "ScenarioID", "RevolvingPurchaseID", "AccountNo", "PeriodID", "PayDate", "Principal", "Interest", "DefaultPrincipal", "Prepayment", "RecoveryPrincipal", "CumulativeDefault", "OpeningBalance", "InterestPenalty", "CumulativePrepayment", "DefaultRateBase", "PrepaymentRateBase", "RecoveryRateBase", "LoanAmount"]]
        print("appending pool_cashflow_history to df_cashflow_details")
        df_cashflow_details = pool_cashflow_history.append(df_cashflow_details, ignore_index=True)

    if not is_revolving:
        # 非循环静态池产品，将现金流保存到数据库（RevolvingPurchaseID=0)
        save_stressed_results(df_cashflow_details, df_mob_details)
    else:
        # 循环产品，将存续池现金流保存到数据库（RevolvingPurchaseID=-1)
        df_cashflow_details_temp = df_cashflow_details.copy()
        df_cashflow_details_temp["RevolvingPurchaseID"] = -1

        df_mob_details_temp = df_mob_details.copy()
        df_mob_details_temp["RevolvingPurchaseID"] = -1

        save_stressed_results(df_cashflow_details_temp, df_mob_details_temp)

    if "SpecifiedGroupID" in params.keys() and params["SpecifiedGroupID"] is not None:
        return (
            df_cashflow_details, 
            df_mob_details, 
            df_grouped_mob_details, 
            dict_revolving_pool_casflow_details, 
            dict_revolving_pool_mob_details, 
            dict_grouped_revolving_pool_mob_details, 
            dict_average_revolving_pool_casflow_details, 
            dict_average_revolving_pool_mob_details, 
            dict_average_grouped_revolving_pool_mob_details
        )

    if is_revolving and ("SpecifiedGroupID" not in params.keys() or params["SpecifiedGroupID"] is None):
        # 对循环池方案现金流加压
        # 注意除了原始现金流之外，还需要截取现金流尾部进行加压，例如尾部12个月、11个月、10个月、直至MinRevolvingPoolLength个月
        (
            dict_revolving_pool_casflow_details,
            dict_revolving_pool_mob_details,
            df_grouped_cashflow_details,
            dict_grouped_revolving_pool_mob_details
        ) = usp_SimulateCashflowByScenario_RevolvingPool_Daily_Group(params={
            "TrustID":params["TrustID"],
            "SessionID":params["SessionID"],
            "ScenarioID":params["ScenarioID"],
            "SimulationStartDate":params["SimulationStartDate"],
            "AllStressCurves":params["AllStressCurves"],
            "UseFirstRevolvingPlan": True,
            "RevolvingPoolAmortisationCurves": params["RevolvingPoolAmortisationCurves"],
            "MaxCollectionDate":params["MaxCollectionDate"],
            "RevolvingPoolAssetGroups":params["RevolvingPoolAssetGroups"],
            "MaxRevolvingPoolLength":params["MaxRevolvingPoolLength"], # 最长的循环池长度
            "MinRevolvingPoolLength":params["MinRevolvingPoolLength"], # 最短的循环池长度, dict中所需要的最小的key
            "PredictionFinalDate":params["PredictionFinalDate"]
        })


        save_grouped_revolving_stressed_results(df_grouped_cashflow_details, dict_grouped_revolving_pool_mob_details)

        save_revolving_stressed_results(dict_revolving_pool_casflow_details, dict_revolving_pool_mob_details)

        calculate_revolving_pool_grouped_stress_effect(params["TrustID"], params["SessionID"], params["ScenarioID"])

        calculate_revolving_pool_stress_effect(params["TrustID"], params["SessionID"], params["ScenarioID"], dict_revolving_pool_casflow_details)

        if (
            params.get("AverageRevolvingPoolAmortisationCurves") is not None
            and params.get("AverageRevolvingPoolAssetGroups") is not None
        ):
            print("计算平均循环池加压后现金流")
            (
                dict_average_revolving_pool_casflow_details,
                dict_average_revolving_pool_mob_details,
                df_average_grouped_cashflow_details,
                dict_average_grouped_revolving_pool_mob_details
            ) = usp_SimulateCashflowByScenario_RevolvingPool_Daily_Group(params={
                "TrustID":params["TrustID"],
                "SessionID":params["SessionID"],
                "ScenarioID":params["ScenarioID"],
                "SimulationStartDate":params["SimulationStartDate"],
                "AllStressCurves":params["AllStressCurves"],
                "UseFirstRevolvingPlan": True,
                "RevolvingPoolAmortisationCurves": params["AverageRevolvingPoolAmortisationCurves"],
                "MaxCollectionDate":params["MaxCollectionDate"],
                "RevolvingPoolAssetGroups":params["AverageRevolvingPoolAssetGroups"],
                "MaxRevolvingPoolLength":params["MaxRevolvingPoolLength"], # 最长的循环池长度
                "MinRevolvingPoolLength":params["MinRevolvingPoolLength"], # 最短的循环池长度, dict中所需要的最小的key
                "PredictionFinalDate":params["PredictionFinalDate"]
            })

            # 将平均循环池的现金流和MOB的RevolvingPurchaseID设置为-3
            if df_average_grouped_cashflow_details is not None:
                df_average_grouped_cashflow_details["RevolvingPurchaseID"] = -3

            if dict_average_grouped_revolving_pool_mob_details is not None:
                for key in dict_average_grouped_revolving_pool_mob_details.keys():
                    dict_average_grouped_revolving_pool_mob_details[key]["RevolvingPurchaseID"] = -3

            print("保存平均循环池分组加压结果到RevolvingPurchaseID=-3")
            save_grouped_revolving_stressed_results(df_average_grouped_cashflow_details, dict_average_grouped_revolving_pool_mob_details)

            if dict_average_revolving_pool_mob_details is not None:
                for key in dict_average_revolving_pool_mob_details.keys():
                    dict_average_revolving_pool_mob_details[key]["RevolvingPurchaseID"] = -3

            if dict_average_revolving_pool_casflow_details is not None:
                for key in dict_average_revolving_pool_casflow_details.keys():
                    dict_average_revolving_pool_casflow_details[key]["RevolvingPurchaseID"] = -3

            print("保存平均循环池加压结果到RevolvingPurchaseID=-3")
            save_revolving_stressed_results(dict_average_revolving_pool_casflow_details, dict_average_revolving_pool_mob_details)

    # 将现金流和MOB返回备用
    # 其中dict_grouped_mob_details用于后续的循环叠加MOB计算
    return (
        df_cashflow_details, 
        df_mob_details, 
        df_grouped_mob_details, 
        dict_revolving_pool_casflow_details, 
        dict_revolving_pool_mob_details, 
        dict_grouped_revolving_pool_mob_details, 
        dict_average_revolving_pool_casflow_details, 
        dict_average_revolving_pool_mob_details, 
        dict_average_grouped_revolving_pool_mob_details
    )
