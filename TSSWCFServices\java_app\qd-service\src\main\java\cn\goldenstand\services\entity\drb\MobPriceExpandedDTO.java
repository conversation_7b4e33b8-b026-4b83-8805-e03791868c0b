/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.entity.drb;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(description = "回购价格计算展开表")
public class MobPriceExpandedDTO {
    private Long id;
    private Long trustId;
    private String productNo;
    private String sessionId;
    private Long scenarioId;
    private String groupId;
    private BigDecimal m0;
    private BigDecimal m1;
    private BigDecimal m2;
    private BigDecimal m3;
    private BigDecimal m4;
    private BigDecimal m5;
    private BigDecimal m6;
    private BigDecimal m6plus;
    private BigDecimal pricingM0;
    private BigDecimal pricingM1;
    private BigDecimal pricingM2;
    private BigDecimal pricingM3;
    private BigDecimal pricingM4;
    private BigDecimal pricingM5;
    private BigDecimal pricingM6;
    private BigDecimal pricingM6plus;
    private BigDecimal priceM0;
    private BigDecimal priceM1;
    private BigDecimal priceM2;
    private BigDecimal priceM3;
    private BigDecimal priceM4;
    private BigDecimal priceM5;
    private BigDecimal priceM6;
    private BigDecimal priceM6plus;
}
