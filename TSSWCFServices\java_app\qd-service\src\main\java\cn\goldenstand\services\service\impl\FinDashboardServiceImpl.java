/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.service.impl;

import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.constant.Status;
import cn.goldenstand.services.entity.drb.BondBalanceDTO;
import cn.goldenstand.services.entity.drb.BondBalanceQueryDTO;
import cn.goldenstand.services.entity.drb.RemainingPrincipalDTO;
import cn.goldenstand.services.entity.drb.TrustMetricDTO;
import cn.goldenstand.services.mapper.FinDashboardMapper;
import cn.goldenstand.services.service.IFinDashboardService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class FinDashboardServiceImpl implements IFinDashboardService {

    @Autowired
    private FinDashboardMapper finDashboardMapper;

    @Override
    public ResultInfo getReportingDates() {
        List<String> reportingDates = finDashboardMapper.getReportingDates();
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, reportingDates);
    }

    @Override
    public ResultInfo getBondBalancePage(Page<BondBalanceDTO> page, BondBalanceQueryDTO queryDTO) {
        IPage<BondBalanceDTO> bondBalancePage = finDashboardMapper.getBondBalancePage(
            page,
            queryDTO.getTrustName(),
            queryDTO.getTrustCode(),
            queryDTO.getReportingDate(),
            queryDTO.getProductType(),
            queryDTO.getCategory(),
            queryDTO.getSortField(),
            queryDTO.getSortOrder()
        );
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, bondBalancePage);
    }

    @Override
    public ResultInfo getBalanceByReportingDate(String reportingDate) {
        List<RemainingPrincipalDTO> remainingPrincipals = finDashboardMapper.getBalanceByReportingDate(reportingDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, remainingPrincipals);
    }

    @Override
    public ResultInfo getSelfHoldingCpbByReportingDate(String reportingDate) {
        List<Map<String, Object>> selfHoldingCpb = finDashboardMapper.getSelfHoldingCpbByReportingDate(reportingDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, selfHoldingCpb);
    }

    @Override
    public ResultInfo getTrustCountByReportingDate(String reportingDate) {
        List<Map<String, Object>> trustCount = finDashboardMapper.getTrustCountByReportingDate(reportingDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, trustCount);
    }

    @Override
    public ResultInfo getAssetRemainingPrincipal(Page<Map<String, Object>> page, BondBalanceQueryDTO queryDTO) {
        IPage<Map<String, Object>> assetRemainingPrincipal = finDashboardMapper.getAssetRemainingPrincipal(
            page,
            queryDTO.getTrustName(),
            queryDTO.getTrustCode(),
            queryDTO.getReportingDate(),
            queryDTO.getProductType(),
            queryDTO.getCategory(),
            queryDTO.getSortField(),
            queryDTO.getSortOrder()
        );
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, assetRemainingPrincipal);
    }

    @Override
    public ResultInfo getTrustMetricPage(Page<TrustMetricDTO> page, BondBalanceQueryDTO queryDTO) {
        IPage<TrustMetricDTO> trustMetricPage = finDashboardMapper.getTrustMetricPage(
            page,
            queryDTO.getTrustName(),
            queryDTO.getTrustCode(),
            queryDTO.getReportingDate(),
            queryDTO.getProductNo(),
            queryDTO.getCategory(),
            queryDTO.getSortField(),
            queryDTO.getSortOrder()
        );
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, trustMetricPage);
    }
}
