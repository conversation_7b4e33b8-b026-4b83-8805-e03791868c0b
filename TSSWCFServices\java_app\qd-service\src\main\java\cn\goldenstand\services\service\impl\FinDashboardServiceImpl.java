/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.service.impl;

import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.constant.Status;
import cn.goldenstand.services.entity.drb.BondBalanceDTO;
import cn.goldenstand.services.entity.drb.BondBalanceQueryDTO;
import cn.goldenstand.services.entity.drb.RemainingPrincipalDTO;
import cn.goldenstand.services.entity.drb.TrustMetricDTO;
import cn.goldenstand.services.mapper.FinDashboardMapper;
import cn.goldenstand.services.service.IFinDashboardService;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Service
public class FinDashboardServiceImpl implements IFinDashboardService {

    @Autowired
    private FinDashboardMapper finDashboardMapper;

    @Override
    public ResultInfo getReportingDates() {
        List<String> reportingDates = finDashboardMapper.getReportingDates();
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, reportingDates);
    }

    @Override
    public ResultInfo getBondBalancePage(Page<BondBalanceDTO> page, BondBalanceQueryDTO queryDTO) {
        IPage<BondBalanceDTO> bondBalancePage = finDashboardMapper.getBondBalancePage(
            page,
            queryDTO.getTrustName(),
            queryDTO.getTrustCode(),
            queryDTO.getReportingDate(),
            queryDTO.getProductType(),
            queryDTO.getCategory(),
            queryDTO.getSortField(),
            queryDTO.getSortOrder()
        );
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, bondBalancePage);
    }

    @Override
    public ResultInfo getBalanceByReportingDate(String reportingDate) {
        List<RemainingPrincipalDTO> remainingPrincipals = finDashboardMapper.getBalanceByReportingDate(reportingDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, remainingPrincipals);
    }

    @Override
    public ResultInfo getSelfHoldingCpbByReportingDate(String reportingDate) {
        List<Map<String, Object>> selfHoldingCpb = finDashboardMapper.getSelfHoldingCpbByReportingDate(reportingDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, selfHoldingCpb);
    }

    @Override
    public ResultInfo getTrustCountByReportingDate(String reportingDate) {
        List<Map<String, Object>> trustCount = finDashboardMapper.getTrustCountByReportingDate(reportingDate);
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, trustCount);
    }

    @Override
    public ResultInfo getAssetRemainingPrincipal(Page<Map<String, Object>> page, BondBalanceQueryDTO queryDTO) {
        IPage<Map<String, Object>> assetRemainingPrincipal = finDashboardMapper.getAssetRemainingPrincipal(
            page,
            queryDTO.getTrustName(),
            queryDTO.getTrustCode(),
            queryDTO.getReportingDate(),
            queryDTO.getProductType(),
            queryDTO.getCategory(),
            queryDTO.getSortField(),
            queryDTO.getSortOrder()
        );
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, assetRemainingPrincipal);
    }

    @Override
    public ResultInfo getTrustMetricPage(Page<TrustMetricDTO> page, BondBalanceQueryDTO queryDTO) {
        IPage<TrustMetricDTO> trustMetricPage = finDashboardMapper.getTrustMetricPage(
            page,
            queryDTO.getTrustName(),
            queryDTO.getTrustCode(),
            queryDTO.getReportingDate(),
            queryDTO.getProductNo(),
            queryDTO.getCategory(),
            queryDTO.getSortField(),
            queryDTO.getSortOrder()
        );
        return new ResultInfo(Status.SUCCESS.code, Status.SUCCESS.message, trustMetricPage);
    }

    @Override
    public void exportTrustMetric(String reportingDate, HttpServletResponse response) throws IOException {
        // 创建一个大数据量的Excel写入器
        String tmpFilePath = System.currentTimeMillis() + "_trust_metric.xlsx";
        ExcelWriter writer = ExcelUtil.getBigWriter(tmpFilePath);

        // 设置列宽
        writer.setColumnWidth(0, 15); // 产品ID
        writer.setColumnWidth(1, 15); // 产品代码
        writer.setColumnWidth(2, 30); // 产品名称
        writer.setColumnWidth(3, 20); // 产品编号
        writer.setColumnWidth(4, 15); // 类别
        writer.setColumnWidth(5, 20); // 可变成分比例
        writer.setColumnWidth(6, 25); // 含超额可变成分比例
        writer.setColumnWidth(7, 20); // 90天以上不良率
        writer.setColumnWidth(8, 20); // 15期年化迁徙率
        writer.setColumnWidth(9, 15); // IRR
        writer.setColumnWidth(10, 15); // 实际利率
        writer.setColumnWidth(11, 20); // 后端收益
        writer.setColumnWidth(12, 20); // 赎回缺口

        // 写入表头（中文）
        writer.writeHeadRow(Arrays.asList(
            "专项计划ID", "专项计划代码", "专项计划名称", "产品类型", "业务类别",
            "可变报酬占比(%)", "可变报酬占比(含超额)(%)", "90+不良率(%)", "APR(MOB15)(%)",
            "IRR(%)", "实收利率(%)", "后端收益(元)", "兑付缺口(元)"
        ));

        // 查询数据
        Page<TrustMetricDTO> page = new Page<>();
        page.setSize(10000); // 设置一个较大的值，以便获取所有数据
        page.setCurrent(1);

        IPage<TrustMetricDTO> trustMetricPage = finDashboardMapper.getTrustMetricPage(
            page,
            null,
            null,
            reportingDate,
            null,
            null,
            null,
            null
        );

        // 写入数据
        for (TrustMetricDTO metric : trustMetricPage.getRecords()) {
            writer.writeRow(Arrays.asList(
                metric.getTrustId(),
                metric.getTrustCode(),
                metric.getTrustName(),
                metric.getProductNo(),
                metric.getCategory(),
                metric.getVarCompRatio(),
                metric.getVarCompRatioInclExcess(),
                metric.getNplRate90dPlus(),
                metric.getAprMob15(),
                metric.getIrr(),
                metric.getActualInterestRate(),
                metric.getBackendRevenue(),
                metric.getRedemptionGap()
            ));
        }

        // 设置响应头
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment; filename=" +
                URLEncoder.encode("存续产品指标_" + reportingDate + ".xlsx", "UTF-8"));

        // 写入响应流
        writer.flush(response.getOutputStream());
        writer.close();
    }
}
