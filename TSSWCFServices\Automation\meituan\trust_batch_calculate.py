import json
import os
import subprocess
import sys
import time
import traceback
from datetime import datetime, timedelta

from fin_dashboard_bond_balance import main as bond_balance
from fin_dashboard_trust_count import get_trust_list as trust_count
from fin_dashboard_trust_metric import main as trust_metric
from helper_dx_send_message import send_text_message
from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from PythonFiles import RunBatchRecordHelper as rd
from PythonFiles.logger_config import Logger

script_name = os.path.basename(__file__)
logger_instance = Logger(maxBytes=50*1024*1024, backupCount=10)

log = logger_instance.get_logger(
    name=script_name,
    log_file_name='trust_batch_calculate.log',
    maxBytes=50*1024*1024,  # 50MB
    backupCount=10
)

script_abspath = os.path.abspath(__file__)
script_abspath_array = script_abspath.split('/') if '/' in script_abspath else script_abspath.split('\\')
script_folder = os.path.normpath('/'.join(script_abspath_array[0:-1]))
tss_folder = os.path.normpath('/'.join(script_abspath_array[0:-3]))
python_scripts_folder = os.path.join(tss_folder, 'services-biz-python-script')

def get_trust_info():
    '''
        排序规则：
        1. 优先级从高到低 1->10
        2. 产品状态: 2循环期 > 3摊还期
        3. 封包日期降序
    '''
    sql = '''
        select tt.TrustId
            , tt.TrustCode
            , tt.Priority
            , tt.TrustCalculationStatus
            , te.ItemValue as TrustStartDate
        from Analysis_Trust tt
        inner join TrustManagement_TrustExtension te
            on tt.TrustId = te.TrustId and te.ItemCode = 'TrustStartDate'
        where tt.IsCalculate = 1 and tt.TrustCalculationStatus in ('2', '3')
        order by Priority, TrustCalculationStatus, TrustStartDate desc
    '''
    return MysqlAdapter.exec_sql_to_df(sql)

def check_data(trust_id, trust_code, trust_status):
    '''
        所有项目：
        1.按分组归集资产信息
        Analysis_AssetDetails
        2.每组资产的未来还款计划归集
        Analysis_GroupedPoolCashflowHistory
        3.每组资产的当前各逾期区间余额
        Analysis_AssetArrearsDetails
        4.每日项目实际信息
        Analysis_PoolCashflowHistory
        5.每个分组的统计金额
        Analysis_AssetPaymentStatus

        循环项目，且在状态为循环期：+下表
        6.每个资产的分组维度对应AccountNo（含备选池信息）
        Analysis_AssetGroups
        7.备选池分布
        abs_to_qd_spare_choose_pool_info
    '''
    # trust_status : 1 发行前 2 循环期 3 摊还期
    # select count(0) from Analysis_AssetDetails where TrustId = {trust_id}; 暂时不加
    sql = f'''
        select count(0) as cnt, 'Analysis_GroupedPoolCashflowHistory' as TableName
        from Analysis_GroupedPoolCashflowHistory where TrustId = {trust_id};

        select count(0) as cnt, 'Analysis_AssetArrearsDetails' as TableName
        from Analysis_AssetArrearsDetails where TrustId = {trust_id};

        select count(0) as cnt, 'Analysis_PoolCashflowHistory' as TableName
        from Analysis_PoolCashflowHistory where TrustId = {trust_id};

        select count(0) as cnt, 'Analysis_AssetPaymentStatus' as TableName
        from Analysis_AssetPaymentStatus where TrustId = {trust_id};

        select if({trust_status} = '2', count(0), 1) as cnt, 'Analysis_AssetGroups' as TableName
        from Analysis_AssetGroups where trust_id = {trust_id};

        select if({trust_status} = '2', count(0), 1) as cnt, 'abs_to_qd_spare_choose_pool_info' as TableName
        from abs_to_qd_spare_choose_pool_info where trust_code = '{trust_code}';
    '''
    data = MysqlAdapter.commonExecuteMultiGetData(sql)

    run_desc = ''
    # 校验全部表，并记录校验结果，如果有一个表无数据，则校验不通过
    for row in data:
        if row[0][0] == 0:
            run_desc += f"{row[0][1]}表无数据\n"

    if len(run_desc) > 0:
        return False, run_desc

    return True, run_desc

def get_simulation_start_date(trust_id):
    sql = f'''
        select DATE_ADD(DATE_FORMAT(max(ReportingDateID), '%Y-%m-%d'), INTERVAL 1 DAY) as SimulationStartDate
        from Analysis_AssetPaymentStatus
        where TrustID = {trust_id};
    '''
    return MysqlAdapter.commonExecuteGetData(sql)[0][0]

def update_simulation_start_date(trust_id, simulation_start_date):
    sql = f'''
        update Analysis_SolutionExtension
        set ItemValue = '{simulation_start_date}'
        where SolutionID = {trust_id} and ItemCode = 'SimulationStartDate';
    '''
    MysqlAdapter.commonExecute(sql)

def call_script(script_name, parameters):
    run_desc = ''
    try:
        result = subprocess.run([sys.executable, script_name, json.dumps(parameters)], check=True, text=True, capture_output=True)

        status = result.returncode == 0 and 'ERROR' not in result.stdout
        if status:
            log.info(f"{script_name}执行成功，参数：{parameters}. 输出: {result.stdout.strip()}")
        else:
            stdout = result.stdout.strip().replace("'",'"').replace(";",'')
            run_desc = f'运行多维度脚本失败：{stdout}'
            log.error(f"{script_name}执行失败，参数：{parameters}. 返回码: {result.returncode}. 输出: {result.stdout.strip()}")

        return status, run_desc
    except subprocess.CalledProcessError as e:
        stdout = str(e).replace("'",'"').replace(";",'')
        run_desc = f'调用多维度脚本报错：{stdout}'
        log.error(f"执行{script_name}报错， 参数：{parameters}，错误信息： {e}")
    except Exception as e:
        stdout = str(e).replace("'",'"').replace(";",'')
        run_desc = f'执行多维度脚本异常：{stdout}'
        log.error(f"执行{script_name}异常: {e}")

    return False, run_desc

def data_preparation(trust_id, trust_code, simulation_start_date, record):
    reporting_date = (datetime.strptime(simulation_start_date, '%Y-%m-%d') - timedelta(days=1)).strftime('%Y-%m-%d')
    scripts_map = [
        ("generate_plans_from_distribution_auto.py"
            , {"TrustID": trust_id, "TrustCode": trust_code, "RunDate": reporting_date, "AverageDays":30}),
        ("load_revolving_distributions_auto_mixed_new.py"
            , {"Params": {"TrustID": trust_id, "CurrentDate": reporting_date, "version": "v1", "AverageDays":30}}),
    ]

    for script, params in scripts_map:
        try:
            status, run_desc = call_script(script, params)
            if status:
                run_desc = f"执行脚本：{script}成功"
                record.update_record(trust_id, 2, run_desc)
                log.info(f"{script}执行成功")
            else:
                run_desc = f"执行脚本：{script}失败"
                record.update_record(trust_id, -1, run_desc)
                log.info(f"{script}执行失败")
                return False, run_desc
        except Exception as e:
            run_desc = f"执行脚本：{script}失败：{e}"
            record.update_record(trust_id, -1, run_desc)
            log.error(f"执行脚本 {script} 失败：{e}")
            return False, run_desc

    return True, run_desc

# 测算产品
def run_trust(row, record):
    trust_id = row["TrustId"]
    trust_code = row["TrustCode"]
    trust_status = row["TrustCalculationStatus"]

    log.info(f"当前测算产品为: {trust_id}")
    run_desc = "开始进行多维度跑批"
    record.update_record(trust_id, 2, run_desc, is_start=True)

    # 校验跑批所需数据是否完备
    check_status, run_desc = check_data(trust_id, trust_code, trust_status)
    if not check_status:
        log.info("数据校验不通过，跳过该产品的测算")
        record.update_record(trust_id, -1, run_desc)
        return

    log.info("数据校验通过")
    run_desc = "数据校验通过"
    record.update_record(trust_id, 2, run_desc)

    simulation_start_date = get_simulation_start_date(trust_id)
    if simulation_start_date is None:
        log.info("模拟起始日期为空，跳过该产品的测算")
        record.update_record(trust_id, -1, run_desc)
        return

    update_simulation_start_date(trust_id, simulation_start_date)
    log.info(f"更新模拟起始日期为: {simulation_start_date}")
    run_desc = f"更新模拟起始日期为：{simulation_start_date}"
    record.update_record(trust_id, 2, run_desc)

    os.chdir(python_scripts_folder)
    log.info(f"当前工作目录为: {python_scripts_folder}")

    # 循环期产品需要运行数据准备脚本
    if trust_status == '2':
        status, run_desc = data_preparation(trust_id, trust_code, simulation_start_date, record)
        if not status:
            return

    # 运行多维度产品设计
    run_desc = "开始运行多维度产品设计"
    record.update_record(trust_id, 2, run_desc)
    log.info("开始运行多维度产品设计")
    os.chdir(script_folder)
    log.info(f"当前工作目录为: {script_folder}")

    status, run_desc = call_script("run_product_design.py", {'TrustID': trust_id})
    if status:
        run_desc = "运行多维度产品设计成功"
        record.update_record(trust_id, 3, run_desc)
        log.info("运行多维度产品设计成功")
    else:
        run_desc = "运行多维度产品设计失败"
        record.update_record(trust_id, -1, run_desc)
        log.info("运行多维度产品设计失败")

def format_elapsed_time(seconds):
    hours, remainder = divmod(seconds, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{hours:02.0f}:{minutes:02.0f}:{seconds:02.0f}"

def main(run_date, task=None):
    # 获取待测算的产品并排序
    trust_info = get_trust_info()
    print(trust_info)
    if trust_info.empty:
        log.info("无待测算产品")
        return

    # 初始化记录类, ProductDesign-多维度产品设计
    record = rd.RecordClass(run_date, 'ProductDesign')

    # 将全部产品写入到日志表中，为待运行状态
    record.delete_all_record()
    trust_info.apply(lambda row: record.insert_record(row["TrustId"]), axis=1)
    # 统计全部待运行的产品并发送大象通知
    trust_count_total = record.count_record_by_status(1)

    start_time = time.perf_counter()
    start_time_str = time.strftime("%Y-%m-%d %H:%M:%S")

    if task is not None:
        execution_date = datetime.strptime(task['execution_date'], "%Y-%m-%d")
        asset_source = task['asset_source']
        category = task['category']
        message_type = task['message_type']
        task_name = task['task_name']
    else:
        execution_date = datetime.now() - timedelta(days=1)
        asset_source = 'all'
        category = 'all'
        message_type = '产品自动化测算'
        task_name = '融资测算自动化跑批（多维度）'

    formatted_date = execution_date.strftime("%Y年%m月%d日")

    msg = (f'''业务类别：{asset_source}_{category}\n'''
        f'''任务类别：{message_type}\n'''
        f'''任务名称：{formatted_date}_{task_name}\n'''
        '任务状态：已启动\n'
        f'开始时间：{start_time_str}\n'
        f'补充说明：本次测算产品总数量为 {trust_count_total} 个，请及时关注测算进度及结果状态：'
        '''[美团融资测算管理系统|https://mf-eco.sankuai.com/qd/quickdeal/#/]''')
    send_text_message('trust_calculation', msg)

    # 依次测算产品
    for index, row in trust_info.iterrows():
        run_trust(row, record)

    # 统计运行成功及失败的产品数量并发送大象通知
    trust_count_success = record.count_record_by_status(3)
    trust_count_failed = record.count_record_by_status(-1)

    end_time = time.perf_counter()
    end_time_str = time.strftime("%Y-%m-%d %H:%M:%S")
    elapsed_time = format_elapsed_time(end_time - start_time)

    msg = (f'''业务类别：{asset_source}_{category}\n'''
        f'''任务类别：{message_type}\n'''
        f'''任务名称：{formatted_date}_{task_name}\n'''
        '任务状态：已完成\n'
        f'开始时间：{start_time_str}\n'
        f'结束时间：{end_time_str}\n'
        f'总计耗时：{elapsed_time}\n'
        f'补充说明：本次测算产品总数量为 {trust_count_total} 个，其中成功 {trust_count_success} 个，失败 {trust_count_failed} 个；'
        '请及时关注测算进度及结果状态：'
        '''[美团融资测算管理系统|https://mf-eco.sankuai.com/qd/quickdeal/#/]''')
    send_text_message('trust_calculation', msg)

    # 融资仪表盘相关统计
    bond_balance(report_date=run_date, trust_id=None)
    trust_count(report_date=run_date)
    trust_metric(report_date=run_date, trust_id=None)

if __name__ == "__main__":
    try:
        run_date = datetime.now().strftime('%Y-%m-%d')
        main(run_date)
    except Exception as e:
        log.error(f"$ERROR {e} {traceback.format_exc()}")
        print("$ERROR", e, traceback.format_exc())