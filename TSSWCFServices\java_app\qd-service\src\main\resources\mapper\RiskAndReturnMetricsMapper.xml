<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.goldenstand.services.mapper.RiskAndReturnMetricsMapper">

    <select id="getTrustBaseInfo" resultType="java.util.Map">
        select t.TrustId
            , TrustCode
            , TrustName
            , case t.TrustCalculationStatus
               when '1' then '发行前'
               when '2' then '循环期'
               when '3' then '摊还期'
               when '4' then '已结束'
               else '' end as TrustStatus
            , t1.ItemValue as TrustStartDate
            , if(t2.ItemValue = 'true', '循环', '静态') as Revolving
            , replace(replace(replace(replace(replace(t3.ItemValue, '00010', '月付'), '00001', '生活费小额')
                , '00007', '生意贷小额'), '00017', '生意贷中额'), '00027', '生活费中额') as ProductNo
            , ifnull(t4.ItemValue, '-') as RepurchaseValueDate
        from Analysis_Trust t
        left join TrustManagement_TrustExtension t1 on t1.TrustId = t.TrustId and t1.ItemCode = 'TrustStartDate'
        left join TrustManagement_TrustExtension t2 on t2.TrustId = t.TrustId and t2.ItemCode = 'IsTopUpAvailable'
        left join TrustManagement_TrustInfoExtension t3 on t3.TrustId = t.TrustId and t3.ItemCode = 'ProductNo'
        left join TrustManagement_TrustInfoExtension t4 on t4.TrustId = t.TrustId and t4.ItemCode = 'RepurchaseValueDate'
        where t.IsCalculate = 1
    </select>

    <select id="getTrustDateInfo" resultType="java.util.Map">
        select t1.ItemValue as TrustStartDate
            , t2.ItemValue as IsTopUpAvailable
            , ifnull(t3.ItemValue, 0) as RevolvingPeriod
            , ifnull(t4.ItemValue, '') as RepurchaseValueDate
        from Analysis_Trust t
        left join TrustManagement_TrustExtension t1 on t1.TrustId = t.TrustId and t1.ItemCode = 'TrustStartDate'
        left join TrustManagement_TrustExtension t2 on t2.TrustId = t.TrustId and t2.ItemCode = 'IsTopUpAvailable'
        left join TrustManagement_TrustExtension t3 on t3.TrustId = t.TrustId and t3.ItemCode = 'RevolvingPeriod'
        left join TrustManagement_TrustInfoExtension t4 on t4.TrustId = t.TrustId and t4.ItemCode = 'RepurchaseValueDate'
        where t.TrustId = #{trustId}
    </select>

    <select id="getAPRMetricsResult" resultType="java.util.Map">
        WITH RECURSIVE DateSeries AS (
            SELECT DATE_FORMAT(DATE_ADD(startdate, INTERVAL 1 MONTH), '%Y-%m-15') AS date
            FROM (SELECT #{trustStartDate} AS startdate, #{trustEndDate} AS enddate) AS dates
            UNION ALL
            SELECT
                CASE
                    WHEN DAY(date) = 15 THEN LAST_DAY(date)
                    ELSE DATE_FORMAT(DATE_ADD(date, INTERVAL 1 MONTH), '%Y-%m-15')
                END AS date
            FROM DateSeries
            WHERE date &lt; #{trustEndDate}
        ), service_fee as (
            SELECT COALESCE(
                (SELECT ItemValue FROM TrustManagement_TrustFee
                WHERE TrustID = #{trustId} AND TrustFeeDisplayName = '浮动服务费' LIMIT 1),0
            ) AS fee_rate
        ), coverage_rate as (
            select ifnull(ItemValue, 100) as coverage_rate
            from Analysis_SolutionExtension s
            where SolutionID = #{trustId} and ItemCode = (
                select if(ItemValue = 'true', 'CoverageRate', 'InitialCoverageRate')
                from TrustManagement_TrustExtension
                where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
            )
        ), plan_id as (
            select if(ItemValue='true', 1, 0) as plan_id
            from TrustManagement_TrustExtension
            where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
        ), coupon_basis as (
            select if(BondExtraBPPointValues is null, 0, t.ItemValue) as coupon_basis
            from Analysis_ProductDesignSensitivitySettings p
            left join TrustManagement_TrustBond t on p.TrustID = t.TrustId
                  and p.BondID = t.TrustBondId and ItemCode = 'CouponBasis'
            where p.TrustID = #{trustId} and AnalysisType = 1 and SensitivityMode='Multiple_DefaultPrepayment' and Type = 3
        ), date_scenario as (
            select date, ifnull(scenario_id, 0) as scenario_id
            from archive_product_design_analysis_result p
            inner join DateSeries d on p.archive_date = adddate(d.date, 1)
            inner join plan_id i on p.plan_id = i.plan_id
            left join service_fee f on f.fee_rate = p.service_fee_rate
            left join coverage_rate r on r.coverage_rate = p.duration_rate
            left join coupon_basis c on c.coupon_basis = p.coupon
            where trust_id = #{trustId} and cdr = 1 and cpr = 1 and premium = 0
        )
        select cast(d.date as char(10)) as archive_date
            , end_date
            , cast(metrics_result as char(10)) as metrics_result
        from archive_abs_metrics_calculate_result r
        inner join date_scenario d on r.archive_date = adddate(d.date, 1) and r.scenario_id = d.scenario_id
        where category = 'ProductDesign' and trust_id = #{trustId} and metrics_type = 'APR';
    </select>

    <select id="getIRRMetricsResult" resultType="java.util.Map">
        WITH RECURSIVE DateSeries AS (
            SELECT DATE_FORMAT(DATE_ADD(startdate, INTERVAL 1 MONTH), '%Y-%m-15') AS date
            FROM (SELECT #{trustStartDate} AS startdate, #{trustEndDate} AS enddate) AS dates
            UNION ALL
            SELECT
                CASE
                    WHEN DAY(date) = 15 THEN LAST_DAY(date)
                    ELSE DATE_FORMAT(DATE_ADD(date, INTERVAL 1 MONTH), '%Y-%m-15')
                END AS date
            FROM DateSeries
            WHERE date &lt; #{trustEndDate}
        ), service_fee as (
            SELECT COALESCE(
                (SELECT ItemValue FROM TrustManagement_TrustFee
                WHERE TrustID = #{trustId} AND TrustFeeDisplayName = '浮动服务费' LIMIT 1),0
            ) AS fee_rate
        ), coverage_rate as (
            select ifnull(ItemValue, 100) as coverage_rate
            from Analysis_SolutionExtension s
            where SolutionID = #{trustId} and ItemCode = (
                select if(ItemValue = 'true', 'CoverageRate', 'InitialCoverageRate')
                from TrustManagement_TrustExtension
                where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
            )
        ), plan_id as (
            select if(ItemValue='true', 1, 0) as plan_id
            from TrustManagement_TrustExtension
            where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
        ), coupon_basis as (
            select if(BondExtraBPPointValues is null, 0, t.ItemValue) as coupon_basis
            from Analysis_ProductDesignSensitivitySettings p
            left join TrustManagement_TrustBond t on p.TrustID = t.TrustId
                and p.BondID = t.TrustBondId and ItemCode = 'CouponBasis'
            where p.TrustID = #{trustId} and AnalysisType = 1 and SensitivityMode='Multiple_DefaultPrepayment' and Type = 3
        ), date_scenario as (
            select date, ifnull(scenario_id, 0) as scenario_id
            from archive_product_design_analysis_result p
            inner join DateSeries d on p.archive_date = adddate(d.date, 1)
            inner join plan_id i on p.plan_id = i.plan_id
            left join service_fee f on f.fee_rate = p.service_fee_rate
            left join coverage_rate r on r.coverage_rate = p.duration_rate
            left join coupon_basis c on c.coupon_basis = p.coupon
            where trust_id = #{trustId} and cdr = 1 and cpr = 1 and premium = 0
        )
        select cast(d.date as char(10)) as archive_date
            , cast(metrics_result as char(10)) as metrics_result
        from archive_abs_metrics_calculate_result r
        inner join date_scenario d on r.archive_date = adddate(d.date, 1) and r.scenario_id = d.scenario_id
        where category = 'ProductDesign' and trust_id = #{trustId}
            and metrics_type = 'IRR' and asset_type = '总'
        order by archive_date
    </select>

    <select id="getRIRMetricsResult" resultType="java.util.Map">
        WITH RECURSIVE DateSeries AS (
            SELECT DATE_FORMAT(DATE_ADD(startdate, INTERVAL 1 MONTH), '%Y-%m-15') AS date
            FROM (SELECT #{trustStartDate} AS startdate, #{trustEndDate} AS enddate) AS dates
            UNION ALL
            SELECT
                CASE
                    WHEN DAY(date) = 15 THEN LAST_DAY(date)
                    ELSE DATE_FORMAT(DATE_ADD(date, INTERVAL 1 MONTH), '%Y-%m-15')
                END AS date
            FROM DateSeries
            WHERE date &lt; #{trustEndDate}
        ), service_fee as (
            SELECT COALESCE(
                (SELECT ItemValue FROM TrustManagement_TrustFee
                WHERE TrustID = #{trustId} AND TrustFeeDisplayName = '浮动服务费' LIMIT 1),0
            ) AS fee_rate
        ), coverage_rate as (
            select ifnull(ItemValue, 100) as coverage_rate
            from Analysis_SolutionExtension s
            where SolutionID = #{trustId} and ItemCode = (
                select case when ItemValue = 'true' then 'CoverageRate' else 'InitialCoverageRate' end
                from TrustManagement_TrustExtension
                where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
            )
        ), plan_id as (
            select if(ItemValue='true', 1, 0) as plan_id
            from TrustManagement_TrustExtension
            where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
        ), coupon_basis as (
            select if(BondExtraBPPointValues is null, 0, t.ItemValue) as coupon_basis
            from Analysis_ProductDesignSensitivitySettings p
            left join TrustManagement_TrustBond t on p.TrustID = t.TrustId
                and p.BondID = t.TrustBondId and ItemCode = 'CouponBasis'
            where p.TrustID = #{trustId} and AnalysisType = 1 and SensitivityMode='Multiple_DefaultPrepayment' and Type = 3
        ), date_scenario as (
            select date, ifnull(scenario_id, 0) as scenario_id
            from archive_product_design_analysis_result p
            inner join DateSeries d on p.archive_date = adddate(d.date, 1)
            inner join plan_id i on p.plan_id = i.plan_id
            left join service_fee f on f.fee_rate = p.service_fee_rate
            left join coverage_rate r on r.coverage_rate = p.duration_rate
            left join coupon_basis c on c.coupon_basis = p.coupon
            where trust_id = #{trustId} and cdr = 1 and cpr = 1 and premium = 0
        )
        select cast(d.date as char(10)) as archive_date
           , cast(metrics_result as char(10)) as metrics_result
        from archive_abs_metrics_calculate_result r
        inner join date_scenario d on r.archive_date = adddate(d.date, 1) and r.scenario_id = d.scenario_id
        where category = 'ProductDesign' and trust_id = #{trustId}
            and metrics_type = 'RIR' and asset_type = '总'
        order by archive_date
    </select>

    <select id="getActualIRROrRIR" resultType="java.util.Map">
        with service_fee as (
            SELECT COALESCE(
                (SELECT ItemValue FROM TrustManagement_TrustFee
                WHERE TrustID = #{trustId} AND TrustFeeDisplayName = '浮动服务费' LIMIT 1),0
            ) AS fee_rate
        ), coverage_rate as (
            select ifnull(ItemValue, 100) as coverage_rate
            from Analysis_SolutionExtension s
            where SolutionID = #{trustId} and ItemCode = (
                select case when ItemValue = 'true' then 'CoverageRate' else 'InitialCoverageRate' end
                from TrustManagement_TrustExtension
                where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
            )
        ), plan_id as (
            select if(ItemValue='true', 1, 0) as plan_id
            from TrustManagement_TrustExtension
            where TrustId = #{trustId} and ItemCode = 'IsTopUpAvailable'
        ), coupon_basis as (
            select if(BondExtraBPPointValues is null, 0, t.ItemValue) as coupon_basis
            from Analysis_ProductDesignSensitivitySettings p
            left join TrustManagement_TrustBond t on p.TrustID = t.TrustId
                and p.BondID = t.TrustBondId and ItemCode = 'CouponBasis'
            where p.TrustID = #{trustId} and AnalysisType = 1 and SensitivityMode='Multiple_DefaultPrepayment' and Type = 3
        ), date_scenario as (
            select #{repurchaseDate} as date, ifnull(scenario_id, 0) as scenario_id
            from archive_product_design_analysis_result p
            inner join plan_id i on p.plan_id = i.plan_id
            left join service_fee f on f.fee_rate = p.service_fee_rate
            left join coverage_rate r on r.coverage_rate = p.duration_rate
            left join coupon_basis c on c.coupon_basis = p.coupon
            where archive_date = adddate(#{repurchaseDate}, 1) and trust_id = #{trustId} and cdr = 1 and cpr = 1 and premium = 0
        )
        select cast(round(metrics_result * 100, 4) as char(10)) as metrics_result
        from archive_abs_metrics_calculate_result r
        inner join date_scenario d on r.archive_date = adddate(d.date, 1) and r.scenario_id = d.scenario_id
        where category = 'ProductDesign' and trust_id = #{trustId}
            and metrics_type = #{metricsType} and asset_type = '总';
    </select>

</mapper>