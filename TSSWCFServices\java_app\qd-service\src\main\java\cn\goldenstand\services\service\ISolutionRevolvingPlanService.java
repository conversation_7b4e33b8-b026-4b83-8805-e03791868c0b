/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.service;

import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.entity.SolutionRevolvingPlan;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 */
public interface ISolutionRevolvingPlanService extends IService<SolutionRevolvingPlan> {

    ResultInfo getSolutionRevolvingPool(String trustId);

    ResultInfo getSolutionSettingCmb(String trustId);

    ResultInfo getScenarios(String trustId);

    ResultInfo getExtSetting(String trustId);

    ResultInfo getPortfolioPaymentSequence(String trustId);

    ResultInfo getBondPremiumSettings(String trustId);

    ResultInfo getTrustBondExtraBpSettings(String trustId);

    ResultInfo getFloatRateSettings(String trustId);

    ResultInfo getSolutionSetting(String trustId);

    /**
     * 获取MOB价格汇总数据
     *
     * @param trustId 产品ID
     * @param sessionId 会话ID
     * @param scenarioId 情景ID
     * @return MOB价格汇总数据
     */
    ResultInfo getMobPriceSummary(Long trustId, String sessionId, Long scenarioId);
}
