# coding: utf-8
import os
import sys
import pandas as pd
import json
import random
import traceback
import urllib.parse
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from PythonFiles import dbhelper as dbh, SQLParamsParser, MysqlAdapter2Java as MysqlAdapter
from Analysis_Trust_GetConfigs_Meituan import get_asset_grouping_config
from distributions_utils import process_distribution_data, calculate_distribution
from distributions_helper import (
    generate_assets_from_distribution_stratified_adjusted,
    generate_assets_from_distribution_stratified_adjusted_v1
)
from distributions_helper2 import (
    generate_assets_from_distribution_stratified_adjusted_v2
)

# 初始随机种子
RANDOM_SEED = 42
random.seed(RANDOM_SEED)
NUMBER_OF_LOANS = 4000

def delete_existing_plan_data(trust_id, plan_id):
    try:
        delete_sql = f"""
            delete from daily_rev_plans where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_cashflows where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_distribution_comparison where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_loans where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_loan_cashflow where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_cashflows_grouped where trust_id = {trust_id} and plan_id = {plan_id};
            delete from trust_revolving_purchase_distributions where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_groups where trust_id = {trust_id} and plan_id = {plan_id};
            delete from daily_rev_plan_amortisation_plan where trust_id = {trust_id} and plan_id = {plan_id};
        """
        MysqlAdapter.commonRunsql(delete_sql)

    except Exception as error:
        print("Failed to delete data from MySQL table: {}".format(error))

def delete_existing_data(params):
    trust_id = params["TrustID"]

    current_date = datetime.now().date().strftime("%Y-%m-%d")

    if "CurrentDate" in params.keys():
        current_date = params["CurrentDate"]

    try:
        time_delete_start = datetime.now()
        delete_sql = f"""
            delete from daily_rev_plans where trust_id = {trust_id};
            delete from daily_rev_plan_cashflows where trust_id = {trust_id};
            delete from daily_rev_plan_distribution_comparison where trust_id = {trust_id};
            delete from daily_rev_plan_loans where trust_id = {trust_id};
            delete from daily_rev_plan_loan_cashflow where trust_id = {trust_id};
            delete from daily_rev_plan_cashflows_grouped where trust_id = {trust_id};
            delete from trust_revolving_purchase_distributions where trust_id = {trust_id} and date = '{current_date}';
            delete from daily_rev_plan_groups where trust_id = {trust_id};
            delete from daily_rev_plan_amortisation_plan where trust_id = {trust_id};
        """
        MysqlAdapter.commonRunsql(delete_sql)
        time_delete_end = datetime.now()
        print("删除数据用时", (time_delete_end - time_delete_start).seconds, "秒")

    except Exception as error:
        print("Failed to delete data from MySQL table: {}".format(error))


def insert_plan_record(trust_id, plan_id, plan_name, plan_xirr):
    try:
        insert_query = "INSERT INTO daily_rev_plans (trust_id, plan_id, plan_name, plan_xirr) VALUES (%s, %s, %s, %s)"
        dbcn_taskprocess = dbh.open_dbconn("TaskProcess")
        dbh.exec_commit(
            insert_query,
            dbcn=dbcn_taskprocess,
            parameters=(trust_id, plan_id, plan_name, plan_xirr),
        )
    except Exception as error:
        print("Error inserting into daily_rev_plans:", error)

def xirr(cashflows, dates, initial_guess=0.1):
    """
    Calculate the Extended Internal Rate of Return (XIRR) for a series of cash flows.

    Parameters:
    cashflows (list of float): A list of cash flow amounts, where negative values represent investments and positive values represent returns.
    dates (list of datetime): A list of dates corresponding to each cash flow.
    initial_guess (float): An initial guess for the XIRR calculation.

    Returns:
    float: The calculated XIRR value.
    """
    def f(rate):
        """The function to find the root of."""
        return sum(
            [
                cf / (1 + rate) ** ((date - dates[0]).days / 365)
                for cf, date in zip(cashflows, dates)
            ]
        )

    def df(rate):
        """Derivative of the function above."""
        return sum(
            [
                -cf
                * ((date - dates[0]).days / 365)
                / (1 + rate) ** ((date - dates[0]).days / 365 + 1)
                for cf, date in zip(cashflows, dates)
            ]
        )

    tolerance = 1e-7
    max_iterations = 100
    rate = initial_guess
    for i in range(max_iterations):
        rate -= f(rate) / df(rate)
        if abs(f(rate)) < tolerance:
            return rate

    raise ValueError("XIRR calculation did not converge")

# 对生成的资产进行分布统计（使用daily_rev_plan_distributions中定义的区间）
def calculate_generated_distributions(trust_id, plan_id, category_loans_df, df_plan_distributions):

    # 将结果保存在一个list中，list包含如下字段：
    # asset_category, distribution_name, bucket_left, bucket_right, percentage
    # 遍历asset_category，distribution_name，将分布统计结果加入generated_distributions
    generated_distributions = []

    asset_category_distribution = df_plan_distributions[df_plan_distributions["asset_category"] == 0].drop(columns=["asset_category"])
    distribution_buckets = process_distribution_data(asset_category_distribution, "asset_category")
    generated_distribution = calculate_distribution(category_loans_df, "asset_category", distribution_buckets)
    for bucket, percentage in generated_distribution.items():
        specified_percentage = distribution_buckets[bucket]
        generated_distributions.append(
            {
                "asset_category": 0,
                "distribution_type": "asset_category",
                "bucket": '(' + str(bucket[0]) + ', ' + str(bucket[1]) + ']',
                "generated_percentage": percentage,
                "specified_percentage": specified_percentage,
            }
        )

    target_distributions = df_plan_distributions[df_plan_distributions["asset_category"] != 0]

    for asset_category in target_distributions["asset_category"].unique():
        asset_category_target_distributions = target_distributions[
            target_distributions["asset_category"] == asset_category
        ].drop(columns=["asset_category"])

        df_loans = category_loans_df[category_loans_df["asset_category"] == asset_category]

        for distribution_name in asset_category_target_distributions["distribution_name"].unique():
            #print("计算资产一级分类", asset_category, "的", distribution_name, "分布")
            distribution_buckets = process_distribution_data(asset_category_target_distributions, distribution_name)
            #print(distribution_buckets)
            # {(0.0, 1.0): 0.4, (1.0, 2.0): 0.6}

            # 按分布统计生成的资产
            loan_column_name_mapping = {
                "AssetSubCategory": "asset_sub_category",
                "LoanTerm": "loan_term",
                "RemainingTerm": "remaining_term",
                "CreditRating": "credit_rating",
            }
            # 按照distribution_buckets对df_loans进行分布统计
            generated_distribution = calculate_distribution(df_loans, loan_column_name_mapping[distribution_name], distribution_buckets)

            #print("generated_distribution:", generated_distribution)

            # 将生成的分布统计结果加入generated_distributions(包括distribution_buckets中的percentage作为'specified_percentage')
            for bucket, percentage in generated_distribution.items():
                specified_percentage = distribution_buckets[bucket]
                generated_distributions.append(
                    {
                        "asset_category": asset_category,
                        "distribution_type": distribution_name,
                        "bucket": '(' + str(bucket[0]) + ', ' + str(bucket[1]) + ']',
                        "generated_percentage": percentage,
                        "specified_percentage": specified_percentage,
                    }
                )

    # 统计df_loans一级分类占比并append到generated_distributions (asset_category = 0)
    print("计算资产一级分类占比")
    asset_category_distribution = df_plan_distributions[df_plan_distributions["asset_category"] == 0].drop(columns=["asset_category"])
    print(asset_category_distribution)
    distribution_buckets = process_distribution_data(asset_category_distribution, "AssetCategory")
    print(distribution_buckets)
    generated_category_distribution = calculate_distribution(category_loans_df, "asset_category", distribution_buckets)
    for bucket, percentage in generated_category_distribution.items():
        specified_percentage = distribution_buckets[bucket]
        generated_distributions.append(
            {
                "asset_category": 0,
                "distribution_type": "asset_category",
                "bucket": '(' + str(bucket[0]) + ', ' + str(bucket[1]) + ']',
                "generated_percentage": percentage,
                "specified_percentage": specified_percentage,
            }
        )

        print("appended asset_category", bucket, "generated_percentage:", percentage, "specified_percentage:", specified_percentage)
    
    # 将生成的分布统计结果保存到daily_rev_plan_distribution_comparison
    df_generated_distributions = pd.DataFrame(generated_distributions)

    # add trust_id and plan_id to the generated_distributions
    df_generated_distributions["trust_id"] = trust_id
    df_generated_distributions["plan_id"] = plan_id

    # 打印分布比较信息
    print("\n=== 分布比较分析 ===")
    for index, row in df_generated_distributions.iterrows():
        deviation = row["generated_percentage"] - row["specified_percentage"]
        deviation_pct = deviation * 100
        print(f"资产类别: {row['asset_category']}, 分布类型: {row['distribution_type']}, 区间: {row['bucket']}")
        print(f"  指定比例: {row['specified_percentage']:.4f}, 实际比例: {row['generated_percentage']:.4f}, 偏离: {deviation_pct:.2f}%")

    # save generated_distributions to daily_rev_plan_distribution_comparison
    MysqlAdapter.dataframe_tosql(df_generated_distributions, "daily_rev_plan_distribution_comparison")


def process_asset_category(
    asset_category,
    available_cash,
    pool_date,
    df_distributions,
    df_asset_groups_interest_rates,
    grouping_configs,
    discount_method,
    asset_group_cache
):
    # 共4个分布：产品号（生意贷、生活费）、合同期限、剩余期限、风险分组
    asset_subcategory_buckets = process_distribution_data(
        df_distributions, "AssetSubCategory"
    )
    loan_term_buckets = process_distribution_data(df_distributions, "LoanTerm")
    remaining_term_buckets = process_distribution_data(
        df_distributions, "RemainingTerm"
    )
    credit_rating_buckets = process_distribution_data(df_distributions, "CreditRating")

    average_loan_balance = available_cash / NUMBER_OF_LOANS

    loans_df, cashflows_df, num_of_loans = generate_assets_from_distribution_stratified_adjusted(
        asset_category,
        asset_subcategory_buckets,
        loan_term_buckets,
        remaining_term_buckets,
        credit_rating_buckets,
        available_cash,
        pool_date,
        average_loan_balance,
        df_asset_groups_interest_rates,
        grouping_configs,
        discount_method,
        asset_group_cache
    )

    print("generated", loans_df.shape[0], "loans for asset_category", asset_category)

    return loans_df, cashflows_df


def save_loans(loans_df, df_pi_loan_cashflows, trust_id, plan_id):
    # find loan_amount by looking up loan_term, interest_rate and remaining_term from pi_loan_cashflows
    '''
        CREATE TABLE `pi_loan_cashflows` (
            `loan_term` int NOT NULL,
            `interest_rate` decimal(15,4) NOT NULL,
            `period` int NOT NULL,
            `remaining_term` int NOT NULL,
            `remaining_percent` decimal(15,4) NOT NULL,
            PRIMARY KEY (`loan_term`,`interest_rate`,`remaining_term`)
        ) ENGINE=InnoDB COMMENT='等额本息摊还比例表';
    
    '''
    #time_start = datetime.now()

    #print("计算资产合同金额")
    # look up loan_amount (loans_df.remaining_balance / df_pi_loan_cashflows.remaining_percent) and add it to loans_df
    # inner join on remaining_term, interest_rate and loan_term
    #time_x_1 = datetime.now()
    # print(loans_df.head(50))
    # print(df_pi_loan_cashflows.head(50))

    loans_df = loans_df.merge(
        df_pi_loan_cashflows,
        how="left",
        left_on=["remaining_term", "interest_rate", "loan_term"],
        right_on=["remaining_term", "interest_rate", "loan_term"],
    )

    loans_df["loan_amount"] = loans_df["remaining_balance"] / loans_df["remaining_percent"]

    #print(loans_df.head(50))

    #print(loans_df.columns)
    # Index(['loan_id', 'remaining_balance', 'asset_category', 'asset_sub_category',
    #    'loan_term', 'seasoning', 'remaining_term', 'credit_rating',
    #    'interest_rate', 'wal', 'discount_factor', 'group_id',
    #    'remaining_percent', 'loan_amount'],
    #   dtype='object')
    loans_df.drop(columns=["remaining_percent"], inplace=True)

    # add trust_id and plan_id to the loans_df
    loans_df["trust_id"] = trust_id
    loans_df["plan_id"] = plan_id
    #time_x_2 = datetime.now()
    #print("处理loans_df用时", (time_x_2 - time_x_1).total_seconds(), "秒")

    #time_x_1 = datetime.now()
    # save loans_df to daily_rev_plan_loans
    loans_df_to_db = loans_df[["trust_id", "plan_id", "loan_id", "remaining_balance", "asset_category", "asset_sub_category", "loan_term", "seasoning", "remaining_term", "credit_rating", "interest_rate", "wal", "discount_factor", "group_id", "loan_amount"]]
    MysqlAdapter.dataframe_tosql(loans_df_to_db, "daily_rev_plan_loans")
    #time_x_2 = datetime.now()
    #print("入库daily_rev_plan_loans用时", (time_x_2 - time_x_1).total_seconds(), "秒")

    time_x_1 = datetime.now()
    # 按group_id汇总存入daily_rev_plan_groups
    # sum(loan_principal), max(remaining_term), weighted_average(seasoning) from loans_df by group_id
    loans_grouped = (
        loans_df.groupby(["group_id"])
        .agg(
            {
                "loan_amount": "sum",
                "remaining_balance": "sum",
                "remaining_term_days": "max",
                "seasoning_days": lambda x: sum(x * loans_df.loc[x.index, "remaining_balance"])
                / sum(loans_df.loc[x.index, "remaining_balance"]),
            }
        )
        .reset_index()
    )

    # seasoning_days to int
    loans_grouped["seasoning_days"] = loans_grouped["seasoning_days"].astype(int)

    #print(loans_grouped.head(50))

    # add trust_id and plan_id to the loans_grouped
    loans_grouped["trust_id"] = trust_id
    loans_grouped["plan_id"] = plan_id

    # rename remaining_term_days to remaining_term, seasoning_days to seasoning
    loans_grouped = loans_grouped.rename(columns={"remaining_term_days": "remaining_term", "seasoning_days": "seasoning"})
    time_x_2 = datetime.now()
    #print("按group_id加总用时", (time_x_2 - time_x_1).total_seconds(), "秒")

    time_x_1 = datetime.now()
    # save loans_grouped to daily_rev_plan_groups
    MysqlAdapter.dataframe_tosql(loans_grouped, "daily_rev_plan_groups")
    time_end = time_x_2 = datetime.now()
    #print("入库daily_rev_plan_groups用时", (time_x_2 - time_x_1).total_seconds(), "秒")

    #print("总用时", (time_end - time_start).total_seconds(), "秒")

    """
        CREATE TABLE `daily_rev_plan_groups` (
        `trust_id` int NOT NULL,
        `plan_id` int NOT NULL,
        `group_id` varchar(20) NOT NULL,
        `loan_amount` decimal(19,2) DEFAULT NULL,
        `remaining_balance` decimal(19,2) DEFAULT NULL,
        `seasoning` int DEFAULT NULL COMMENT '账龄',
        `remaining_term` int DEFAULT NULL,
        PRIMARY KEY (`trust_id`,`plan_id`,`group_id`)
        ) ENGINE=InnoDB COMMENT='循环池资产分组';
    """
    return loans_grouped


def save_cashflows(cashflows_df, loans_grouped, trust_id, plan_id, current_date):
    #print("将现金流按group_id汇总，形成按日现金流，存入daily_rev_plan_cashflows_grouped")
    #time1 = datetime.now()
    # sum(principal) and sum(interest) from cashflows by group_id and payment_date
    cashflows_grouped = (
        cashflows_df.groupby(["group_id", "payment_date"])
        .agg({"principal": "sum", "interest": "sum"})
        .reset_index()
    )
    #time2 = datetime.now()
    #print("按group_id和payment_date加总用时", (time2 - time1).total_seconds(), "秒")

    #print("columns of cashflows_grouped:", cashflows_grouped.columns)
    # columns of cashflows_grouped: Index(['group_id', 'payment_date', 'principal', 'interest'], dtype='object')

    # each group of cashflows might not have consecutive payment dates
    # e.g. group_id = 1 has payment_date = 2021-01-01, 2021-01-03, 2021-01-05, ..., 2022-12-29, 2022-12-31
    #      group_id = 2 has payment_date = 2021-01-02, 2021-01-04, 2021-01-06, ..., 2021-12-28, 2021-12-30
    # for each group, fill in missing dates with 0 principal and 0 interest, so all groups of cashflows have consecutive payment dates
    # e.g. group_id = 1 has payment_date = 2021-01-01, 2021-01-02, 2021-01-03, 2021-01-04, 2021-01-05, ..., 2022-12-29, 2022-12-30, 2022-12-31
    #      group_id = 2 has payment_date = 2021-01-02, 2021-01-03, 2021-01-04, 2021-01-05, ..., 2021-12-28, 2021-12-29, 2021-12-30

    cashflows_filled = pd.DataFrame()
    for group_id in cashflows_grouped["group_id"].unique():
        group_cashflows = cashflows_grouped[cashflows_grouped["group_id"] == group_id]
        max_date = group_cashflows["payment_date"].max()
        all_dates = pd.date_range(start=current_date, end=max_date, freq="D")
        group_cashflows = (
            group_cashflows.set_index("payment_date")
            .reindex(all_dates)
            .fillna(0)
            .reset_index()
        )
        group_cashflows["group_id"] = group_id
        cashflows_filled = pd.concat([cashflows_filled, group_cashflows])

    # rename 'index' column back to 'payment_date'
    cashflows_filled = cashflows_filled.rename(columns={"index": "payment_date"})

    # Step 3: Add `trust_id` and `plan_id` columns to identify the trust and plan

    cashflows_filled["trust_id"] = trust_id
    cashflows_filled["plan_id"] = plan_id
    #time3 = datetime.now()
    #print("填充连续日期用时", (time3 - time2).total_seconds(), "秒")

    calculated_xirr = 0

    # save cashflows_filled to daily_rev_plan_cashflows_grouped
    if not cashflows_filled.empty:
        time1 = datetime.now()
        MysqlAdapter.dataframe_tosql(
            cashflows_filled, "daily_rev_plan_cashflows_grouped"
        )
        time2 = datetime.now()
        print(
            "saved",
            cashflows_filled.shape[0],
            "rows to daily_rev_plan_cashflows_grouped in", (time2 - time1).total_seconds(), "秒"
        )

        total_remaining_balance = loans_grouped["remaining_balance"].sum()
        calculated_xirr = calculate_plan_xirr(cashflows_grouped, total_remaining_balance, current_date)
    else:
        print("No cashflows to save for trust_id:", trust_id, "and plan_id:", plan_id)

    # step 4: 生成摊还计划表备用
    # insert into daily_rev_plan_amortisation_plan (trust_id, plan_id, group_id, seq_no, payment_date, principal, interest, opening_balance, principal_percent, interest_rate)
    # similar to the below logic, using cashflows_filled
    # each day's opening_balance = loans_grouped.loan_amount - cashflows_filled.principal.cumsum() + cashflows_filled.principal
    # look up loans_grouped.loan_amount by group_id
    #time4 = datetime.now()

    df_amortisation_plan = cashflows_filled.copy()

    # now calculate each day's opening_balance
    # df_amortisation_plan left join loans_grouped on group_id, to get remaining_balance
    df_amortisation_plan = df_amortisation_plan.merge(
        loans_grouped[["group_id", "remaining_balance"]], how="left", on="group_id"
    )

    # calculate cumsum of principal and add it to df_amortisation_plan
    df_amortisation_plan["principal_cumsum"] = df_amortisation_plan.groupby("group_id")["principal"].cumsum()

    # opening_balance = remaing_balance - principal_cumsum + principal
    df_amortisation_plan["opening_balance"] = (
        df_amortisation_plan["remaining_balance"] - df_amortisation_plan["principal_cumsum"] + df_amortisation_plan["principal"]
    )

    # drop remaining_balance and principal_cumsum
    df_amortisation_plan = df_amortisation_plan.drop(columns=["remaining_balance", "principal_cumsum"])

    df_amortisation_plan["principal_percent"] = (
        df_amortisation_plan["principal"] / df_amortisation_plan["opening_balance"]
    )
    df_amortisation_plan["interest_rate"] = (
        df_amortisation_plan["interest"] / df_amortisation_plan["opening_balance"]
    )

    df_amortisation_plan["seq_no"] = df_amortisation_plan.groupby("group_id").cumcount() + 1
    time5 = datetime.now()
    #print("生成摊还计划表用时", (time5 - time4).total_seconds(), "秒")

    # save df_amortisation_plan to daily_rev_plan_amortisation_plan
    MysqlAdapter.dataframe_tosql(df_amortisation_plan, "daily_rev_plan_amortisation_plan")
    time6 = datetime.now()
    print("摊还计划表入库用时", (time6 - time5).total_seconds(), "秒, 数据条数", df_amortisation_plan.shape[0])

    # create table daily_rev_plan_amortisation_plan
    """
        CREATE TABLE `daily_rev_plan_amortisation_plan` (
        `trust_id` int NOT NULL,
        `plan_id` int NOT NULL,
        `group_id` varchar(20) NOT NULL,
        `seq_no` int NOT NULL,
        `payment_date` date NOT NULL,
        `principal` decimal(19,2) DEFAULT NULL,
        `interest` decimal(19,2) DEFAULT NULL,
        `opening_balance` decimal(19,2) DEFAULT NULL,
        `principal_percent` decimal(19,4) DEFAULT NULL,
        `interest_rate` decimal(19,4) DEFAULT NULL,
        PRIMARY KEY (`trust_id`,`plan_id`,`group_id`,`seq_no`)
        ) ENGINE=InnoDB COMMENT='循环池资产摊还计划';
    """

    return calculated_xirr

def calculate_plan_xirr(cashflows_grouped, total_remaining_balance, current_date):
    # investment is the total_remaining_balance at current_date
    # cashflow is cashflows_grouped.principal + cashflows_grouped.interest
    # payment_date is cashflows_grouped.payment_date
    # calculate xirr
    cashflows = cashflows_grouped["principal"] + cashflows_grouped["interest"]
    dates = cashflows_grouped["payment_date"]
    cashflows = cashflows.tolist()
    dates = dates.tolist()
    cashflows.insert(0, -total_remaining_balance)
    dates.insert(0, current_date)
    try:
        calculated_xirr = xirr(cashflows, dates)

        # to 4 decimal places
        calculated_xirr = round(calculated_xirr, 4)

    except ValueError as e:
        print(f"Error calculating XIRR: {e}")
        calculated_xirr = None

    return calculated_xirr

def save_plan(trust_id, plan_id, calculated_xirr):
    sql = f"""
        INSERT INTO daily_rev_plans (trust_id, plan_id, plan_name, plan_xirr)
        VALUES ({trust_id}, {plan_id}, '方案{plan_id}', {calculated_xirr});
    """
    #print(sql)
    MysqlAdapter.commonRunsql(sql)


def process_sheets(params):
    time_script_start = datetime.now()

    delete_existing_data(params)

    trust_id = params["TrustID"]
    version = params.get("version", "")
    method_name = get_method_name(version)
    print("使用算法：", method_name)

    if "CurrentDate" in params.keys():
        current_date = datetime.strptime(params["CurrentDate"], "%Y-%m-%d")
    else:
        current_date = datetime.now().date()

    # 以10M现金为基准产生示例现金流
    AVAILABLE_CASH = 10000000

    sql = f"""
        SELECT cast(ItemValue as unsigned integer) 
        FROM Analysis_SolutionExtension 
        WHERE SolutionID = {trust_id} AND ItemCode = 'DiscountMethod' 
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    discount_method = res[0][0] if res and res[0][0] else 0

    # 获取待循环的plan_id
    sql = f"""
        SELECT plan_id, asset_category, distribution_name, bucket_left, bucket_right, percentage
        FROM daily_rev_plan_distributions
        WHERE trust_id = {trust_id}
    """
    #print(sql)
    df_daily_rev_plans = MysqlAdapter.exec_sql_to_df(sql)


    # 获取分组定义
    grouping_configs = get_asset_grouping_config(trust_id)
    
    # 打印配置结构，帮助调试
    print("\n=== 检查配置结构 ===")
    
    # 检查asset_category=1的配置
    if 1 in grouping_configs:
        print(f"资产类别1配置: {type(grouping_configs[1])}")
        if "CreditRating" in grouping_configs[1]:
            cr_config = grouping_configs[1]["CreditRating"]
            print(f"CreditRating配置类型: {type(cr_config)}")
            print(f"CreditRating首项类型: {type(cr_config[0])}")
            print(f"CreditRating首项内容: {cr_config[0]}")
    
    print("\n=== 获取资产分组的利率 ===")
    # dimension1 = AssetSubCategory, dimension2 = CreditRating, dimension3 = LoanTerm, dimension4 = Seasoning
    sql = f"""
        select asset_category
        , dimension1_right
        , dimension2_right
        , dimension3_right
        , sum(interest_rate * percentage) / sum(percentage) as interest_rate
        from Analysis_AssetGroups
        where trust_id = {trust_id}
        group by asset_category, dimension1_right, dimension2_right, dimension3_right;
    """
    print(sql)
    df_asset_groups_interest_rates = MysqlAdapter.exec_sql_to_df(sql)

    if "AverageDays" in params:
        average_days = int(params["AverageDays"])
        df_avg_interest_rates = get_average_asset_groups(trust_id, average_days, current_date)
        if df_avg_interest_rates is not None and not df_avg_interest_rates.empty:
            df_asset_groups_interest_rates = df_avg_interest_rates
    
    # 分组匹配缓存
    asset_group_cache = {}

    time_x_1 = datetime.now()
    sql = """
        select loan_term, interest_rate, remaining_term, remaining_percent
        from pi_loan_cashflows;
    """
    df_pi_loan_cashflows = MysqlAdapter.exec_sql_to_df(sql)
    time_x_2 = datetime.now()
    print("查询pi_loan_cashflows用时", (time_x_2 - time_x_1).total_seconds(), "秒")

    if df_pi_loan_cashflows.empty:
        raise Exception("pi_loan_cashflows表为空")

    # 循环处理每个plan_id
    # 每个plan有如下的资产分布
    """
        CREATE TABLE `daily_rev_plan_distributions` (
            `trust_id` int NOT NULL,
            `plan_id` int NOT NULL,
            `asset_category` int NOT NULL DEFAULT '0' COMMENT '资产一级分类： 微贷 1:小额 2:中额 月付 1:非分期 2:分期',
            `distribution_name` varchar(50) NOT NULL,
            `bucket_left` decimal(15,6) NOT NULL,
            `bucket_right` decimal(15,6) NOT NULL,
            `percentage` decimal(15,6) NOT NULL,
            PRIMARY KEY (`trust_id`,`plan_id`,`asset_category`,`distribution_name`,`bucket_left`)
        ) ENGINE=InnoDB   
        需要遍历 asset_category 分别生成模拟资产
    """
    print("开始生成", len(df_daily_rev_plans["plan_id"].unique()), "个方案的资产和现金流")

    for plan_id in df_daily_rev_plans["plan_id"].unique():
        
        time_plan_start = datetime.now()

        random.seed(RANDOM_SEED)

        df_plan_distributions = df_daily_rev_plans[df_daily_rev_plans["plan_id"] == plan_id]

        # daily_rev_plan_distributions表中 asset_category = 0 的记录用于保存一级分类的占比
        df_asset_categories = df_plan_distributions[
            (df_plan_distributions["asset_category"] == 0) & (df_plan_distributions["distribution_name"] == "AssetCategory")
        ]

        category_loans_df = pd.DataFrame()
        category_cashflows_df = pd.DataFrame()

        # 遍历一级分类（目前对于微贷和月付都是两种一级分类），取 bucket_right 和 percentage 两个字段
        for category in df_asset_categories.iterrows():
            # 获取该行记录的bucket_right 和 percentage
            asset_category = int(category[1]["bucket_right"])
            percentage = category[1]["percentage"]
            available_cash_for_asset_category = AVAILABLE_CASH * percentage

            print("针对资产一级分类", asset_category, "生成余额为", available_cash_for_asset_category, "的资产")

            time3 = datetime.now()
            df_distributions_for_asset_category = df_plan_distributions[
                (df_plan_distributions["asset_category"] == asset_category)
            ].drop(columns=["asset_category"])

            asset_subcategory_buckets = process_distribution_data(df_distributions_for_asset_category, "AssetSubCategory")
            loan_term_buckets = process_distribution_data(df_distributions_for_asset_category, "LoanTerm")
            remaining_term_buckets = process_distribution_data(df_distributions_for_asset_category, "RemainingTerm")
            credit_rating_buckets = process_distribution_data(df_distributions_for_asset_category, "CreditRating")

            # 针对此种一级分类资产的分布，生成资产
            # 根据method_name动态调用相应的生成资产函数
            asset_generator = globals()[method_name]
            loans_df, cashflows_df, num_of_loans = asset_generator(
                asset_category,
                asset_subcategory_buckets,
                loan_term_buckets,
                remaining_term_buckets,
                credit_rating_buckets,
                available_cash_for_asset_category,
                current_date,
                AVAILABLE_CASH / NUMBER_OF_LOANS,
                df_asset_groups_interest_rates,
                grouping_configs,
                discount_method,
                asset_group_cache
            )

            category_loans_df = pd.concat([category_loans_df, loans_df])
            category_cashflows_df = pd.concat([category_cashflows_df, cashflows_df])
            time4 = datetime.now()
            print("生成资产和现金流用时", (time4 - time3).total_seconds(), "秒")

        time5 = datetime.now()
        loans_grouped = save_loans(category_loans_df, df_pi_loan_cashflows, trust_id, plan_id)
        time6 = datetime.now()
        print("保存资产用时", (time6 - time5).total_seconds(), "秒")

        calculated_xirr = save_cashflows(category_cashflows_df, loans_grouped, trust_id, plan_id, current_date)
        time7 = datetime.now()
        print("保存现金流用时", (time7 - time6).total_seconds(), "秒")

        save_plan(trust_id, plan_id, calculated_xirr)

        calculate_generated_distributions(trust_id, plan_id, category_loans_df, df_plan_distributions)

        time_plan_finish = datetime.now()
        print(f"=== 方案 {plan_id} 用时", (time_plan_finish - time_plan_start).total_seconds(), "秒 ===")

        plan_id += 1  # Increment plan_id for the next sheet

    # 计算每个plan的principal和interest
    sql = f"""
        update daily_rev_plans p
        inner join (
            select plan_id, sum(principal) as principal, sum(interest) as interest
            from daily_rev_plan_cashflows_grouped
            where trust_id = {trust_id}
            group by plan_id
        ) c on p.plan_id = c.plan_id
        set p.plan_principal = c.principal, p.plan_interest = c.interest
        where p.trust_id = {trust_id};
    """
    MysqlAdapter.commonRunsql(sql)
    
    time_script_final = datetime.now()
    print("===== 程序总用时", (time_script_final - time_script_start).total_seconds(), "秒 =====")

    return "success"

def get_method_name(version):
    if version == "":
        return "generate_assets_from_distribution_stratified_adjusted"
    elif version == "v1":
        return "generate_assets_from_distribution_stratified_adjusted_v1"
    elif version == "v2":
        return "generate_assets_from_distribution_stratified_adjusted_v2"
    else:
        raise ValueError(f"错误的算法版本: {version}")

def process_plan(params):
    plan_id = int(params["plan_id"])
    trust_id = int(params["TrustID"])
    version = params.get("version", "")
    method_name = get_method_name(version)
    print("使用算法：", method_name)

    delete_existing_plan_data(trust_id, plan_id)

    if "CurrentDate" in params.keys():
        current_date = datetime.strptime(params["CurrentDate"], "%Y-%m-%d")
    else:
        current_date = datetime.now().date()

    # 以10M现金为基准产生示例现金流
    AVAILABLE_CASH = 10000000

    sql = f"""
        SELECT cast(ItemValue as unsigned integer) 
        FROM Analysis_SolutionExtension 
        WHERE SolutionID = {trust_id} AND ItemCode = 'DiscountMethod' 
    """
    res = MysqlAdapter.commonExecuteGetData(sql)
    discount_method = res[0][0] if res and res[0][0] else 0

    # 获取待循环的plan_id
    sql = f"""
        SELECT plan_id, asset_category, distribution_name, bucket_left, bucket_right, percentage
        FROM daily_rev_plan_distributions
        WHERE trust_id = {trust_id} and plan_id = {plan_id};
    """
    #print(sql)
    df_daily_rev_plans = MysqlAdapter.exec_sql_to_df(sql)

    # 获取分组定义
    grouping_configs = get_asset_grouping_config(trust_id)
    
    # 打印配置结构，帮助调试
    print("\n=== 检查配置结构 ===")
    
    # 检查asset_category=1的配置
    if 1 in grouping_configs:
        print(f"资产类别1配置: {type(grouping_configs[1])}")
        if "CreditRating" in grouping_configs[1]:
            cr_config = grouping_configs[1]["CreditRating"]
            print(f"CreditRating配置类型: {type(cr_config)}")
            print(f"CreditRating首项类型: {type(cr_config[0])}")
            print(f"CreditRating首项内容: {cr_config[0]}")
    
    # 获取资产分组的利率
    # dimension1 = AssetSubCategory, dimension2 = CreditRating, dimension3 = LoanTerm, dimension4 = Seasoning
    sql = f"""
        select asset_category
        , dimension1_right
        , dimension2_right
        , dimension3_right
        , sum(interest_rate * percentage) / sum(percentage) as interest_rate
        from Analysis_AssetGroups
        where trust_id = {trust_id}
        group by asset_category, dimension1_right, dimension2_right, dimension3_right;
    """
    df_asset_groups_interest_rates = MysqlAdapter.exec_sql_to_df(sql)

    if "AverageDays" in params:
        average_days = int(params["AverageDays"])
        df_asset_groups_interest_rates = get_average_asset_groups(trust_id, average_days)

    # 分组匹配缓存
    asset_group_cache = {}

    sql = """
        select loan_term, interest_rate, remaining_term, remaining_percent
        from pi_loan_cashflows;
    """
    df_pi_loan_cashflows = MysqlAdapter.exec_sql_to_df(sql)

    df_plan_distributions = df_daily_rev_plans[df_daily_rev_plans["plan_id"] == plan_id]

    # daily_rev_plan_distributions表中 asset_category = 0 的记录用于保存一级分类的占比
    df_asset_categories = df_plan_distributions[
        (df_plan_distributions["asset_category"] == 0) & (df_plan_distributions["distribution_name"] == "AssetCategory")
    ]

    category_loans_df = pd.DataFrame()
    category_cashflows_df = pd.DataFrame()

    plan_start_time = datetime.now()

    # 遍历一级分类（目前对于微贷和月付都是两种一级分类），取 bucket_right 和 percentage 两个字段
    for category in df_asset_categories.iterrows():
        # 获取该行记录的bucket_right 和 percentage
        asset_category = int(category[1]["bucket_right"])
        percentage = category[1]["percentage"]
        available_cash_for_asset_category = AVAILABLE_CASH * percentage

        df_distributions_for_asset_category = df_plan_distributions[
            (df_plan_distributions["asset_category"] == asset_category)
        ].drop(columns=["asset_category"])

        asset_subcategory_buckets = process_distribution_data(df_distributions_for_asset_category, "AssetSubCategory")
        loan_term_buckets = process_distribution_data(df_distributions_for_asset_category, "LoanTerm")
        remaining_term_buckets = process_distribution_data(df_distributions_for_asset_category, "RemainingTerm")
        credit_rating_buckets = process_distribution_data(df_distributions_for_asset_category, "CreditRating")

        # 针对此种一级分类资产的分布，生成资产
        # 根据method_name动态调用相应的生成资产函数
        asset_generator = globals()[method_name]
        loans_df, cashflows_df, num_of_loans = asset_generator(
            asset_category,
            asset_subcategory_buckets,
            loan_term_buckets,
            remaining_term_buckets,
            credit_rating_buckets,
            available_cash_for_asset_category,
            current_date,
            AVAILABLE_CASH / NUMBER_OF_LOANS,
            df_asset_groups_interest_rates,
            grouping_configs,
            discount_method,
            asset_group_cache
        )

        category_loans_df = pd.concat([category_loans_df, loans_df])
        category_cashflows_df = pd.concat([category_cashflows_df, cashflows_df])

    loans_grouped = save_loans(category_loans_df, df_pi_loan_cashflows, trust_id, plan_id)

    calculated_xirr = save_cashflows(category_cashflows_df, loans_grouped, trust_id, plan_id, current_date)

    save_plan(trust_id, plan_id, calculated_xirr)

    calculate_generated_distributions(trust_id, plan_id, category_loans_df, df_plan_distributions)

    # 计算每个plan的principal和interest
    sql = f"""
        update daily_rev_plans p
        inner join (
            select plan_id, sum(principal) as principal, sum(interest) as interest
            from daily_rev_plan_cashflows_grouped
            where trust_id = {trust_id}
            group by plan_id
        ) c on p.plan_id = c.plan_id
        set p.plan_principal = c.principal, p.plan_interest = c.interest
        where p.trust_id = {trust_id} and p.plan_id = {plan_id};
    """
    MysqlAdapter.commonRunsql(sql)

    end_time = datetime.now()
    print("===== 方案 {} 生成资产用时 {} 秒 =====".format(plan_id, (end_time - plan_start_time).total_seconds()))
    
    return "success"


def save_plan_loan_distributions(trust_id, plan_id, current_date):
    current_date = current_date.strftime("%Y-%m-%d")

    sql = f"""
        with temp_report_loans as (
            SELECT
            loan_id,
            amount,
            CASE
                WHEN interest_rate > 0 AND interest_rate <= 18 THEN '(0,18]'
                WHEN interest_rate > 18 AND interest_rate <= 21 THEN '(18,21]'
                WHEN interest_rate > 21 AND interest_rate <= 22 THEN '(21,22]'
                WHEN interest_rate > 22 AND interest_rate <= 23 THEN '(22,23]'
                WHEN interest_rate > 23 AND interest_rate <= 24 THEN '(23,24]'
                ELSE 'Other'
            END AS interest_rate_bucket,
            CASE
                WHEN remaining_term > 0 AND remaining_term <= 3 THEN '(0,3]'
                WHEN remaining_term > 3 AND remaining_term <= 6 THEN '(3,6]'
                WHEN remaining_term > 6 AND remaining_term <= 9 THEN '(6,9]'
                WHEN remaining_term > 9 AND remaining_term <= 12 THEN '(9,12]'
                WHEN remaining_term > 12 AND remaining_term <= 24 THEN '(12,24]'
                ELSE 'Other'
            END AS remaining_term_bucket,
            CASE
                WHEN credit_rating >= 0 AND credit_rating <= 1 THEN '(0,1]'
                WHEN credit_rating > 1 AND credit_rating <= 2 THEN '(1,2]'
                WHEN credit_rating > 2 AND credit_rating <= 3 THEN '(2,3]'
                WHEN credit_rating > 3 AND credit_rating <= 4 THEN '(3,4]'
                WHEN credit_rating > 4 AND credit_rating <= 5 THEN '(4,5]'
                ELSE 'Other'
            END AS credit_rating_bucket
            FROM daily_rev_plan_loans l
            where l.trust_id = {trust_id} and l.plan_id = {plan_id}
        )

        insert into trust_revolving_purchase_distributions (trust_id, date, plan_id, interest_rate_bucket, remaining_term_bucket, credit_rating_bucket, principal_balance)
        select {trust_id}
        , '{current_date}' as date
        , {plan_id} as plan_id
        , interest_rate_bucket
        , remaining_term_bucket
        , credit_rating_bucket
        , sum(amount) as principal_balance
        from temp_report_loans l
        group by interest_rate_bucket, remaining_term_bucket, credit_rating_bucket;
    """
    with dbh.open_dbconn("TaskProcess") as conn:
        dbh.commonRunsql(sql, dbcn=conn)


def insert_distribution_data(trust_id, plan_id, asset_category, distribution_name, buckets):
    try:
        insert_query = """
        INSERT INTO daily_rev_plan_distributions (trust_id, plan_id, asset_category, distribution_name, bucket_left, bucket_right, percentage)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        dbcn_taskprocess = dbh.open_dbconn("TaskProcess")
        for bucket, percentage in buckets.items():
            bucket_left, bucket_right = bucket
            # print(distribution_name, bucket_left, bucket_right, percentage)
            dbh.exec_commit(
                insert_query,
                dbcn=dbcn_taskprocess,
                parameters=(
                    trust_id,
                    plan_id,
                    asset_category,
                    distribution_name,
                    bucket_left,
                    bucket_right,
                    percentage,
                ),
            )

    except Exception as error:
        print("Error inserting into daily_rev_plan_distributions:", error)


def insert_distribution_comparison_data(trust_id, plan_id, distribution_type, data):
    try:
        insert_query = """
            INSERT INTO daily_rev_plan_distribution_comparison (trust_id, plan_id, distribution_type, bucket, generated_percentage, specified_percentage)
            VALUES (%s, %s, %s, %s, %s, %s)
        """

        dbcn_taskprocess = dbh.open_dbconn("TaskProcess")
        for item in data:
            bucket = item["bucket"].replace(")", "]")
            generated = item["generated"]
            specified = item["specified"]
            dbh.exec_commit(
                insert_query,
                dbcn=dbcn_taskprocess,
                parameters=(
                    trust_id,
                    plan_id,
                    distribution_type,
                    bucket,
                    generated,
                    specified,
                ),
            )

    except Exception as error:
        print("Error inserting into daily_rev_plan_distribution_comparison:", error)


# Helper function to pick a value from a bucket
def pick_from_bucket(bucket):
    return random.uniform(bucket[0] + 0.0001, bucket[1])


# Function to generate a random date within the specified range
def random_date(start_date, end_date):
    time_between_dates = end_date - start_date
    random_number_of_days = random.randrange(time_between_dates.days)
    return start_date + timedelta(days=random_number_of_days)


# Function to calculate monthly payment
def calculate_monthly_payment(principal, annual_rate, term_months):
    if annual_rate == 0:
        return principal / term_months
    else:
        monthly_rate = annual_rate / 12 / 100
        # print("monthly_rate=", monthly_rate, "term_months=", term_months)
        return (
            principal
            * (monthly_rate * (1 + monthly_rate) ** term_months)
            / ((1 + monthly_rate) ** term_months - 1)
        )


# Function to create and format comparison DataFrame
def create_formatted_comparison_dataframe(calculated, specified):
    data = []
    for bucket, spec_value in specified.items():
        calc_value = calculated.get(bucket, 0)
        # data.append({
        #     "Bucket": f"{bucket}",
        #     "Calculated Distribution (%)": calc_value * 100,
        #     "Specified Distribution (%)": spec_value * 100
        # })
        data.append(
            {
                "bucket": f"{bucket}",
                "generated": calc_value * 100,
                "specified": spec_value * 100,
            }
        )
    return data
    # df = pd.DataFrame(data)
    # return df.style.format({
    #     "Calculated Distribution (%)": "{:.2f}%",
    #     "Specified Distribution (%)": "{:.2f}%"
    # }).bar(subset=["Calculated Distribution (%)", "Specified Distribution (%)"], color='#5fba7d')

def get_average_asset_groups(trust_id, average_days, current_date):
    sql = f"""
        SELECT archive_date
        , asset_category
        , dimension_1_right as dimension1_right
        , dimension_2_right as dimension2_right
        , dimension_3_right as dimension3_right
        , sum(interest_rate * percentage) / sum(percentage) as interest_rate
        FROM archive_asset_groups
        where trust_id = {trust_id}
        group by archive_date, asset_category, dimension1_right, dimension2_right, dimension3_right;
    """
    df_avg_source = MysqlAdapter.exec_sql_to_df(sql)
    if df_avg_source.empty:
        return None

    # archive_date 有可能是str，需要转换成datetime
    df_avg_source['archive_date'] = pd.to_datetime(df_avg_source['archive_date'])
    max_archive_date = df_avg_source['archive_date'].max()

    # 如果max_archive_date >= current_date，这应该是一个历史数据验证场景
    if max_archive_date >= current_date:
        print("max_archive_date >= current_date")
        average_start_date = current_date - pd.Timedelta(days=average_days-1)
        df_avg_source = df_avg_source[(df_avg_source['archive_date'] >= average_start_date) & (df_avg_source['archive_date'] <= current_date)]
    else:
        print("current_date不在存档表中")
        average_start_date = max_archive_date - pd.Timedelta(days=average_days-2)

        df_avg_source = df_avg_source[df_avg_source['archive_date'] >= average_start_date]

        print("已选取archive_date >= {} 的数据".format(average_start_date.strftime('%Y-%m-%d')))

        sql = f"""
            select '2099-12-31' as archive_date
            , asset_category
            , dimension1_right
            , dimension2_right
            , dimension3_right
            , sum(interest_rate * percentage) / sum(percentage) as interest_rate
            from Analysis_AssetGroups
            where trust_id = {trust_id}
            group by asset_category, dimension1_right, dimension2_right, dimension3_right;
        """
        print(sql)
        df_current = MysqlAdapter.exec_sql_to_df(sql)
        df_current['archive_date'] = pd.to_datetime(df_current['archive_date'])
        
        # append df_current to df_avg_source
        df_avg_source = pd.concat([df_avg_source, df_current], ignore_index=True)
        print("已添加当前数据")
    
    distinct_archive_dates = df_avg_source['archive_date'].unique()
    print(f"找到 {len(distinct_archive_dates)} 日利率数据")
    
    # 按资产类别和维度分组计算所有日期的平均利率
    df_avg = df_avg_source.groupby(['asset_category', 'dimension1_right', 'dimension2_right', 'dimension3_right'])['interest_rate'].mean().reset_index()
    return df_avg

if __name__ == "__main__":
    try:
        str_arg = r""""""
        if len(sys.argv) >= 2 and sys.argv[1] is not None:
            str_arg = sys.argv[1]

        arg = urllib.parse.unquote_plus(str_arg)
        arg = json.loads(arg)
        if 'Params' not in arg:
            params = arg
        else:
            arg = json.dumps(arg['Params'])
            params = SQLParamsParser.getParmas(arg)

        if "plan_id" not in params:
            result = process_sheets(params)
        else:
            result = process_plan(params)

        # docker exec -it naughty_feynman bash -c "cd /opt/meituan/abs-quickdeal/data/app/TSSWCFServices/services-biz-python-script && bash"
        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 load_revolving_distributions_auto_mixed_new.py '{"TrustID": "88", "CurrentDate": "2025-01-07", "plan_id": 1}'
        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 load_revolving_distributions_auto_mixed_new.py '{"TrustID": "88", "CurrentDate": "2025-01-07", "plan_id": 1, "version": "v2"}'
        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 load_revolving_distributions_auto_mixed_new.py '{"TrustID": "88", "CurrentDate": "2025-04-13", "version": "v1", "AverageDays": 30}'

        print("$OUTPUT" + result)
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
