/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.mapper;

import cn.goldenstand.services.entity.SolutionRevolvingPlan;
import cn.goldenstand.services.entity.drb.MobPriceSummaryDTO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */

public interface SolutionRevolvingPlanMapper extends BaseMapper<SolutionRevolvingPlan> {

    List<Map<String, Object>> getStructurePeriodsData(String trustId);

    List<Map<String, Object>> getPaymentTypeOptions();

    List<Map<String, Object>> getTemplateOptions();

    List<Map<String, Object>> getSolutionSettingCmb(String trustId);

    List<Map<String, Object>> getConditionOptions();

    List<Map<String, Object>> getScenarios(String trustId);

    List<Map<String, Object>> getContainersOne(String trustId);

    List<Map<String, Object>> getContainersTwo(String trustId);

    List<Map<String, Object>> getExtSetting(String trustId);

    Boolean IsTopUpAvailable(String trustId);

    List<Map<String, Object>> getPaymentSequenceItems(String trustId);

    List<Map<String, Object>> getEventItems(String trustId);

    List<Map<String, Object>> getStructurePremiumSettings(String trustId);

    String getTranche(String trustId);

    List<Map<String, Object>> getBondNames(String trustId);

    List<Map<String, Object>> getBondNamesOther(String trustId);

    List<Map<String, Object>> getTrustBondExtraBpSettings(String trustId);

    List<Map<String, Object>> getBondsFloatBPDatas(String trustId);

    List<Map<String, Object>> getTrustFloatDatas(String trustId);

    List<Map<String, Object>> getSolutionSetting(String trustId);

    /**
     * 获取MOB价格汇总数据
     *
     * @param trustId 产品ID
     * @param sessionId 会话ID
     * @param scenarioId 情景ID
     * @return MOB价格汇总数据
     */
    List<MobPriceSummaryDTO> getMobPriceSummary(@Param("trustId") Long trustId,
                                               @Param("sessionId") String sessionId,
                                               @Param("scenarioId") Long scenarioId);
}
