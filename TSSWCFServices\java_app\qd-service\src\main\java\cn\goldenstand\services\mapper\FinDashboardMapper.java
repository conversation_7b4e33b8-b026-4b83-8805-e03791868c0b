/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.mapper;

import cn.goldenstand.services.entity.drb.BondBalanceDTO;
import cn.goldenstand.services.entity.drb.RemainingPrincipalDTO;
import cn.goldenstand.services.entity.drb.TrustMetricDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface FinDashboardMapper {

    /**
     * 获取报告日期列表
     *
     * @return 报告日期列表
     */
    List<String> getReportingDates();

    /**
     * 获取券端余额信息
     *
     * @param page 分页参数
     * @param trustName 信托名称
     * @param trustCode 信托代码
     * @param reportingDate 报告日期
     * @param productType 产品类型
     * @param category 类别（月付/微贷）
     * @param sortField 排序字段
     * @param sortOrder 排序方向（asc/desc）
     * @return 券端余额信息
     */
    IPage<BondBalanceDTO> getBondBalancePage(Page<BondBalanceDTO> page,
                                            @Param("trustName") String trustName,
                                            @Param("trustCode") String trustCode,
                                            @Param("reportingDate") String reportingDate,
                                            @Param("productType") String productType,
                                            @Param("category") String category,
                                            @Param("sortField") String sortField,
                                            @Param("sortOrder") String sortOrder);

    /**
     * 根据报告日期获取各个层级剩余本金
     *
     * @param reportingDate 报告日期
     * @return 各个层级剩余本金
     */
    List<RemainingPrincipalDTO> getBalanceByReportingDate(@Param("reportingDate") String reportingDate);

    /**
     * 根据报告日期获取券端自持剩余本金
     *
     * @param reportingDate 报告日期
     * @return 券端自持剩余本金
     */
    List<Map<String, Object>> getSelfHoldingCpbByReportingDate(@Param("reportingDate") String reportingDate);

    /**
     * 根据报告日期获取融资项目数量总览
     *
     * @param reportingDate 报告日期
     * @return 融资项目数量总览
     */
    List<Map<String, Object>> getTrustCountByReportingDate(@Param("reportingDate") String reportingDate);

    /**
     * 获取资产端剩余本金信息
     *
     * @param page 分页参数
     * @param trustName 信托名称
     * @param trustCode 信托代码
     * @param reportingDate 报告日期
     * @param productType 产品类型
     * @param category 类别（月付/微贷）
     * @param sortField 排序字段
     * @param sortOrder 排序方向（asc/desc）
     * @return 资产端剩余本金信息
     */
    IPage<Map<String, Object>> getAssetRemainingPrincipal(Page<Map<String, Object>> page,
                                            @Param("trustName") String trustName,
                                            @Param("trustCode") String trustCode,
                                            @Param("reportingDate") String reportingDate,
                                            @Param("productType") String productType,
                                            @Param("category") String category,
                                            @Param("sortField") String sortField,
                                            @Param("sortOrder") String sortOrder);

    /**
     * 获取存续产品指标信息
     *
     * @param page 分页参数
     * @param trustName 信托名称
     * @param trustCode 信托代码
     * @param reportingDate 报告日期
     * @param productNo 产品编号
     * @param category 类别（月付/微贷）
     * @param sortField 排序字段
     * @param sortOrder 排序方向（asc/desc）
     * @return 存续产品指标信息
     */
    IPage<TrustMetricDTO> getTrustMetricPage(Page<TrustMetricDTO> page,
                                            @Param("trustName") String trustName,
                                            @Param("trustCode") String trustCode,
                                            @Param("reportingDate") String reportingDate,
                                            @Param("productNo") String productNo,
                                            @Param("category") String category,
                                            @Param("sortField") String sortField,
                                            @Param("sortOrder") String sortOrder);
}
