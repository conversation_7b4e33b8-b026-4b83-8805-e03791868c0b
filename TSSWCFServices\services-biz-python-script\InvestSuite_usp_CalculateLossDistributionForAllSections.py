import sys
import urllib.parse
import traceback
from PythonFiles import MysqlAdapter2Java as MysqlAdapter, SQLParamsParser
from InvestSuite_usp_StaticPool_GenerateCurves import (
    func as InvestSuite_usp_StaticPool_GenerateCurves,
)

def func(params):
    with_regression = params.get("WithRegression", False)
    default_rate_regression_results = params.get("DefaultRateRegressionResults", None)
    prepayment_rate_regression_results = params.get("PrepaymentRateRegressionResults", None)

    super_group_id = 0
    if "SuperGroupID" in params.keys():
        super_group_id = params["SuperGroupID"]

    sql = """
        select Section from (
            select Section, sum(ArrearsRate) as V
            from dbo_StaticPoolProcessedData
            where StaticPoolID = @StaticPoolID
            group by Section
        ) x
        union
        select '[M0]' as Section
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    tuple_section = MysqlAdapter.commonExecuteGetData(sql)

    params["Organisation"] = ""
    params["AssetType"] = ""
    params["Name"] = ""

    sql = """
        select ifnull(max(AttributeValue),'') from dbo_EntityAttributes where EntityID=@StaticPoolID and AttributeName = 'Organisation'
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["Organisation"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select ifnull(max(AttributeValue),'') from dbo_EntityAttributes where EntityID=@StaticPoolID and AttributeName = 'AssetType'
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["AssetType"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    sql = """
        select ifnull(max(AttributeValue),'') from dbo_EntityAttributes where EntityID=@StaticPoolID and AttributeName = 'Name'
    """
    sql = MysqlAdapter.prepareSql(sql, params)
    params["Name"] = MysqlAdapter.commonExecuteGetData(sql)[0][0]

    params["CurveGroupNameBase"] = "静态池曲线组-("
    params["vCurveGroupName"] = ""
    if params["Name"] != "":
        params["CurveGroupNameBase"] = params["CurveGroupNameBase"] + params["Name"]
    if params["Organisation"] != "":
        params["CurveGroupNameBase"] = (
            params["CurveGroupNameBase"] + "_" + params["Organisation"]
        )
    if params["AssetType"] != "":
        params["CurveGroupNameBase"] = (
            params["CurveGroupNameBase"] + "_" + params["AssetType"]
        )

    for section in tuple_section:
        section_name = section[0]
        #print("计算", section_name, "分段的损失分布和压力曲线")
        InvestSuite_usp_StaticPool_GenerateCurves(
            params={
                "StaticPoolID": params["StaticPoolID"],
                "Section": section_name,
                "SelectedDates": "",
                "SuperGroupID": super_group_id,
                "WithRegression": with_regression,
                "DefaultRateRegressionResults": default_rate_regression_results,
                "PrepaymentRateRegressionResults": prepayment_rate_regression_results
            }
        )

    return ""


if __name__ == "__main__":
    try:

        arg = urllib.parse.unquote_plus(sys.argv[1])
        params = SQLParamsParser.getParmas(arg)
        # cmdline: /opt/meituan/abs-quickdeal/data/app/Tools/python3/bin/python3 InvestSuite_usp_CalculateLossDistributionForAllSections.py '{"StaticPoolID": "1500", "Section":"[ArrearsRate1_30]","SelectedDates":""}

        result = func(params)
        
        print("$OUTPUT" + result)
    except Exception as e:
        print("$ERROR", e, traceback.format_exc())
