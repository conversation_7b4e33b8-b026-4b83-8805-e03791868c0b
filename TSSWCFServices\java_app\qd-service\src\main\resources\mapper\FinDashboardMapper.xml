<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.goldenstand.services.mapper.FinDashboardMapper">
    <select id="getReportingDates" resultType="java.lang.String">
        SELECT DISTINCT DATE_FORMAT(reporting_date, '%Y-%m-%d') as reporting_date
        FROM daily_bond_balance
    </select>

    <select id="getBondBalancePage" resultType="cn.goldenstand.services.entity.drb.BondBalanceDTO">
        SELECT
            trust_id as trustId,
            trust_code as trustCode,
            trust_name as trustName,
            case when product_no like '%月付%' then '月付' else '微贷' end as category,
            case when product_no like '生活费%' then '生活费'
                when product_no like '生意贷%' then '生意贷'
                else product_no end as productType,
            DATE_FORMAT(data_date, '%Y-%m-%d') as dataDate,
            term as term,
            CONCAT('[', DATE_FORMAT(start_date, '%Y-%m-%d'), ', ', DATE_FORMAT(end_date, '%Y-%m-%d'), ']') as dateRange,
            origin_amount as originAmount,
            actual_paid as actualPaid,
            estimated_cpb as estimatedCpb
        FROM daily_bond_balance
        <where>
            product_no not like '%月付%'
            <if test="trustName != null and trustName != ''">
                AND trust_name LIKE CONCAT('%', #{trustName}, '%')
            </if>
            <if test="trustCode != null and trustCode != ''">
                AND trust_code LIKE CONCAT('%', #{trustCode}, '%')
            </if>
            <if test="reportingDate != null and reportingDate != ''">
                AND reporting_date = #{reportingDate}
            </if>
            <if test="productType != null and productType != ''">
                AND product_no LIKE CONCAT('%', #{productType}, '%')
            </if>
            <if test="category != null and category != ''">
                <choose>
                    <when test="category == '月付'">
                        AND product_no LIKE '%月付%'
                    </when>
                    <when test="category == '微贷'">
                        AND product_no NOT LIKE '%月付%'
                    </when>
                </choose>
            </if>
        </where>
        <if test="sortField != null and sortField != ''">
            <choose>
                <when test="sortField == 'trustName'">
                    ORDER BY trust_name
                </when>
                <when test="sortField == 'trustCode'">
                    ORDER BY trust_code
                </when>
                <when test="sortField == 'productType'">
                    ORDER BY product_no
                </when>
                <when test="sortField == 'dataDate'">
                    ORDER BY data_date
                </when>
                <when test="sortField == 'term'">
                    ORDER BY term
                </when>
                <when test="sortField == 'originAmount'">
                    ORDER BY origin_amount
                </when>
                <when test="sortField == 'actualPaid'">
                    ORDER BY actual_paid
                </when>
                <when test="sortField == 'estimatedCpb'">
                    ORDER BY estimated_cpb
                </when>
                <otherwise>
                    ORDER BY trust_id
                </otherwise>
            </choose>
            <if test="sortOrder != null and sortOrder != ''">
                <choose>
                    <when test="sortOrder == 'desc'">
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="sortField == null or sortField == ''">
            ORDER BY trust_id DESC
        </if>
    </select>

    <select id="getBalanceByReportingDate" resultType="cn.goldenstand.services.entity.drb.RemainingPrincipalDTO">
        with cpb as (
            select '资产端' as `type`, 'product_no' as aggregation_level,
                product_no, sum(COALESCE(cpb, 0)) as cpb,
                case when product_no like '%月付%' then '月付' else '微贷' end as category,
                case when product_no like '生意贷%' then '生意贷'
                    when product_no like '生活费%' then '生活费'
                    else product_no end as product_type
            from trust_distribution as d
            where reporting_date = DATE_ADD(#{reportingDate}, INTERVAL -1 DAY)
                and aggregation_level = 'trust_product' and product_no != '月付'
                and exists (
                    select 1 from daily_bond_balance as b
                    where b.reporting_date = #{reportingDate} and b.trust_code = d.trust_code
                )
            group by product_no
            UNION ALL
            select '券端' as `type`, 'product_no' as aggregation_level,
                product_no, sum(COALESCE(estimated_cpb, 0)) as cpb,
                case when product_no like '%月付%' then '月付' else '微贷' end as category,
                case when product_no like '生意贷%' then '生意贷'
                    when product_no like '生活费%' then '生活费'
                    else product_no end as product_type
            from daily_bond_balance as b
            where reporting_date = #{reportingDate} and product_no != '月付'
            group by product_no
            union all
            select '资产端' as `type`, 'product_no' as aggregation_level, '未分期' as product_no, 0 as cpb,
                '月付' as category, '未分期' as product_type
            union all
            select '资产端' as `type`, 'product_no' as aggregation_level, '分期' as product_no, 0 as cpb,
                '月付' as category, '分期' as product_type
            union all
            select '券端' as `type`, 'product_no' as aggregation_level, '未分期' as product_no, 0 as cpb,
            '月付' as category, '未分期' as product_type
            union all
            select '券端' as `type`, 'product_no' as aggregation_level, '分期' as product_no, 0 as cpb,
            '月付' as category, '分期' as product_type
        )
        select `type`, aggregation_level, category, product_type as productType, product_no as productNo, cpb from cpb
        union all
        select `type`, 'product_type' as aggregation_level, category, product_type as productType,
            null as productNo, sum(cpb) as cpb
        from cpb group by `type`, category, product_type
        union all
        select `type`, 'category' as aggregation_level, category, null as productType,
            null as productNo, sum(cpb) as cpb
        from cpb group by `type`, category
        union all
        select `type`, 'type' as aggregation_level, null as category, null as productType,
            null as productNo, sum(cpb) as cpb
        from cpb group by `type`
    </select>

    <select id="getSelfHoldingCpbByReportingDate" resultType="java.util.Map">
        with cte as (
            select round(self_holding_cpb, 2) as self_holding_cpb,
                case when product_no like '生活费%' then '微贷生活费'
                    when product_no like '生意贷%' then '微贷生意贷'
                    else product_no end as product_type
            from daily_bond_balance
            where reporting_date = #{reportingDate} and product_no != '月付'
            union all
            select 0 as self_holding_cpb, '月付未分期' as product_type
            union all
            select 0 as self_holding_cpb, '月付分期' as product_type
        )
        select product_type, sum(COALESCE(self_holding_cpb, 0)) as self_holding_cpb
        from cte
        group by product_type
    </select>

    <select id="getTrustCountByReportingDate" resultType="java.util.Map">
        SELECT title, parent, description, count
        FROM daily_trust_count
        WHERE reporting_date = #{reportingDate}
    </select>

    <select id="getAssetRemainingPrincipal" resultType="java.util.Map">
        with trust as (
            select t.TrustId as trust_id, t.TrustCode as trust_code, t.TrustName as trust_name,
                case when te.ItemValue like '%00010%' then '月付' else '微贷' end as category,
                case when te.ItemValue like '%00007%' or te.ItemValue like '%00017%' then '生意贷'
                    when te.ItemValue like '%00001%' or te.ItemValue like '%00027%' then '生活费'
                    when te.ItemValue like '%00010%' then '月付'
                    else te.ItemValue end as product_type
            from TrustManagement_Trust as t
            inner join Analysis_Trust as a on t.TrustId = a.TrustId
            inner join TrustManagement_TrustInfoExtension as te on t.TrustId = te.TrustId and te.ItemCode = 'ProductNo'
            where exists (
                select 1 from daily_bond_balance as b
                where b.reporting_date = DATE_ADD(#{reportingDate}, INTERVAL 1 DAY) and b.trust_code = t.TrustCode
            )
        )
        select t.trust_id as trustId, t.trust_code as trustCode, t.trust_name as trustName,
               t.category, t.product_type as productType, d.cpb
        from trust_distribution as d
        inner join trust as t on d.trust_code = t.trust_code
        <where>
            d.reporting_date = #{reportingDate} and d.aggregation_level = 'trust' and t.category = '微贷'
            <if test="trustName != null and trustName != ''">
                AND t.trust_name LIKE CONCAT('%', #{trustName}, '%')
            </if>
            <if test="trustCode != null and trustCode != ''">
                AND t.trust_code LIKE CONCAT('%', #{trustCode}, '%')
            </if>
            <if test="productType != null and productType != ''">
                AND t.product_type LIKE CONCAT('%', #{productType}, '%')
            </if>
            <if test="category != null and category != ''">
                <choose>
                    <when test="category == '月付'">
                        AND t.category = '月付'
                    </when>
                    <when test="category == '微贷'">
                        AND t.category = '微贷'
                    </when>
                </choose>
            </if>
        </where>
        <if test="sortField != null and sortField != ''">
            <choose>
                <when test="sortField == 'trustName'">
                    ORDER BY t.trust_name
                </when>
                <when test="sortField == 'trustCode'">
                    ORDER BY t.trust_code
                </when>
                <when test="sortField == 'productType'">
                    ORDER BY t.product_type
                </when>
                <when test="sortField == 'category'">
                    ORDER BY t.category
                </when>
                <when test="sortField == 'cpb'">
                    ORDER BY d.cpb
                </when>
                <otherwise>
                    ORDER BY t.trust_code
                </otherwise>
            </choose>
            <if test="sortOrder != null and sortOrder != ''">
                <choose>
                    <when test="sortOrder == 'desc'">
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="sortField == null or sortField == ''">
            ORDER BY t.trust_id ASC
        </if>
    </select>

    <select id="getTrustMetricPage" resultType="cn.goldenstand.services.entity.drb.TrustMetricDTO">
        SELECT
            trust_id as trustId,
            trust_code as trustCode,
            trust_name as trustName,
            product_no as productNo,
            category as category,
            var_comp_ratio as varCompRatio,
            var_comp_ratio_incl_excess as varCompRatioInclExcess,
            `90d_plus_npl_rate` as nplRate90dPlus,
            apr_mob15 as aprMob15,
            irr as irr,
            actual_interest_rate as actualInterestRate,
            backend_revenue as backendRevenue,
            redemption_gap as redemptionGap
        FROM daily_trust_metric
        <where>
            <if test="trustName != null and trustName != ''">
                AND trust_name LIKE CONCAT('%', #{trustName}, '%')
            </if>
            <if test="trustCode != null and trustCode != ''">
                AND trust_code LIKE CONCAT('%', #{trustCode}, '%')
            </if>
            <if test="reportingDate != null and reportingDate != ''">
                AND reporting_date = #{reportingDate}
            </if>
            <if test="productNo != null and productNo != ''">
                AND product_no LIKE CONCAT('%', #{productNo}, '%')
            </if>
            <if test="category != null and category != ''">
                AND category = #{category}
            </if>
        </where>
        <if test="sortField != null and sortField != ''">
            <choose>
                <when test="sortField == 'trustName'">
                    ORDER BY trust_name
                </when>
                <when test="sortField == 'trustCode'">
                    ORDER BY trust_code
                </when>
                <when test="sortField == 'productNo'">
                    ORDER BY product_no
                </when>
                <when test="sortField == 'category'">
                    ORDER BY category
                </when>
                <when test="sortField == 'varCompRatio'">
                    ORDER BY var_comp_ratio
                </when>
                <when test="sortField == 'varCompRatioInclExcess'">
                    ORDER BY var_comp_ratio_incl_excess
                </when>
                <when test="sortField == 'nplRate90dPlus'">
                    ORDER BY `90d_plus_npl_rate`
                </when>
                <when test="sortField == 'aprMob15'">
                    ORDER BY apr_mob15
                </when>
                <when test="sortField == 'irr'">
                    ORDER BY irr
                </when>
                <when test="sortField == 'actualInterestRate'">
                    ORDER BY actual_interest_rate
                </when>
                <when test="sortField == 'backendRevenue'">
                    ORDER BY backend_revenue
                </when>
                <when test="sortField == 'redemptionGap'">
                    ORDER BY redemption_gap
                </when>
                <otherwise>
                    ORDER BY trust_id
                </otherwise>
            </choose>
            <if test="sortOrder != null and sortOrder != ''">
                <choose>
                    <when test="sortOrder == 'desc'">
                        DESC
                    </when>
                    <otherwise>
                        ASC
                    </otherwise>
                </choose>
            </if>
        </if>
        <if test="sortField == null or sortField == ''">
            ORDER BY trust_id DESC
        </if>
    </select>
</mapper>
