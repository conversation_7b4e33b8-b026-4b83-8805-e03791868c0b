import os

from PythonFiles import MysqlAdapter2Java as MysqlAdapter
from PythonFiles.logger_config import Logger

script_name = os.path.basename(__file__)
logger_instance = Logger()
log = logger_instance.get_logger(script_name)

def get_session_id_scenario_zero(trust_id, archive_date=None):
    if archive_date is not None:
        sql = f'''
            select task_session_id as session_id from archive_product_design_analysis_result
            where trust_id = {trust_id} and scenario_id = 0 and archive_date = '{archive_date}'
        '''
        result = MysqlAdapter.commonExecuteGetData(sql)
        session_id = result[0][0] if result else None
    else:
        sql = f'''
            select task_session_id as session_id from product_design_risk_transfer_report
            where trust_id = {trust_id} and scenario_id = 0
        '''
        result = MysqlAdapter.commonExecuteGetData(sql)
        session_id = result[0][0] if result else None
    return session_id

def get_standard_session_id(trust_id, revolving, archive_date=None):
    """
    获取标准情景的session_id
    
    参数:
        trust_id (int): 产品ID
        revolving (str): 是否循环，可选值为 '循环' 或 '静态'
        archive_date (str, optional): 归档日期，格式为 'YYYY-MM-DD'。如果为None，则查询当前数据
        
    返回:
        str: 标准情景的session_id，如果未找到则返回None
    """
    try:
        # 获取浮动服务费率
        sql = f'''
            SELECT ItemValue FROM TrustManagement_TrustFee 
            WHERE TrustID = {trust_id} AND TrustFeeDisplayName = '浮动服务费' LIMIT 1
        '''
        result = MysqlAdapter.commonExecuteGetData(sql)
        fee_rate = result[0][0] if result else None
        fee_rate_item = 'service_fee_rate' if archive_date else 'ServiceFeeRate'
        fee_rate_filter = f'and {fee_rate_item} = {fee_rate}' if fee_rate else ''
        
        # 获取票面利率基准
        sql = f'''
            select if(BondExtraBPPointValues is null, 0, t.ItemValue) as CouponBasis
            from Analysis_ProductDesignSensitivitySettings p
            left join TrustManagement_TrustBond t on p.TrustID = t.TrustId 
                and p.BondID = t.TrustBondId and ItemCode = 'CouponBasis'
            where p.TrustID = {trust_id} and AnalysisType = 1 
                and SensitivityMode='Multiple_DefaultPrepayment' and Type = 3
        '''
        result = MysqlAdapter.commonExecuteGetData(sql)
        coupon_basis = result[0][0] if result else None
        
        # 循环购买方案id
        plan_id_item = 'PlanID' if archive_date is None else 'plan_id'
        plan_id_value = '1' if revolving == '循环' else '0'
        plan_id_filter = f"and {plan_id_item} = {plan_id_value}"

        # 如果是循环产品，需要获取覆盖率
        coverage_rate_filter = ''
        if revolving == '循环':
            duration_rate_item = 'DurationRate'
            
            sql = f'''
                select ItemValue from Analysis_SolutionExtension 
                where SolutionID = {trust_id} and ItemCode = 'CoverageRate'
            '''
            if archive_date is not None:
                sql = f'''
                    select item_value from archive_analysis_solution_extension
                    where solution_id = {trust_id} and item_code = 'CoverageRate'
                        and archive_date = '{archive_date}'
                '''
                duration_rate_item = 'duration_rate'
            result = MysqlAdapter.commonExecuteGetData(sql)
            coverage_rate = result[0][0] if result else 0
            coverage_rate_filter = f"and {duration_rate_item} = {coverage_rate}"
        
        # 获取标准情景的session_id
        sql = f'''
            select TaskSessionID as session_id from Analysis_ProductDesignAnalysisResult
            where TrustID = {trust_id} and CDR = 1 and CPR = 1 and Coupon = {coupon_basis} and Premium = 0
                {fee_rate_filter} {coverage_rate_filter} {plan_id_filter}
        '''
        if archive_date is not None:
            sql = f'''
                select task_session_id as session_id from archive_product_design_analysis_result
                where trust_id = {trust_id} and cpr = 1 and cdr = 1 and coupon = {coupon_basis} and premium = 0
                    {fee_rate_filter} {coverage_rate_filter} {plan_id_filter}
                    and archive_date = '{archive_date}'
            '''
        log.info(f'获取标准情景sql: {sql}')
        result = MysqlAdapter.commonExecuteGetData(sql)
        session_id = result[0][0] if result else None
         
        if session_id:
            log.info(f"获取到产品 {trust_id} 的标准情景session_id: {session_id}")
        else:
            log.warning(f"未找到产品 {trust_id} 的标准情景session_id, sql: {sql}")
            log.warning('将获取情景id=0的session_id')
            session_id = get_session_id_scenario_zero(trust_id, archive_date)
            
        return session_id
    except Exception as e:
        log.error(f"获取标准情景session_id失败: {e}, sql: {sql}", exc_info=True)
        return None

# 如果直接运行此脚本，则显示使用说明
if __name__ == "__main__":
    print("此模块提供获取标准情景session_id的功能，可被其他脚本引用")
    print("主要函数:")
    print("  get_standard_session_id(trust_id, revolving, archive_date=None)")
