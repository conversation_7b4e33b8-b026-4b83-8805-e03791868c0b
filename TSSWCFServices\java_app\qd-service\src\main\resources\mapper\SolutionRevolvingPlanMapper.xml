<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.goldenstand.services.mapper.SolutionRevolvingPlanMapper">

    <select id="getStructurePeriodsData" resultType="java.util.Map">
        select AccountNo,
               RevolvingPeriodFrom,
               RevolvingPeriodTo,
               LoanTerm,
               LoanRemainingTerm,
               InterestRate,
               PaymentTypeCode,
               ifnull(ia.ItemAliasValue, i.ItemValue) as PaymentType,
               Percentage,
               ShortName,
               ifnull(RevolvingCount, 0)              as RevolvingCount
        from Analysis_SolutionRevolvingPlan p
                 left join Analysis_Item i on p.PaymentTypeCode = i.ItemCode and i.CategoryId = 14
                 left join Analysis_ItemAlias ia on i.ItemId = ia.ItemId
                 left join (select RevolvingPeriod, count(*) as RevolvingCount
                            from Analysis_RevolvingAnalysisConfig
                            where TrustID = #{trustId}
                            group by RevolvingPeriod) c on p.RevolvingPeriodFrom = c.RevolvingPeriod
        where p.TrustID = #{trustId}
        order by RevolvingPeriodFrom, LoanRemainingTerm desc, Percentage desc;
    </select>
    <select id="getPaymentTypeOptions" resultType="java.util.Map">
        select i.ItemCode as PaymentTypeCode, ifnull(ia.ItemAliasValue, i.ItemValue) as PaymentType
        from Analysis_Item i
                 left join Analysis_ItemAlias ia
                           on i.ItemId = ia.ItemId
        where i.CategoryId = 14
        order by i.ItemId;
    </select>
    <select id="getTemplateOptions" resultType="java.util.Map">
        select TemplateID, TemplateName
        from Analysis_TemplateDefinition
        where TemplateTypeCode = 'TrustRevolvingPool'
    </select>
    <select id="getSolutionSettingCmb" resultType="java.util.Map">
        select TrustID, TrustType, ReturnRate, InterestStartDay, InterestStartDay
        from Analysis_SolutionROISetting_CMB
        where TrustID = #{trustId};
    </select>
    <select id="getConditionOptions" resultType="java.util.Map">
        select N'OccurrenceDate' as ItemCode, N'日期' as ItemText, N'date' as ItemDataType
        union
        select N'BondBalance' as ItemCode, N'总债券余额' as ItemText, N'float' as ItemDataType
        union
        select N'PoolBalance' as ItemCode, N'资产池余额' as ItemText, N'float' as ItemDataType
        union
        select N'PoolBalance_Performing' as ItemCode, N'资产池余额(不含违约)' as ItemText, N'float' as ItemDataType;
    </select>
    <select id="getScenarios" resultType="java.util.Map">
        select ID, ScenarioName, ScenarioDescription
        from Analysis_BondRepurchaseSetting
        where TrustID = #{trustId};
    </select>
    <select id="getContainersOne" resultType="java.util.Map">
        select bs.ID as ScenarioID
             , ct.ContainerID
             , ct.ContainerType
             , ct.ContainerDesc
             , ct.PContainerID
        from Analysis_BondRepurchaseSetting bs
                 join Analysis_CriteriaContainer ct
                      on ct.BusinessType = N'BondRepurchase' and ct.BusinessID = bs.ID
        where bs.TrustID = #{trustId}
        order by bs.ID, ContainerID;
    </select>
    <select id="getContainersTwo" resultType="java.util.Map">
        select bs.ID as ScenarioID
             , cd.ContainerID
             , cd.ConditionID
             , cd.ConditionItem
             , cd.Operator
             , cd.Value
        from Analysis_BondRepurchaseSetting bs
                 join Analysis_CriteriaContainer ct
                      on ct.BusinessType = N'BondRepurchase' and ct.BusinessID = bs.ID
                 join Analysis_CriteriaCondition cd
                      on cd.BusinessType = ct.BusinessType and cd.BusinessID = ct.BusinessID and
                         cd.ContainerID = ct.ContainerID
        where bs.TrustID = #{trustId}
        order by bs.ID, cd.ContainerID, cd.ConditionID
    </select>
    <select id="getExtSetting" resultType="java.util.Map">
        select Id, TrustID, CalculateBasis, DiscountRate, SpecifiedCallDiscount, 
            CallAvailableAmount, CallPriceActual, M0, M1, M2, M3, M4, M5, M6, M6plus, 
            TriggerNextMonth, is_calculated as IsCalculated
        from Analysis_BondRepurchaseSettingExt
        where TrustID = #{trustId}
    </select>
    <select id="IsTopUpAvailable" resultType="java.lang.Boolean">
        select IsTopUpAvailable
        from TrustManagement_Trust
        where TrustID = #{trustId};
    </select>
    <select id="getPaymentSequenceItems" resultType="java.util.Map">
        select ScenarioId as SequenceID, Name as SequenceDesc
        from TrustManagement_tblTrustPaymentScenario
        where TrustId = #{trustId}
        order by ScenarioId;
    </select>
    <select id="getEventItems" resultType="java.util.Map">
        select *
        from dbo_EventPaymentSequence
        where TrustID = #{trustId}
        order by PriorityLevel;
    </select>
    <select id="getStructurePremiumSettings" resultType="java.util.Map">
        select TrustBondID, ItemValue as BondName
        from TrustManagement_TrustBond
        where TrustId = #{trustId}
          and ItemCode = 'ShortName';
    </select>
    <select id="getTranche" resultType="java.lang.String">
        select Tranche
        from Analysis_SolutionBondPremiumSettings
        where TrustID = #{trustId};
    </select>
    <select id="getBondNames" resultType="java.util.Map">
        select a.*, ItemValue as BondName
        from Analysis_SolutionBondPremiumSettings a
                 inner join TrustManagement_TrustBond b
                            on a.Tranche = b.TrustBondId and a.TrustID = b.TrustId
        where a.TrustID = #{trustId}
          and b.ItemCode = 'ShortName';
    </select>
    <select id="getBondNamesOther" resultType="java.util.Map">
        select *
        from Analysis_SolutionBondPremiumSettings
        where TrustID = #{trustId};
    </select>
    <select id="getTrustBondExtraBpSettings" resultType="java.util.Map">
        select *
        from Analysis_SolutionBondExtraBPSettings
        where TrustID = #{trustId}
    </select>
    <select id="getBondsFloatBPDatas" resultType="java.util.Map">
        select *
        from Analysis_SolutionBondFloatRateSettings
        where TrustID = #{trustId};
    </select>
    <select id="getTrustFloatDatas" resultType="java.util.Map">
        select *
        from Analysis_FloatingRate
        where TrustID = #{trustId};
    </select>
    <select id="getSolutionSetting" resultType="java.util.Map">
        select * from Analysis_SolutionROISetting
        where TrustID=#{trustId};
    </select>

    <select id="getMobPriceSummary" resultType="cn.goldenstand.services.entity.drb.MobPriceSummaryDTO">
        with cte as (
            select * from analysis_grouped_mob_price_summary
            where trust_id = #{trustId} and session_id = #{sessionId} and scenario_id = #{scenarioId}
        )
        select repurchase_calculation_date as repurchaseCalculationDate, 'M0' as assetCategory, m0 as assetBalance,
            price_m0 as repurchasePrice, pricing_m0 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M1' as assetCategory, m1 as assetBalance,
            price_m1 as repurchasePrice, pricing_m1 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M2' as assetCategory, m2 as assetBalance,
            price_m2 as repurchasePrice, pricing_m2 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M3' as assetCategory, m3 as assetBalance,
            price_m3 as repurchasePrice, pricing_m3 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M4' as assetCategory, m4 as assetBalance,
            price_m4 as repurchasePrice, pricing_m4 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M5' as assetCategory, m5 as assetBalance,
            price_m5 as repurchasePrice, pricing_m5 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M6' as assetCategory, m6 as assetBalance,
            price_m6 as repurchasePrice, pricing_m6 as discountRate
        from cte
        union all
        select repurchase_calculation_date as repurchaseCalculationDate, 'M6+' as assetCategory, m6plus as assetBalance,
            price_m6plus as repurchasePrice, pricing_m6plus as discountRate
        from cte
    </select>

    <select id="getMobPriceExpanded" resultType="cn.goldenstand.services.entity.drb.MobPriceExpandedDTO">
        select e.*, t.ItemValue as productNo
        from analysis_grouped_mob_price_expanded as e
        left join TrustManagement_TrustInfoExtension as t on e.trust_id = t.TrustId
            and t.ItemCode = 'ProductNo'
        where e.trust_id = #{trustId} and session_id = #{sessionId} and scenario_id = #{scenarioId}
            and pay_date = #{payDate};
    </select>
</mapper>
