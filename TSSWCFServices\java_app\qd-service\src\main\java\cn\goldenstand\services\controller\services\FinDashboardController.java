/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.controller.services;

import cn.goldenstand.services.constant.ResultInfo;
import cn.goldenstand.services.entity.drb.BondBalanceDTO;
import cn.goldenstand.services.entity.drb.BondBalanceQueryDTO;
import cn.goldenstand.services.entity.drb.TrustMetricDTO;
import cn.goldenstand.services.service.IFinDashboardService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.IOException;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "融资仪表盘接口")
@RestController
@RequestMapping("/Services/FinDashboard")
public class FinDashboardController {

    @Autowired
    private IFinDashboardService finDashboardService;

    /**
     * 获取报告日期列表
     *
     * @return 报告日期列表
     */
    @ApiOperation("获取报告日期列表")
    @GetMapping("/getReportingDates")
    public ResultInfo getReportingDates() {
        return finDashboardService.getReportingDates();
    }

    /**
     * 获取券端余额信息
     *
     * @param queryDTO 查询参数
     * @return 券端余额信息
     */
    @ApiOperation("获取券端余额信息")
    @PostMapping("/getBondBalancePage")
    public ResultInfo getBondBalancePage(@RequestBody BondBalanceQueryDTO queryDTO) {
        Page<BondBalanceDTO> page = new Page<>();
        page.setSize(queryDTO.getPageSize());
        page.setCurrent(queryDTO.getPageNum());
        return finDashboardService.getBondBalancePage(page, queryDTO);
    }

    /**
     * 根据报告日期获取各个层级剩余本金
     *
     * @param reportingDate 报告日期
     * @return 各个层级剩余本金
     */
    @ApiOperation("根据报告日期获取各个层级剩余本金")
    @GetMapping("/getBalanceByReportingDate")
    public ResultInfo getBalanceByReportingDate(
            @ApiParam(value = "报告日期", required = true) @RequestParam String reportingDate) {
        return finDashboardService.getBalanceByReportingDate(reportingDate);
    }

    /**
     * 根据报告日期获取券端自持剩余本金
     *
     * @param reportingDate 报告日期
     * @return 券端自持剩余本金
     */
    @ApiOperation("根据报告日期获取券端自持剩余本金")
    @GetMapping("/getSelfHoldingCpbByReportingDate")
    public ResultInfo getSelfHoldingCpbByReportingDate(
            @ApiParam(value = "报告日期", required = true) @RequestParam String reportingDate) {
        return finDashboardService.getSelfHoldingCpbByReportingDate(reportingDate);
    }

    /**
     * 根据报告日期获取融资项目数量总览
     *
     * @param reportingDate 报告日期
     * @return 融资项目数量总览
     */
    @ApiOperation("根据报告日期获取融资项目数量总览")
    @GetMapping("/getTrustCountByReportingDate")
    public ResultInfo getTrustCountByReportingDate(
            @ApiParam(value = "报告日期", required = true) @RequestParam String reportingDate) {
        return finDashboardService.getTrustCountByReportingDate(reportingDate);
    }

    /**
     * 获取资产端剩余本金信息
     *
     * @param queryDTO 查询参数
     * @return 资产端剩余本金信息
     */
    @ApiOperation("获取资产端剩余本金信息")
    @PostMapping("/getAssetRemainingPrincipal")
    public ResultInfo getAssetRemainingPrincipal(@RequestBody BondBalanceQueryDTO queryDTO) {
        Page<Map<String, Object>> page = new Page<>();
        page.setSize(queryDTO.getPageSize());
        page.setCurrent(queryDTO.getPageNum());
        return finDashboardService.getAssetRemainingPrincipal(page, queryDTO);
    }

    /**
     * 获取存续产品指标
     *
     * @param queryDTO 查询参数
     * @return 存续产品指标信息
     */
    @ApiOperation("获取存续产品指标")
    @PostMapping("/getTrustMetricPage")
    public ResultInfo getTrustMetricPage(@RequestBody BondBalanceQueryDTO queryDTO) {
        Page<TrustMetricDTO> page = new Page<>();
        page.setSize(queryDTO.getPageSize());
        page.setCurrent(queryDTO.getPageNum());
        return finDashboardService.getTrustMetricPage(page, queryDTO);
    }

    /**
     * 导出存续产品指标
     *
     * @param reportingDate 报告日期
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @ApiOperation("导出存续产品指标")
    @GetMapping("/exportTrustMetric")
    public void exportTrustMetric(
            @ApiParam(value = "报告日期", required = true) @RequestParam String reportingDate,
            HttpServletResponse response) throws IOException {
        finDashboardService.exportTrustMetric(reportingDate, response);
    }
}
