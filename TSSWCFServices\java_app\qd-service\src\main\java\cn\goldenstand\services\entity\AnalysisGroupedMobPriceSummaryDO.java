/*
 * Copyright (c) 2024 Sankuai, Inc. All Rights Reserved.
 */
package cn.goldenstand.services.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 回购价格汇总表
 */
@Data
@TableName("analysis_grouped_mob_price_summary")
public class AnalysisGroupedMobPriceSummaryDO {

    /**
     * 自增id
     */
    @TableId
    private Long id;

    /**
     * 产品ID
     */
    @TableField("trust_id")
    private Integer trustId;

    /**
     * 会话ID
     */
    @TableField("session_id")
    private String sessionId;

    /**
     * 情景ID
     */
    @TableField("scenario_id")
    private Long scenarioId;

    /**
     * 情景描述
     */
    @TableField("scenario_desc")
    private String scenarioDesc;

    /**
     * 回购起算日
     */
    @TableField("repurchase_calculation_date")
    private String repurchaseCalculationDate;

    /**
     * 模拟起始日
     */
    @TableField("simulation_start_date")
    private String simulationStartDate;

    /**
     * M0
     */
    @TableField("m0")
    private BigDecimal m0;

    /**
     * M1
     */
    @TableField("m1")
    private BigDecimal m1;

    /**
     * M2
     */
    @TableField("m2")
    private BigDecimal m2;

    /**
     * M3
     */
    @TableField("m3")
    private BigDecimal m3;

    /**
     * M4
     */
    @TableField("m4")
    private BigDecimal m4;

    /**
     * M5
     */
    @TableField("m5")
    private BigDecimal m5;

    /**
     * M6
     */
    @TableField("m6")
    private BigDecimal m6;

    /**
     * M6plus
     */
    @TableField("m6plus")
    private BigDecimal m6plus;

    /**
     * pricing_m0
     */
    @TableField("pricing_m0")
    private BigDecimal pricingM0;

    /**
     * pricing_m1
     */
    @TableField("pricing_m1")
    private BigDecimal pricingM1;

    /**
     * pricing_m2
     */
    @TableField("pricing_m2")
    private BigDecimal pricingM2;

    /**
     * pricing_m3
     */
    @TableField("pricing_m3")
    private BigDecimal pricingM3;

    /**
     * pricing_m4
     */
    @TableField("pricing_m4")
    private BigDecimal pricingM4;

    /**
     * pricing_m5
     */
    @TableField("pricing_m5")
    private BigDecimal pricingM5;

    /**
     * pricing_m6
     */
    @TableField("pricing_m6")
    private BigDecimal pricingM6;

    /**
     * pricing_m6plus
     */
    @TableField("pricing_m6plus")
    private BigDecimal pricingM6plus;

    /**
     * price_m0
     */
    @TableField("price_m0")
    private BigDecimal priceM0;

    /**
     * price_m1
     */
    @TableField("price_m1")
    private BigDecimal priceM1;

    /**
     * price_m2
     */
    @TableField("price_m2")
    private BigDecimal priceM2;

    /**
     * price_m3
     */
    @TableField("price_m3")
    private BigDecimal priceM3;

    /**
     * price_m4
     */
    @TableField("price_m4")
    private BigDecimal priceM4;

    /**
     * price_m5
     */
    @TableField("price_m5")
    private BigDecimal priceM5;

    /**
     * price_m6
     */
    @TableField("price_m6")
    private BigDecimal priceM6;

    /**
     * price_m6plus
     */
    @TableField("price_m6plus")
    private BigDecimal priceM6plus;
}
